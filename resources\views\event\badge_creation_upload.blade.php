<div class="row mt-5" id="uploadDesign">
    <!-- Upload Section -->
    <div class="col-md-4"  style="">
        <p class="fw-bolder">Upload</p>
        <div id="uploadButton" style="cursor:pointer;"
             class="upload-container mt-2 d-flex justify-content-center align-items-center">
            <div class="text-center">
                <i class="bi bi-cloud-upload display-1 text-primary"></i>

                <button class="mb-4 border border-0 bg-transparent">
                    <img src="{{ asset('images/upload.png') }}" />
                </button>

                <p class="mb-5">ag Files to Upload</p>
                <p class="fw-bolder">Browse File</p>
            </div>
        </div>
        <p class="mt-2">Minimum image size: 575x850 px<br></p>
    </div>

    <!-- Preview Section -->
    <div class="col-md-4">
        <p class="fw-bolder">Preview</p>
        <div id="preview-container-upload"
             class="preview-container position-relative d-flex flex-column align-items-center text-center mt-2">
            <img src="{{ asset('images/bd-logo.png') }}" class="upload_preview_logo_area" style="width: 120px"/>
            {{--<p class="speaker mt-3">Speaker</p>--}}
                <div class="upload_logo_container" style="max-width: 325px;">
                {{--<h3 class="first_name fw-bolder">JOHN</h3>
                <h4 class="last_name fw-bolder">DOE</h4>
                <p class="company_name fw-bolder">Country</p>--}}

                <h2 class="fw-semibold upload_full_name" style="color: #0E599A">Name Surname</h2>
                <p class="fw-semibold upload_specialty" style="color: #F0682F">Specialty / Job Title</p>
                <br>
                <h3 class="fw-semibold upload_event_name" style="color:#0E599A;max-width:325px;">
                    Name of Event
                </h3>
                <br>
                <p class="fw-semibold upload_company" style="color: #F0682F">Hospital / Organization</p>
                <p class="fw-semibold upload_country" style="color: #0E599A">Country</p>
            </div>

            {{--<div class="qr_container d-flex justify-content-center align-items-center mt-3">
                <img src="qr_placeholder.png" alt="QR Code" class="qr-image">
            </div>--}}
        </div>
    </div>

    <!-- Form Section -->
    <div class="col-md-4">
        <div class="form-container">
            <form method="post" id="uploadBgFrom" action="{{ route('panel.png_badge_generate', ['event_id' => $event->id]) }}" enctype="multipart/form-data">
                @csrf
                <input type="file" id="badge_bg_file" name="badge_bg_file" style="display: none;" accept=".png">

                @isset($register)
                    {{--<input type="hidden" id="upload_badge_qr_code" name="upload_badge_qr_code" value="{{ $register->qr_code }}">--}}
                    <input type="hidden" id="register_id" name="register_id" value="{{ $register->id }}">
                @endisset

                {{-- Name --}}
                <div class="d-flex justify-content-between">
                    <label for="name" class="form-label">Name</label>
                </div>
                <div class="mb-3">
                    <input type="text" class="form-control" id="upload_first_name" name="first_name"
                           value="@isset($register){{ $register->first_name }}@endisset"
                           placeholder="Name" required>
                </div>

                {{-- Surname --}}
                <div class="d-flex justify-content-between">
                    <label for="surname" class="form-label">Surname</label>
                </div>
                <div class="mb-3">
                    <input type="text" class="form-control" id="upload_last_name" name="last_name"
                           value="@isset($register){{ $register->last_name }}@endisset"
                           placeholder="Surname" required>
                </div>

                {{-- Specialty --}}
                <div class="d-flex justify-content-between">
                    <label for="surname" class="form-label">Specialty / Job Title</label>
                </div>
                <div class="mb-3">
                    <input type="text" class="form-control" id="upload_specialty" name="specialty"
                           placeholder="Specialty / Job Title" value="@isset($register){{ $register->title }}@endisset">
                </div>


                {{-- Organization / Company / Department --}}
                <div class="d-flex justify-content-between">
                    <label for="surname" class="form-label">Organization / Company</label>
                </div>
                <div class="mb-3">
                    <input type="text" class="form-control" id="upload_company" name="company"
                           placeholder="Company" value="@isset($register){{ $register->company }}@endisset">
                </div>

                {{-- Country --}}
                <div class="d-flex justify-content-between">
                    <label for="country" class="form-label">Country</label>
                </div>
                <div class="mb-3">
                    <input type="text" class="form-control" id="upload_country" name="country"
                           value="@isset($register){{ $register->country }}@endisset"
                           placeholder="Country">
                </div>

                {{-- Event Name --}}
                <div class="d-flex justify-content-between">
                    <label for="surname" class="form-label">Event Name</label>
                </div>
                <div class="mb-3">
                    <input type="text" class="form-control" id="upload_event_name" name="event_name"
                           placeholder="Event Name" value="{{ isset($event) ? $event->event_title : "" }}" required>
                </div>

                <input type="hidden" name="email" value="@isset($register){{ $register->email }}@endisset">
                <input type="hidden" name="user_id" value="@isset($register){{ $register->user_id }}@endisset">

                <div class="d-flex justify-content-end mt-4">
                    <button type="submit" class="badge_create_save_btn w-full-custom">Generate Badge</button>
                </div>
            </form>
        </div>
    </div>
</div>
