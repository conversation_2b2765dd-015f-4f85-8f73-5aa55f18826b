<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BD | Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
</head>

<body class="dashboard-body">
    <!-- Header -->
    <header class="dashboard-header">
        <div class="container px-3 py-2">
            <div class="d-flex justify-content-between align-items-center">
                <!-- Left side - Logo and Navigation -->
                <div class="d-flex align-items-center">
                    <div class="d-flex align-items-center me-4">
                        <img src="assets/images/bd-logo.png" alt="BD Logo" class="logo-image me-2">
                        <h4 class="logo-text-small mb-0 d-none d-lg-block">BD</h4>
                    </div>
                    <!-- Desktop Navigation -->
                    <nav class="d-none d-lg-flex">
                        <a href="travel-documents.html" class="nav-link me-4">Travel Documents</a>
                        <a href="dashboard.html" class="nav-link active">My Events</a>
                    </nav>
                </div>

                <!-- Right side - User Profile and Menu -->
                <div class="d-flex align-items-center">
                    <!-- Desktop User Profile -->
                    <div class="d-none d-lg-flex align-items-center">
                        <div class="text-end me-3">
                            <h6 class="user-name-desktop mb-0">Dr. John Simmons</h6>
                            <small class="user-title-desktop">Title placeholder</small>
                        </div>
                        <img src="assets/images/user.png" alt="Profile" class="profile-image-desktop">
                    </div>

                    <!-- Mobile Menu Button -->
                    <button class="btn btn-link p-0 text-dark d-lg-none" id="menuToggle">
                        <i class="bi bi-list fs-4"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Side Menu Overlay -->
    <div class="side-menu-overlay" id="sideMenuOverlay"></div>

    <!-- Side Menu -->
    <div class="side-menu" id="sideMenu">
        <div class="side-menu-header">
            <div class="side-menu-logo">
                <img src="assets/images/bd-logo.png" alt="BD Logo" class="logo-image">
            </div>
            <button class="side-menu-close" id="sideMenuClose">
                <i class="bi bi-x"></i>
            </button>
        </div>

        <div class="side-menu-profile">
            <img src="assets/images/user.png" alt="Profile" class="side-menu-profile-image">
            <div class="side-menu-profile-info">
                <h5>Dr. John Simmons</h5>
                <small>Title placeholder</small>
            </div>
        </div>

        <nav class="side-menu-nav">
            <a href="travel-documents.html" class="side-menu-item">Travel Documents</a>
            <a href="dashboard.html" class="side-menu-item">My Events</a>
        </nav>
    </div>

    <!-- Welcome Section -->
    <div class="container px-3 py-3">

        <!-- User Profile -->
        <div class="d-flex align-items-center mb-4">
            <img src="assets/images/user.png" alt="Profile" class="profile-image me-3">
            <div>
                <h6 class="user-name mb-0">Dr. John Simmons</h6>
                <small class="user-title text-muted">Title placeholder</small>
            </div>
        </div>

        <!-- Tabs -->
        <div class="event-tabs mb-4">
            <button class="btn tab-btn active me-2" data-tab="my-events">My Events</button>
            <button class="btn tab-btn" data-tab="other-events">Other Events</button>
        </div>

        <!-- Events List -->
        <div class="events-container" id="events-container">
            <!-- My Events Content -->
            <div class="tab-content active" id="my-events">
                <!-- Event Card 1 -->
                <div class="event-card mb-3">
                    <div class="event-image-container">
                        <img src="assets/images/event.png" alt="Event Image" class="event-image">
                        <span class="registered-badge">Registered</span>
                    </div>
                    <div class="event-content">
                        <h6 class="event-title">APAC Hospital at Home Symposium</h6>
                        <div class="event-details">
                            <small class="text-muted">TODAY | 12:00PM-04:00PM</small><br>
                            <small class="text-muted">Auditorium, National Universi...</small>
                        </div>
                    </div>
                </div>

                <!-- Event Card 2 -->
                <div class="event-card mb-3">
                    <div class="event-image-container">
                        <img src="assets/images/event.png" alt="Event Image" class="event-image">
                        <span class="registered-badge">Registered</span>
                    </div>
                    <div class="event-content">
                        <h6 class="event-title">APAC Hospital at Home Symposium</h6>
                        <div class="event-details">
                            <small class="text-muted">TODAY | 12:00PM-04:00PM</small><br>
                            <small class="text-muted">Auditorium, National Universi...</small>
                        </div>
                    </div>
                </div>

                <!-- Event Card 3 -->
                <div class="event-card mb-3">
                    <div class="event-image-container">
                        <img src="assets/images/event.png" alt="Event Image" class="event-image">
                        <span class="registered-badge">Registered</span>
                    </div>
                    <div class="event-content">
                        <h6 class="event-title">APAC Hospital at Home Symposium</h6>
                        <div class="event-details">
                            <small class="text-muted">TODAY | 12:00PM-04:00PM</small><br>
                            <small class="text-muted">Auditorium, National Universi...</small>
                        </div>
                    </div>
                </div>

                <!-- Event Card 4 -->
                <div class="event-card mb-3">
                    <div class="event-image-container">
                        <img src="assets/images/event.png" alt="Event Image" class="event-image">
                        <span class="registered-badge">Registered</span>
                    </div>
                    <div class="event-content">
                        <h6 class="event-title">APAC Hospital at Home Symposium</h6>
                        <div class="event-details">
                            <small class="text-muted">TODAY | 12:00PM-04:00PM</small><br>
                            <small class="text-muted">Auditorium, National Universi...</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Other Events Content -->
            <div class="tab-content" id="other-events">
                <!-- Event Card 1 -->
                <div class="event-card mb-3">
                    <div class="event-image-container">
                        <img src="assets/images/event.png" alt="Event Image" class="event-image">
                    </div>
                    <div class="event-content">
                        <h6 class="event-title">Medical Innovation Conference</h6>
                        <div class="event-details">
                            <small class="text-muted">TOMORROW | 09:00AM-05:00PM</small><br>
                            <small class="text-muted">Convention Center, Downtown...</small>
                        </div>
                    </div>
                </div>

                <!-- Event Card 2 -->
                <div class="event-card mb-3">
                    <div class="event-image-container">
                        <img src="assets/images/event.png" alt="Event Image" class="event-image">
                    </div>
                    <div class="event-content">
                        <h6 class="event-title">Healthcare Technology Summit</h6>
                        <div class="event-details">
                            <small class="text-muted">NEXT WEEK | 10:00AM-03:00PM</small><br>
                            <small class="text-muted">Tech Hub, Silicon Valley...</small>
                        </div>
                    </div>
                </div>

                <!-- Event Card 3 -->
                <div class="event-card mb-3">
                    <div class="event-image-container">
                        <img src="assets/images/event.png" alt="Event Image" class="event-image">
                    </div>
                    <div class="event-content">
                        <h6 class="event-title">Healthcare Technology Summit</h6>
                        <div class="event-details">
                            <small class="text-muted">NEXT WEEK | 10:00AM-03:00PM</small><br>
                            <small class="text-muted">Tech Hub, Silicon Valley...</small>
                        </div>
                    </div>
                </div>

                <!-- Event Card 4 -->
                <div class="event-card mb-3">
                    <div class="event-image-container">
                        <img src="assets/images/event.png" alt="Event Image" class="event-image">
                    </div>
                    <div class="event-content">
                        <h6 class="event-title">Healthcare Technology Summit</h6>
                        <div class="event-details">
                            <small class="text-muted">NEXT WEEK | 10:00AM-03:00PM</small><br>
                            <small class="text-muted">Tech Hub, Silicon Valley...</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Side menu functionality
        const menuToggle = document.getElementById('menuToggle');
        const sideMenu = document.getElementById('sideMenu');
        const sideMenuOverlay = document.getElementById('sideMenuOverlay');
        const sideMenuClose = document.getElementById('sideMenuClose');

        function openSideMenu() {
            sideMenu.classList.add('show');
            sideMenuOverlay.classList.add('show');
            document.body.style.overflow = 'hidden';
        }

        function closeSideMenu() {
            sideMenu.classList.remove('show');
            sideMenuOverlay.classList.remove('show');
            document.body.style.overflow = '';
        }

        menuToggle.addEventListener('click', openSideMenu);
        sideMenuClose.addEventListener('click', closeSideMenu);
        sideMenuOverlay.addEventListener('click', closeSideMenu);

        // Tab functionality
        document.addEventListener('DOMContentLoaded', function () {
            const tabButtons = document.querySelectorAll('.tab-btn');
            const tabContents = document.querySelectorAll('.tab-content');

            tabButtons.forEach(button => {
                button.addEventListener('click', function () {
                    const targetTab = this.getAttribute('data-tab');

                    // Remove active class from all buttons
                    tabButtons.forEach(btn => btn.classList.remove('active'));

                    // Add active class to clicked button
                    this.classList.add('active');

                    // Hide all tab contents with fade out
                    tabContents.forEach(content => {
                        content.classList.remove('active');
                    });

                    // Show target tab content with fade in
                    setTimeout(() => {
                        document.getElementById(targetTab).classList.add('active');
                    }, 150);
                });
            });
        });
    </script>
</body>

</html>