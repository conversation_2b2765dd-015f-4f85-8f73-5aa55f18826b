@extends('layouts.bd-app')

@section('title', 'BD | Dashboard')

@section('content')
@include('bd.components.header', ['user' => $user ?? null])

<!-- Welcome Section -->
<div class="container px-3 py-3">

    <div class="row mb-4">
        <div class="col-12">
            <p class="welcome-text-mobile mb-0">Welcome to your BD events dashboard</p>
        </div>
    </div>

    <!-- Tabs -->
    <div class="event-tabs mb-4">
        <button class="btn tab-btn active me-2" data-tab="my-events">My Events</button>
        <button class="btn tab-btn" data-tab="other-events">Other Events</button>
    </div>

    <!-- Events List -->
    <div class="events-container" id="events-container">

        <!-- My Events Content -->
        <div class="tab-content active justify-content-start" id="my-events">
            @forelse($myEvents as $event)
                <div class="event-card mb-3" data-event-id="{{ $event->id }}">
                    <div class="event-image-container">
                        <img src="{{ $event->image }}" alt="Event Image" class="event-image">
                        @if($event->is_registered)
                            <span class="registered-badge">Registered</span>
                        @endif
                    </div>
                    <div class="event-content">
                        <h6 class="event-title">{{ $event->title }}</h6>
                        <div class="event-details">
                            <small class="text-muted">{{ $event->formatted_date }}</small><br>
                            @if(!empty($event->locations))
                                @foreach($event->locations as $location)
                                    <small class="text-muted" style="font-style: italic;">{{ $location }}</small><br>
                                @endforeach
                            @else
                                <small class="text-muted" style="font-style: italic;">Location TBA</small>
                            @endif
                        </div>
                    </div>
                </div>
            @empty
                <div class="text-center py-5">
                    <img src="{{ asset('assets/bd/images/no-events.png') }}" alt="No Events" class="mb-3" style="width: 100px; opacity: 0.5;">
                    <h6 class="text-muted">No registered events found</h6>
                    <p class="text-muted small">Check out the "Other Events" tab to find events to register for.</p>
                </div>
            @endforelse
        </div>

        <!-- Other Events Content -->
        <div class="tab-content" id="other-events">
            @forelse($otherEvents as $event)
                <div class="event-card mb-3" data-event-id="{{ $event->id }}">
                    <div class="event-image-container">
                        <img src="{{ $event->image }}" alt="Event Image" class="event-image">
                        <div class="register-overlay">
                            {{--<button class="btn btn-primary btn-sm register-btn" onclick="registerForEvent({{ $event->id }})">
                                Register Now
                            </button>--}}
                        </div>
                    </div>
                    <div class="event-content">
                        <h6 class="event-title">{{ $event->title }}</h6>
                        <div class="event-details">
                            <small class="text-muted">{{ $event->formatted_date }}</small><br>
                            @if(!empty($event->locations))
                                @foreach($event->locations as $location)
                                    <small class="text-muted" style="font-style: italic;">{{ $location }}</small><br>
                                @endforeach
                            @else
                                <small class="text-muted" style="font-style: italic;">Location TBA</small>
                            @endif
                        </div>
                    </div>
                </div>
            @empty
                <div class="text-center py-5" style="width: 100%;">
                    <h6 class="text-muted">No other events available</h6>
                    <p class="text-muted small">All available events are already in your registered events.</p>
                </div>
            @endforelse
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Tab switching functionality
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');

    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');

            // Remove active class from all buttons and contents
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));

            // Add active class to clicked button and corresponding content
            this.classList.add('active');
            document.getElementById(targetTab).classList.add('active');
        });
    });

    // Event card click handler
    const eventDivCards = document.querySelectorAll('.event-card');
    eventDivCards.forEach(card => {
        card.addEventListener('click', function(e) {
            // Don't navigate if register button was clicked
            if (e.target.classList.contains('register-btn') || e.target.closest('.register-btn')) {
                return;
            }

            const eventId = this.getAttribute('data-event-id');
            if (eventId) {
                window.location.href = `/event/${eventId}`;
            } else {
                window.location.href = '{{ route("bd.event-detail") }}';
            }
        });
    });

});

// Register for event function
function registerForEvent(eventId) {
    // Show loading state
    const button = event.target;
    const originalText = button.textContent;
    button.textContent = 'Registering...';
    button.disabled = true;

    // Here you would typically make an AJAX call to register
    // For now, we'll just simulate it
    setTimeout(() => {
        alert('Registration functionality will be implemented here');
        button.textContent = originalText;
        button.disabled = false;
    }, 1000);
}
</script>

<style>
.register-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.event-image-container:hover .register-overlay {
    opacity: 1;
}

.register-btn {
    background-color: #007bff !important;
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 500;
    transform: translateY(10px);
    transition: transform 0.3s ease;
}

.event-image-container:hover .register-btn {
    transform: translateY(0);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Desktop layout for events */
@media screen and (min-width: 1024px) {
    .events-container {
        overflow-y: unset;
    }

    .tab-content {
        display: none;
        justify-content: space-between;
        gap: 15px;
        flex-wrap: wrap;
    }

    .tab-content.active {
        display: flex;
    }

    /* Other Events - 4 columns with equal spacing */
    #other-events.active {
        justify-content: flex-start;
    }

    #other-events.active .event-card {
        flex: 0 0 calc(25% - 11.25px);
        margin-bottom: 15px;
    }
}

/* Event image consistent height */
.event-image {
    width: 300px;
    height: 150px;
    object-fit: cover;
}

.tab-btn {
    background: transparent;
    padding: 8px 16px;
    transition: all 0.3s ease;
}

</style>
@endpush
