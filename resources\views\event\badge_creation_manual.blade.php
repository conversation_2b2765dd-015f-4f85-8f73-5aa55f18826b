@extends('layouts.app')
@section('content')

<div class="checkout">
    <div class="card shadow-none border">
        <div class="card-body p-4">
            @include('layouts.event_details')
            @include('layouts.page_navigation')

            <div class="row mt-5">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                Create Badge for Non-Listed User
                            </h5>
                            <p class="text-muted mb-0 mt-2">
                                This section allows you to generate a badge for users outside of the Registration list/ Shortlist
                            </p>
                        </div>
                        <div class="card-body">
                            <form action="{{ route('panel.badge_create_manual', ['event_id' => $event->id]) }}" method="POST">
                                @csrf

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="first_name" class="form-label">
                                                First Name <span class="text-danger">*</span>
                                            </label>
                                            <input type="text" class="form-control @error('first_name') is-invalid @enderror"
                                                   id="first_name" name="first_name" value="{{ old('first_name') }}" required>
                                            @error('first_name')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="last_name" class="form-label">
                                                Last Name <span class="text-danger">*</span>
                                            </label>
                                            <input type="text" class="form-control @error('last_name') is-invalid @enderror"
                                                   id="last_name" name="last_name" value="{{ old('last_name') }}" required>
                                            @error('last_name')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="email" class="form-label">
                                                Email <span class="text-danger">*</span>
                                            </label>
                                            <input type="email" class="form-control @error('email') is-invalid @enderror"
                                                   id="email" name="email" value="{{ old('email') }}" required>
                                            @error('email')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="phone" class="form-label">Phone</label>
                                            <input type="text" class="form-control @error('phone') is-invalid @enderror"
                                                   id="phone" name="phone" value="{{ old('phone') }}">
                                            @error('phone')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="company" class="form-label">Company</label>
                                            <input type="text" class="form-control @error('company') is-invalid @enderror"
                                                   id="company" name="company" value="{{ old('company') }}">
                                            @error('company')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="title" class="form-label">Job Title</label>
                                            <input type="text" class="form-control @error('title') is-invalid @enderror"
                                                   id="title" name="title" value="{{ old('title') }}">
                                            @error('title')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="country" class="form-label">Country</label>
                                            <input type="text" class="form-control @error('country') is-invalid @enderror"
                                                   id="country" name="country" value="{{ old('country') }}">
                                            @error('country')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="template_id" class="form-label">Badge Template</label>
                                            <select class="form-control @error('template_id') is-invalid @enderror"
                                                    id="template_id" name="template_id">
                                                <option value="1" selected>Default Template</option>
                                            </select>
                                            @error('template_id')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group mb-3">
                                    <div class="alert alert-info">
                                        <i class="ti ti-info-circle"></i>
                                        <strong>Note:</strong> This person will be added as an external participant (status: manual)
                                        and will automatically get a QR code (based on email address) and badge generated.
                                    </div>
                                </div>

                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-success">
                                        <i class="ti ti-check"></i> Create Badge
                                    </button>
                                    <a href="{{ route('panel.badge_list', ['event_id' => $event->id]) }}" class="btn btn-secondary">
                                        <i class="ti ti-arrow-left"></i> Back to Badge List
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">Badge Preview</h6>
                        </div>
                        <div class="card-body text-center">
                            <div class="badge-preview-container">
                                <img src="{{ asset('images/default/bg-light.png') }}"
                                     alt="Badge Preview" class="img-fluid" style="max-width: 200px;">
                                <p class="text-muted mt-2">Preview will show here</p>
                            </div>
                        </div>
                    </div>

                    <div class="card mt-3">
                        <div class="card-header">
                            <h6 class="card-title mb-0">Manual Badge Info</h6>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li><i class="ti ti-check text-success"></i> Auto QR code generation (email-based)</li>
                                <li><i class="ti ti-check text-success"></i> Instant badge creation</li>
                                <li><i class="ti ti-check text-success"></i> External participant status</li>
                                <li><i class="ti ti-check text-success"></i> Full event access</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection

@push('css')
<style>
    .badge-preview-container {
        border: 2px dashed #dee2e6;
        border-radius: 8px;
        padding: 20px;
        background-color: #f8f9fa;
    }

    .form-label {
        font-weight: 600;
        color: #495057;
    }

    .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
    }
</style>
@endpush

@push('js')
<script>
    // Form preview functionality
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.querySelector('form');
        const previewContainer = document.querySelector('.badge-preview-container p');

        // Form değişikliklerini dinle
        form.addEventListener('input', function() {
            const firstName = document.getElementById('first_name').value;
            const lastName = document.getElementById('last_name').value;
            const company = document.getElementById('company').value;
            const email = document.getElementById('email').value;

            if (firstName || lastName) {
                const fullName = `${firstName} ${lastName}`.trim();
                previewContainer.innerHTML = `
                    <strong>${fullName}</strong><br>
                    ${company ? `<small>${company}</small><br>` : ''}
                    ${email ? `<small class="text-info">QR: ${email}</small><br>` : ''}
                    <small class="text-muted">Manual Badge Preview</small>
                `;
            } else {
                previewContainer.innerHTML = 'Preview will show here';
            }
        });
    });
</script>
@endpush
