<?php

namespace App\Http\Controllers\BD;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use App\Models\Register;
use App\Models\Event;

class QrController extends Controller
{
    /**
     * Show My QR page
     */
    public function index()
    {
        $user = Session::get('bd_user');

        if (!$user) {
            return redirect()->route('bd.login');
        }

        // Get user's first registration to get user_id for QR
        $registration = Register::where('email', $user->email)->first();

        $qrData = null;
        if ($registration) {
            // Check if QR code image file exists
            $qrImagePath = public_path("qr/{$registration->user_id}.png");

            if (file_exists($qrImagePath)) {
                $qrData = (object) [
                    'user_id' => $registration->user_id,
                    'qr_code_url' => url("/qr/{$registration->user_id}.png"),
                    'user_name' => $user->name,
                    'user_email' => $user->email
                ];
            }
        }

        return view('bd.my-qr', compact('user', 'qrData'));
    }
}
