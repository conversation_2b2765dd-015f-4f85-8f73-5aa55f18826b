@extends('layouts.bd-app')

@section('title', 'BD | Travel Documents')
@section('body-class', 'travel-documents-body')

@section('content')
@include('bd.components.header', ['user' => $user ?? null])

<div class="container-fluid px-4 py-4">
    <!-- Event Selection -->
    <div class="event-selection-container">
        <h6 class="select-event-title mb-3">Please select the event</h6>

        <!-- Dropdown Button -->
        <div class="dropdown mb-3">
            <button class="btn event-dropdown-btn w-100 d-flex justify-content-between align-items-center" type="button" id="eventDropdown">
                <span class="dropdown-text">Select Event</span>
                <i class="bi bi-chevron-down"></i>
            </button>

            <!-- Dropdown Menu -->
            <div class="dropdown-menu w-100 event-dropdown-menu" id="dropdownMenu">
                @forelse($events as $event)
                    <div class="event-option {{ $loop->first ? 'active' : '' }}"
                         data-event="{{ $event->title }}"
                         data-event-id="{{ $event->id }}">
                        {{ Str::limit($event->display_text, 40) }}
                    </div>
                @empty
                    <div class="no-events-message text-center py-3">
                        <p class="text-muted mb-0">No registered events found</p>
                        <small class="text-muted">Please register for an event first</small>
                    </div>
                @endforelse
            </div>
        </div>

        <!-- Event Details Section (Hidden by default) -->
        <div class="event-details-section d-none" id="eventDetailsSection">
            <!-- Event Image -->
            <div class="event-image-container mb-3">
                <img src="{{ $selectedEvent->image ?? asset('assets/bd/images/event.png') }}" alt="Event" class="travel-event-image">
            </div>

            <!-- Event Info -->
            <div class="event-info mb-3">
                <h6 class="event-title mb-1" id="selectedEventTitle">Event</h6>
                <div class="event-time" id="eventDateTime">{{ $selectedEvent->formatted_date ?? 'TODAY | 12:00PM-04:00PM' }}</div>
                <div class="event-location" id="eventLocation">{{ $selectedEvent->location ?? 'Auditorium, National Universi...' }}</div>
            </div>

            <hr class="event-divider">

            <!-- Travel Documents Upload Section -->
            <div class="travel-documents-section">
                <h6 class="travel-section-title mb-3">My Travel Documents</h6>

                <div class="travel-documents-list">
                    <!-- Flight Information -->
                    <div class="travel-item mb-3">
                        <div class="d-flex align-items-center">
                            <div class="travel-icon flight-icon me-3">
                                <i class="bi bi-airplane"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="travel-item-title mb-0">Flight Information</h6>
                                <small class="travel-item-subtitle" id="flightSubtitle">Upload your flight ticket</small>
                            </div>
                            <div class="travel-actions">
                                <input type="file" id="flightFile" class="d-none" accept=".pdf,.jpg,.jpeg,.png">
                                <button class="btn travel-add-btn" id="flightUploadBtn" onclick="document.getElementById('flightFile').click()">
                                    <i class="bi bi-upload"></i>
                                </button>
                                <button class="btn travel-download-btn d-none" id="flightDownloadBtn" onclick="downloadFile('flight')">
                                    <i class="bi bi-download"></i>
                                </button>
                                <button class="btn travel-delete-btn d-none" id="flightDeleteBtn" onclick="deleteFile('flight')">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </div>
                        <div class="upload-status mt-2" id="flightStatus"></div>
                    </div>

                    <!-- Hotel Information -->
                    <div class="travel-item mb-3">
                        <div class="d-flex align-items-center">
                            <div class="travel-icon hotel-icon me-3">
                                <i class="bi bi-building"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="travel-item-title mb-0">Hotel Information</h6>
                                <small class="travel-item-subtitle" id="hotelSubtitle">Upload your hotel reservation</small>
                            </div>
                            <div class="travel-actions">
                                <input type="file" id="hotelFile" class="d-none" accept=".pdf,.jpg,.jpeg,.png">
                                <button class="btn travel-add-btn" id="hotelUploadBtn" onclick="document.getElementById('hotelFile').click()">
                                    <i class="bi bi-upload"></i>
                                </button>
                                <button class="btn travel-download-btn d-none" id="hotelDownloadBtn" onclick="downloadFile('hotel')">
                                    <i class="bi bi-download"></i>
                                </button>
                                <button class="btn travel-delete-btn d-none" id="hotelDeleteBtn" onclick="deleteFile('hotel')">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </div>
                        <div class="upload-status mt-2" id="hotelStatus"></div>
                    </div>

                    <!-- Passport Details -->
                    <div class="travel-item mb-3">
                        <div class="d-flex align-items-center">
                            <div class="travel-icon passport-icon me-3">
                                <i class="bi bi-file-text"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="travel-item-title mb-0">Passport Details</h6>
                                <small class="travel-item-subtitle" id="passportSubtitle">Upload your passport copy</small>
                            </div>
                            <div class="travel-actions">
                                <input type="file" id="passportFile" class="d-none" accept=".pdf,.jpg,.jpeg,.png">
                                <button class="btn travel-add-btn" id="passportUploadBtn" onclick="document.getElementById('passportFile').click()">
                                    <i class="bi bi-upload"></i>
                                </button>
                                <button class="btn travel-download-btn d-none" id="passportDownloadBtn" onclick="downloadFile('passport')">
                                    <i class="bi bi-download"></i>
                                </button>
                                <button class="btn travel-delete-btn d-none" id="passportDeleteBtn" onclick="deleteFile('passport')">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </div>
                        <div class="upload-status mt-2" id="passportStatus"></div>
                    </div>
                </div>

                <!-- Save Button -->
                <div class="text-center mt-4">
                    <button class="btn btn-primary save-documents-btn" style="background-color: #04589C;" onclick="saveDocuments()">
                        <i class="bi bi-check-circle"></i> Save Documents
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const eventOptions = document.querySelectorAll('.event-option');
    const eventDetailsSection = document.getElementById('eventDetailsSection');
    window.selectedEventId = null;

    // Event selection handler
    eventOptions.forEach(option => {
        option.addEventListener('click', function() {
            const eventId = this.getAttribute('data-event-id');
            const eventTitle = this.getAttribute('data-event');

            if (eventId) {
                window.selectedEventId = eventId;
                loadEventDetails(eventId);

                // Update dropdown button text
                document.querySelector('.dropdown-text').textContent = eventTitle;

                // Update active state
                eventOptions.forEach(opt => opt.classList.remove('active'));
                this.classList.add('active');
            }
        });
    });

    // File upload handlers
    ['passportFile', 'flightFile', 'hotelFile', 'visaFile'].forEach(fileId => {
        const fileInput = document.getElementById(fileId);
        if (fileInput) {
            fileInput.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    const statusId = fileId.replace('File', 'Status');
                    const statusElement = document.getElementById(statusId);
                    if (statusElement) {
                        statusElement.innerHTML =
                            `<small class="text-success"><i class="bi bi-check-circle"></i> ${file.name}</small>`;
                    }
                }
            });
        }
    });
});

function loadEventDetails(eventId) {
    console.log('Loading event details for ID:', eventId);

    // First test the API connection
    fetch('/travel-documents/test')
        .then(response => response.json())
        .then(data => {
            console.log('API test result:', data);
        })
        .catch(error => {
            console.error('API test failed:', error);
        });

    const url = `/travel-documents/event-details?event_id=${eventId}`;
    console.log('Fetch URL:', url);

    fetch(url, {
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
        .then(response => {
            console.log('Response status:', response.status);
            console.log('Response headers:', response.headers);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            // Check if response is JSON
            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json')) {
                return response.text().then(text => {
                    console.error('Non-JSON response:', text);
                    throw new Error('Server returned non-JSON response');
                });
            }

            return response.json();
        })
        .then(data => {
            console.log('Event details response:', data);

            if (data.success) {
                const event = data.event;
                const existingFiles = data.existing_files || {};

                // Update event banner and details
                const imgEl = document.querySelector('.travel-event-image');
                if (imgEl && event.banner) imgEl.src = event.banner;

                document.getElementById('selectedEventTitle').textContent = event.title;
                document.getElementById('eventDateTime').textContent =
                    (event.date && event.start_time && event.end_time) ?
                    `${event.date} | ${event.start_time} - ${event.end_time}` : 'Date & Time TBA';
                document.getElementById('eventLocation').textContent =
                    event.country ? `${event.location || 'Location TBA'}, ${event.country}` : 'Location TBA';

                // Update existing files
                updateExistingFiles(existingFiles);

                // Show event details section
                document.getElementById('eventDetailsSection').classList.remove('d-none');
            } else {
                alert('Error loading event details: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Fetch error:', error);
            alert('Failed to load event details: ' + error.message);
        });
}

function updateExistingFiles(existingFiles) {
    const fileTypes = ['flight', 'hotel', 'passport'];

    fileTypes.forEach(type => {
        const fileName = existingFiles[type];
        const uploadBtn = document.getElementById(`${type}UploadBtn`);
        const downloadBtn = document.getElementById(`${type}DownloadBtn`);
        const deleteBtn = document.getElementById(`${type}DeleteBtn`);
        const subtitle = document.getElementById(`${type}Subtitle`);

        if (fileName) {
            // File exists - show download and delete buttons, hide upload button
            uploadBtn.classList.add('d-none');
            downloadBtn.classList.remove('d-none');
            deleteBtn.classList.remove('d-none');
            subtitle.textContent = `File: ${fileName}`;
            subtitle.classList.add('text-success');
        } else {
            // No file - show upload button, hide download and delete buttons
            uploadBtn.classList.remove('d-none');
            downloadBtn.classList.add('d-none');
            deleteBtn.classList.add('d-none');
            subtitle.textContent = getDefaultSubtitle(type);
            subtitle.classList.remove('text-success');
        }
    });
}

function getDefaultSubtitle(type) {
    const subtitles = {
        'flight': 'Upload your flight ticket',
        'hotel': 'Upload your hotel reservation',
        'passport': 'Upload your passport copy'
    };
    return subtitles[type] || 'Upload file';
}

function downloadFile(fileType) {
    if (!selectedEventId) {
        Swal.fire({
            icon: 'warning',
            title: 'No Event Selected',
            text: 'Please select an event first',
            confirmButtonColor: '#0039D1'
        });
        return;
    }

    window.location.href = `/travel-documents/download?event_id=${selectedEventId}&file_type=${fileType}`;
}

function deleteFile(fileType) {
    if (!selectedEventId) {
        Swal.fire({
            icon: 'warning',
            title: 'No Event Selected',
            text: 'Please select an event first',
            confirmButtonColor: '#0039D1'
        });
        return;
    }

    Swal.fire({
        title: 'Delete Document?',
        text: `Are you sure you want to delete the ${fileType} document? This action cannot be undone.`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'Yes, delete it!',
        cancelButtonText: 'Cancel'
    }).then((result) => {
        if (result.isConfirmed) {
            // Show loading
            Swal.fire({
                title: 'Deleting...',
                text: 'Please wait while we delete your document',
                allowOutsideClick: false,
                allowEscapeKey: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            fetch('/travel-documents/delete', {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    event_id: selectedEventId,
                    file_type: fileType
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Deleted!',
                        text: data.message,
                        confirmButtonColor: '#0039D1'
                    });
                    // Refresh the event details to update the UI
                    loadEventDetails(selectedEventId);
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Delete Failed',
                        text: data.message,
                        confirmButtonColor: '#0039D1'
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'An error occurred while deleting the file',
                    confirmButtonColor: '#0039D1'
                });
            });
        }
    });
}

function saveDocuments() {
    if (!selectedEventId) {
        alert('Please select an event first');
        return;
    }

    // Get all uploaded files
    const files = {
        passport: document.getElementById('passportFile')?.files[0],
        flight: document.getElementById('flightFile')?.files[0],
        hotel: document.getElementById('hotelFile')?.files[0],
        visa: document.getElementById('visaFile')?.files[0]
    };

    // Check if at least one file is uploaded
    const hasFiles = Object.values(files).some(file => file);

    if (!hasFiles) {
        alert('Please upload at least one document');
        return;
    }

    // Create FormData
    const formData = new FormData();
    formData.append('event_id', selectedEventId);
    formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));

    Object.keys(files).forEach(key => {
        if (files[key]) {
            formData.append(`${key}_file`, files[key]);
        }
    });

    // Upload files
    fetch('/travel-documents/upload', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Documents uploaded successfully!');
            // Reload event details to update file status
            loadEventDetails(selectedEventId);
            // Reset form
            ['passportFile', 'flightFile', 'hotelFile'].forEach(fileId => {
                const fileInput = document.getElementById(fileId);
                const statusElement = document.getElementById(fileId.replace('File', 'Status'));
                if (fileInput) fileInput.value = '';
                if (statusElement) statusElement.innerHTML = '';
            });
        } else {
            alert('Upload failed: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Upload failed!');
    });
}
</script>
@endpush
