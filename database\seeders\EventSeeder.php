<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class EventSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::table('events')->insert([
            'marketo_event_id' => '123456789',
            'event_title' => 'GA Commercial Excellence & Omnichannel Summit',
            'event_date' => Carbon::createFromDate(2024, 10, 21),
            'event_time' => Carbon::now()->format('H:i:s'),
            'event_banner' => 'test_event_banner.jpg',
            'user_check_in' => 1,
            'feedback_survey' => 1,
            'badge_creation' => 1,
            'interactive_tools' => 1,
            'status' => 1,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ]);
    }
}
