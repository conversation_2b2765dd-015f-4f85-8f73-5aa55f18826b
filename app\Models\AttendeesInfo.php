<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AttendeesInfo extends Model
{
    use HasFactory;

    protected $table = 'attendees_infos';

    protected $fillable = [
        'event_id',
        'register_id',
        'flight_information',
        'hotel_information',
        'passport_details',
    ];

    /**
     * Get the event that owns the attendees info.
     */
    public function event(): BelongsTo
    {
        return $this->belongsTo(Event::class);
    }

    /**
     * Get the register that owns the attendees info.
     */
    public function register(): BelongsTo
    {
        return $this->belongsTo(Register::class);
    }
}
