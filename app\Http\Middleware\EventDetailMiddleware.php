<?php

namespace App\Http\Middleware;

use App\Models\Event;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class EventDetailMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $event_id = $request->route('event_id');
        $event = Event::query()->find($event_id);

        if (!$event) {
            return response()->json(['message' => 'Event not found'], 404);
        }

        view()->share('event', $event);

        return $next($request);
    }
}
