<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('badge_templates', function (Blueprint $table) {
            $table->id();
            $table->integer('event_id')->default(1);
            $table->string('name')->nullable();
            $table->string('logo_url')->nullable();
            $table->string('bg_url')->nullable();
            $table->string('firstname_color')->default('#000000');
            $table->string('lastname_color')->default('#000000');
            $table->string('specialty_color')->default('#000000');
            $table->string('event_name_color')->default('#000000');
            $table->string('company_color')->default('#000000');
            $table->string('country_color')->default('#000000');
            $table->boolean('status')->default(1);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('badge_templates');
    }
};
