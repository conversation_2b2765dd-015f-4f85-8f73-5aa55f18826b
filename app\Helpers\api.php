<?php

use App\Models\ServiceLog;

ini_set('max_execution_time', 1800);

if (!function_exists('get_token')) {
        function get_token() {
        $curl = curl_init();
//https://825-YNO-689.mktorest.com/identity/oauth/token?grant_type=client_credentials&client_id=cb0057a9-3d9a-4098-a9e0-0efdacc26508&client_secret=8WjKR3tuCMXdhai8mHmRXZw9muOx8rg8
        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://890-QFF-740.mktorest.com/identity/oauth/token?grant_type=client_credentials&client_id=ba05ef00-d13d-4d0e-a078-803022f99efd&client_secret=Hnf5XGXE5O7zPZcTRXPO9AVPfCtU7F45',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'GET',
        ));

        $response = curl_exec($curl);

        curl_close($curl);

        $data = json_decode($response);
        if (isset($data->access_token)) {
            return $data->access_token;
        }
        return null;
    }
}

if (!function_exists('get_smart_list')) {
    function get_smart_list($smart_list_id) {
        $token = get_token();

        if ($token === null) {
            echo "Error: Unable to retrieve token.";
            return;
        }

        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://890-QFF-740.mktorest.com/rest/v1/list/'.$smart_list_id.'/leads.json?fields=email',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'GET',
            CURLOPT_HTTPHEADER => array(
                'Authorization: Bearer ' . $token
            ),
        ));

        $response = curl_exec($curl);

        curl_close($curl);
        $result = json_decode($response);

        if($result->success){
            if(!$result){
                echo "Error: No email found in smart list.";
                return;
            }
            $emails = [];
            foreach($result->result as $email){
                $emails[] = $email->id;
            }
            return ['emails' => $emails, 'token' => $token];
        }

        if($result->errors){
            echo "Error: " . $result->errors[0]->message;
            return;
        }
    }
}

if (!function_exists('get_user_list')) {
    function get_user_list($smart_list_id) {
        $smart_list = get_smart_list($smart_list_id);

        if(get_smart_list($smart_list_id) === NULL){
            echo "Error: smart list null.";
            return;
        }

        $emails = $smart_list['emails'];
        $token = $smart_list['token'];
        if (!$emails) {
            echo json_decode("Error: email null.");
            return;
        }

        $emails = implode(',', $emails);

        $url = 'https://890-QFF-740.mktorest.com/rest/v1/leads.json?filterType=id&filterValues='.$emails.'&fields=firstName%2ClastName%2Ccompany%2Cemail%2Ctitle%2Cphone';
        $curl = curl_init();

        curl_setopt_array($curl, array(
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'GET',
        CURLOPT_HTTPHEADER => array(
            'Authorization: Bearer ' . $token
        ),
        ));

        $response = curl_exec($curl);

        curl_close($curl);

        $request_data = [
            'filterType' => 'email',
            'filterValues' => $emails,
            'fields' => 'firstName,lastName,company,email,title,phone',
        ];

        App\Models\ServiceLog::create([
            'service' => 'get_user_list',
            'url' => 'https://890-QFF-740.mktorest.com/rest/v1/leads.json?filterType=id&filterValues=',
            'request' => json_encode($request_data),
            'response' => $response,
        ]);

        if($response){
            return json_decode($response)->result;
        }else{
            echo "Error: Unable to retrieve user list.";
            return;
        }
    }
}

if (!function_exists('complete_user_list')) {
    function complete_user_list($user, $token) {

        //$token = get_token();

        if ($token === null) {
            echo "Error: Unable to retrieve token.";
            return;
        }

        $niceyeExternalID = $user->get_event->marketo_event_id.'|'.$user->user_id;

        $url = 'https://890-QFF-740.mktorest.com/rest/v1/customobjects/niceyeAttendeeData_c.json?access_token=bfba3a32-e6fe-45c5-985f-962aac134d04%3Aab';

        $curl = curl_init();

        curl_setopt_array($curl, array(
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS =>'{
            "action": "createOrUpdate",
            "dedupeBy": "dedupeFields",
            "input": [
                {
                    "attended": "0",
                    "company": "BD",
                    "confirmation": "0",
                    "notAttended": "0",
                    "registered": "1",
                    "sessionsAttended": "",
                    "shortlisted": "0",
                    "email": "'.$user->email.'",
                    "eventID": "'.$user->get_event->marketo_event_id.'",
                    "eventTitle": "'.$user->get_event->event_title.'",
                    "firstName": "'.$user->first_name.'",
                    "lastName": "'.$user->last_name.'",
                    "niceyeExternalID": "'.$niceyeExternalID.'",
                    "leadID": "'.$user->user_id.'"
                }
            ]
        }',
        CURLOPT_HTTPHEADER => array(
            'Content-Type: application/json',
            'Authorization: Bearer ' . $token
        ),
        ));

        $response = curl_exec($curl);

        curl_close($curl);

        $request_data = [
            'shortlisted' => $user->status,
            'email' => $user->email,
            'eventID' => $user->get_event->marketo_event_id,
            'eventTitle' => $user->get_event->event_title,
            'firstName' => $user->first_name,
            'lastName' => $user->last_name,
            'niceyeExternalID' => $user->get_event->marketo_event_id.'|'.$user->user_id,
            'leadID' => $user->user_id,
        ];

        App\Models\ServiceLog::create([
            'service' => 'complete_user_list',
            'url' => $url,
            'request' => json_encode($request_data),
            'response' => $response,
        ]);


        return $response;

    }
}

if (!function_exists('complete_short_list')) {
    function complete_short_list($user, $token) {

        //$token = get_token();

        if ($token === null) {
            echo "Error: Unable to retrieve token.";
            return;
        }

        $url = 'https://890-QFF-740.mktorest.com/rest/v1/customobjects/niceyeAttendeeData_c.json';
        $niceyeExternalID = $user->get_event->marketo_event_id.'|'.$user->user_id;

        $curl = curl_init();

        curl_setopt_array($curl, array(
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS =>'{
            "action":"updateOnly",
            "input":[
                {
                    "niceyeExternalID":"'.$niceyeExternalID.'",
                    "shortlisted":"true",
                    "confirmation":"Not Submitted"
                }
            ]
        }
        ',
        CURLOPT_HTTPHEADER => array(
            'Content-Type: application/json',
            'Authorization: Bearer ' . $token
        ),
        ));

        $response = curl_exec($curl);

        curl_close($curl);

        $request_data = [
            'niceyeExternalID' => $user->get_event->marketo_event_id.'|'.$user->user_id,
        ];

        App\Models\ServiceLog::create([
            'service' => 'complete_short_list',
            'url' => $url,
            'request' => json_encode($request_data),
            'response' => $response,
        ]);



        $url = 'https://890-QFF-740.mktorest.com/rest/v1/customobjects/niceyeAttendeeData_c.json';

        $curl = curl_init();

        curl_setopt_array($curl, array(
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS =>'{
            "action":"updateOnly",
            "input":[
                {
                    "niceyeExternalID":"'.$niceyeExternalID.'",
                    "confirmation":"Confirmed"
                }
            ]
        }
        ',
        CURLOPT_HTTPHEADER => array(
            'Content-Type: application/json',
            'Authorization: Bearer ' . $token
        ),
        ));

        $response = curl_exec($curl);

        curl_close($curl);

        $request_data = [
            'niceyeExternalID' => $user->get_event->marketo_event_id.'|'.$user->user_id,
        ];

        App\Models\ServiceLog::create([
            'service' => 'confirmation',
            'url' => $url,
            'request' => json_encode($request_data),
            'response' => $response,
        ]);


        return $response;

    }
}

if (!function_exists('user_check_in_manuel')) {
    function user_check_in_manuel($user) {

        $token = get_token();

        if ($token === null) {
            echo "Error: Unable to retrieve token.";
            return;
        }

        $url = 'https://890-QFF-740.mktorest.com/rest/v1/customobjects/niceyeAttendeeData_c.json';
        $niceyeExternalID = $user->get_event->marketo_event_id.'|'.$user->user_id;

        $curl = curl_init();

        curl_setopt_array($curl, array(
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS =>'{
            "action":"updateOnly",
            "input":[
                {
                    "niceyeExternalID":"'.$niceyeExternalID.'",
                    "Attended":"true"
                }
            ]
        }
        ',
        CURLOPT_HTTPHEADER => array(
            'Content-Type: application/json',
            'Authorization: Bearer ' . $token
        ),
        ));

        $response = curl_exec($curl);

        curl_close($curl);

        $request_data = [
            'niceyeExternalID' => $user->get_event->marketo_event_id.'|'.$user->user_id,
        ];

        App\Models\ServiceLog::create([
            'service' => 'Attended',
            'url' => $url,
            'request' => json_encode($request_data),
            'response' => $response,
        ]);


        return $response;

    }
}

if (!function_exists('user_check_in_session_manuel')) {
    function user_check_in_session_manuel($user, $session) {

        $token = get_token();

        if ($token === null) {
            echo "Error: Unable to retrieve token.";
            return;
        }

        $url = 'https://890-QFF-740.mktorest.com/rest/v1/customobjects/niceyeAttendeeData_c.json';
        $niceyeExternalID = $user->get_event->marketo_event_id.'|'.$user->user_id;

        $curl = curl_init();

        curl_setopt_array($curl, array(
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS =>'{
            "action":"updateOnly",
            "input":[
                {
                    "niceyeExternalID":"'.$niceyeExternalID.'",
                    "Attended":"true",
                    "sessionsAttended":"'.$session.'"
                }
            ]
        }
        ',
        CURLOPT_HTTPHEADER => array(
            'Content-Type: application/json',
            'Authorization: Bearer ' . $token
        ),
        ));

        $response = curl_exec($curl);

        curl_close($curl);

        $request_data = [
            'niceyeExternalID' => $user->get_event->marketo_event_id.'|'.$user->user_id,
            'sessionsAttended' => $session
        ];

        App\Models\ServiceLog::create([
            'service' => 'Session Attended',
            'url' => $url,
            'request' => json_encode($request_data),
            'response' => $response,
        ]);


        return $response;

    }
}
