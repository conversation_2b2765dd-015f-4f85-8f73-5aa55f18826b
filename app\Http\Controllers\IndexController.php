<?php

namespace App\Http\Controllers;

use App\Models\Event;
use App\Models\Register;
use Illuminate\Http\Request;
use SimpleSoftwareIO\QrCode\Facades\QrCode;
use SimpleSoftwareIO\QrCode\Generator;

class IndexController extends Controller
{
    public function qrCodePreview($user_id)
    {
        $register = Register::query()->where('user_id', $user_id)->firstOrFail();

        /*if (!$register->qr_code){
            return abort(404, 'QR not found.');
        }*/

        // QR kod için veri belirle: Manuel badge ise email, normal ise uuid
        $qrData = ($register->status == 3) ? $register->email : $register->uuid;

        try {
            // SVG formatını kullan (Imagick gerektirmez)
            $svgQrCode = QrCode::format('svg')->size(200)->generate($qrData);
            $base64 = base64_encode($svgQrCode);
        } catch (\Exception $e) {
            // Eğer QR kod oluşturulamıyorsa, basit bir placeholder oluştur
            $placeholder = imagecreate(200, 200);
            $bg = imagecolorallocate($placeholder, 255, 255, 255);
            $text_color = imagecolorallocate($placeholder, 0, 0, 0);
            imagestring($placeholder, 5, 50, 90, 'QR Error', $text_color);

            ob_start();
            imagepng($placeholder);
            $qrCode = ob_get_contents();
            ob_end_clean();
            imagedestroy($placeholder);

            $base64 = base64_encode($qrCode);
        }

        return view('qrcode_preview', compact(['register','base64']));
    }

    public function qrCodePreview2(Request $request)
    {
        if(($request->user == 1088192 || $request->user == 111212 || $request->user == 111213 ||
            $request->user == 111214) && $request->event == 1568){
                try {
                    $svgQrCode = QrCode::format('svg')->size(200)->generate($request->user);
                    $base64 = base64_encode($svgQrCode);
                } catch (\Exception $e) {
                    $placeholder = imagecreate(200, 200);
                    $bg = imagecolorallocate($placeholder, 255, 255, 255);
                    $text_color = imagecolorallocate($placeholder, 0, 0, 0);
                    imagestring($placeholder, 5, 50, 90, 'QR Error', $text_color);

                    ob_start();
                    imagepng($placeholder);
                    $qrCode = ob_get_contents();
                    ob_end_clean();
                    imagedestroy($placeholder);

                    $base64 = base64_encode($qrCode);
                }

                return view('qrcode_preview2', compact('base64'));
        }else{
            return abort(404, 'User not found.');
        }
    }

    public function testFunc()
    {
        echo 'Test';
    }
}
