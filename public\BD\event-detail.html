<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BD | Event Detail</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
</head>

<body class="dashboard-body">
    <!-- Header -->
    <header class="dashboard-header">
        <div class="container px-3 py-2">
            <div class="d-flex justify-content-between align-items-center">
                <!-- Left side - Logo and Navigation -->
                <div class="d-flex align-items-center">
                    <div class="d-flex align-items-center me-4">
                        <img src="assets/images/bd-logo.png" alt="BD Logo" class="logo-image me-2">
                        <h4 class="logo-text-small mb-0 d-none d-lg-block">BD</h4>
                    </div>
                    <!-- Desktop Navigation -->
                    <nav class="d-none d-lg-flex">
                        <a href="travel-documents.html" class="nav-link me-4">Travel Documents</a>
                        <a href="dashboard.html" class="nav-link">My Events</a>
                    </nav>
                </div>

                <!-- Center - Welcome Text (Desktop only) -->
                <div class="d-none d-lg-block text-center flex-grow-1">
                    <p class="welcome-text-desktop mb-0">Welcome to your BD events dashboard</p>
                </div>

                <!-- Right side - User Profile and Menu -->
                <div class="d-flex align-items-center">
                    <!-- Desktop User Profile -->
                    <div class="d-none d-lg-flex align-items-center">
                        <div class="text-end me-3">
                            <h6 class="user-name-desktop mb-0">Dr. John Simmons</h6>
                            <small class="user-title-desktop">Title placeholder</small>
                        </div>
                        <img src="assets/images/user.png" alt="Profile" class="profile-image-desktop">
                    </div>

                    <!-- Mobile Menu Button -->
                    <button class="btn btn-link p-0 text-dark d-lg-none" id="menuToggle">
                        <i class="bi bi-list fs-4"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Side Menu Overlay -->
    <div class="side-menu-overlay" id="sideMenuOverlay"></div>

    <!-- Side Menu -->
    <div class="side-menu" id="sideMenu">
        <div class="side-menu-header">
            <div class="side-menu-logo">
                <img src="assets/images/bd-logo.png" alt="BD Logo" class="logo-image">
            </div>
            <button class="side-menu-close" id="sideMenuClose">
                <i class="bi bi-x"></i>
            </button>
        </div>

        <div class="side-menu-profile">
            <img src="assets/images/user.png" alt="Profile" class="side-menu-profile-image">
            <div class="side-menu-profile-info">
                <h5>Dr. John Simmons</h5>
                <small>Title placeholder</small>
            </div>
        </div>

        <nav class="side-menu-nav">
            <a href="travel-documents.html" class="side-menu-item">Travel Documents</a>
            <a href="dashboard.html" class="side-menu-item">My Events</a>
        </nav>
    </div>

    <script>
        // Side menu functionality
        const menuToggle = document.getElementById('menuToggle');
        const sideMenu = document.getElementById('sideMenu');
        const sideMenuOverlay = document.getElementById('sideMenuOverlay');
        const sideMenuClose = document.getElementById('sideMenuClose');

        function openSideMenu() {
            sideMenu.classList.add('show');
            sideMenuOverlay.classList.add('show');
            document.body.style.overflow = 'hidden';
        }

        function closeSideMenu() {
            sideMenu.classList.remove('show');
            sideMenuOverlay.classList.remove('show');
            document.body.style.overflow = '';
        }

        menuToggle.addEventListener('click', openSideMenu);
        sideMenuClose.addEventListener('click', closeSideMenu);
        sideMenuOverlay.addEventListener('click', closeSideMenu);
    </script>

    <!-- Welcome Section -->
    <div class="container px-3 py-3">
        <p class="welcome-text mb-3">Welcome to your BD events dashboard</p>

        <!-- User Profile -->
        <div class="d-flex align-items-center mb-4">
            <img src="assets/images/user.png" alt="Profile" class="profile-image me-3">
            <div>
                <h6 class="user-name mb-0">Dr. John Simmons</h6>
                <small class="user-title text-muted">Title placeholder</small>
            </div>
        </div>

        <!-- Event Detail Card -->
        <div class="event-detail-card mb-4">
            <div class="event-image-container">
                <img src="assets/images/event.png" alt="Event Image" class="event-image">
                <span class="registered-badge">Registered</span>
            </div>
            <div class="event-content">
                <h5 class="event-title">APAC Hospital at Home Symposium</h5>
                <div class="event-details mb-3">
                    <small class="text-muted">TODAY | 12:00PM-04:00PM</small><br>
                    <small class="text-muted">Auditorium, National Universi...</small>
                </div>
                <p class="event-description">
                    Explore cutting-edge homecare solutions for patient management, including telemedicine, remote
                    monitoring, and personalized care plans.
                </p>
                <button class="btn contact-btn w-100 mb-3">Contact</button>
            </div>
        </div>

        <!-- Speakers Panel -->
        <div class="speakers-section mb-4">
            <h6 class="section-title">Speakers/Panel</h6>
            <div class="row g-3">
                <div class="col-sm-6 col-md-3">
                    <div class="speaker-card">
                        <div class="speaker-image-placeholder"></div>
                        <h6 class="speaker-name">Dr. İlker Devrim</h6>
                        <button class="btn speaker-read-btn" onclick="window.location.href='speaker-detail.html'">Read
                            more</button>
                    </div>
                </div>
                <div class="col-sm-6 col-md-3">
                    <div class="speaker-card">
                        <div class="speaker-image-placeholder"></div>
                        <h6 class="speaker-name">Dr. İlker Devrim</h6>
                        <button class="btn speaker-read-btn" onclick="window.location.href='speaker-detail.html'">Read
                            more</button>
                    </div>
                </div>
                <div class="col-sm-6 col-md-3">
                    <div class="speaker-card">
                        <div class="speaker-image-placeholder"></div>
                        <h6 class="speaker-name">Dr. İlker Devrim</h6>
                        <button class="btn speaker-read-btn" onclick="window.location.href='speaker-detail.html'">Read
                            more</button>
                    </div>
                </div>
                <div class="col-sm-6 col-md-3">
                    <div class="speaker-card">
                        <div class="speaker-image-placeholder"></div>
                        <h6 class="speaker-name">Dr. İlker Devrim</h6>
                        <button class="btn speaker-read-btn" onclick="window.location.href='speaker-detail.html'">Read
                            more</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- What to expect -->
    <div class="expect-section mb-4 ">
        <div class="container px-3 py-3">
            <h6 class="section-title">What to expect from this event?</h6>
            <p class="expect-text">
                Lorem ipsum dolor sit amet consectetur. Lectus est tempus sed sed venenatis amet.
            </p>
            <p class="expect-text">
                Purus mattis morbi sed sed amet facilisi nam purus quam. Facilisis ultrices libero commodo sed aenean
                fusce lectus.
            </p>
            <p class="expect-text">
                Viverra urna tincidunt consequat quis enim faucibus cras vitae odio. Sit adipiscing molestie amet auctor
                gravida aliquet.
            </p>
            <p class="expect-text">
                Urna non sapien lectus mauris feugiat in pharetra neque arcu. Nisl leo convallis cursus urna mauris
                mauris nunc urna.
            </p>
        </div>
    </div>

    <div class="container px-3 py-3">
        <!-- Agenda Section -->
        <div class="agenda-section mb-4">
            <h6 class="section-title">Agenda</h6>

            <div class="agenda-item">
                <div class="agenda-time">00:00 - 00:00</div>
                <div class="agenda-content">
                    <h6 class="agenda-topic">Placeholder topic lorem ipsum dolor sit amet, consectetur adipiscing elit.
                    </h6>
                    <p class="agenda-speaker">Dr Name surname</p>
                </div>
            </div>

            <div class="agenda-item">
                <div class="agenda-time">00:00 - 00:00</div>
                <div class="agenda-content">
                    <h6 class="agenda-topic">Placeholder topic lorem ipsum dolor sit amet, consectetur adipiscing elit.
                    </h6>
                    <p class="agenda-speaker">Dr Name surname</p>
                </div>
            </div>

            <div class="agenda-item">
                <div class="agenda-time">00:00 - 00:00</div>
                <div class="agenda-content">
                    <h6 class="agenda-topic">Placeholder topic lorem ipsum dolor sit amet, consectetur adipiscing elit.
                    </h6>
                    <p class="agenda-speaker">Dr Name surname</p>
                </div>
            </div>

            <div class="agenda-item">
                <div class="agenda-time">00:00 - 00:00</div>
                <div class="agenda-content">
                    <h6 class="agenda-topic">Placeholder topic lorem ipsum dolor sit amet, consectetur adipiscing elit.
                    </h6>
                    <p class="agenda-speaker">Dr Name surname</p>
                </div>
            </div>
        </div>
    </div>
</body>

</html>