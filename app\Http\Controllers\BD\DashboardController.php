<?php

namespace App\Http\Controllers\BD;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use App\Models\Event;
use App\Models\Register;
use App\Models\EventDate;
use Carbon\Carbon;

class DashboardController extends Controller
{
    /**
     * Show dashboard
     */
    public function index()
    {
        $user = Session::get('bd_user');

        if (!$user) {
            return redirect()->route('bd.login');
        }

        // Get user's registered events (My Events)
        $registeredEventIds = Register::where('email', $user->email)
            ->pluck('event_id')
            ->toArray();

        $myEvents = Event::whereIn('id', $registeredEventIds)
            ->where('status', 1) // Active events only
            ->with('event_dates') // Load event dates relationship
            ->get()
            ->map(function ($event) {
                return $this->formatEventData($event, true);
            })
            ->sortBy('sort_date'); // Sort by calculated date

        // Get other events (not registered)
        $otherEvents = Event::whereNotIn('id', $registeredEventIds)
            ->where('status', 1) // Active events only
            ->with('event_dates') // Load event dates relationship
            ->get()
            ->map(function ($event) {
                return $this->formatEventData($event, false);
            })
            ->sortBy('sort_date'); // Sort by calculated date

        return view('bd.dashboard', compact('user', 'myEvents', 'otherEvents'));
    }

    /**
     * Format event data for display
     */
    private function formatEventData($event, $isRegistered = false)
    {
        // Collect all locations from event_dates
        $locations = [];
        $firstEventDate = null;

        if ($event->event_dates && $event->event_dates->count() > 0) {
            foreach ($event->event_dates as $eventDate) {
                if (!$firstEventDate) {
                    $firstEventDate = $eventDate; // İlk tarihi date/time için kullanacağız
                }

                $location = $eventDate->event_location ?? 'Location TBA';

                // Handle location - if it's numeric, use mapping, otherwise use as-is
                if (is_numeric($location)) {
                    $locationName = $this->getLocationName($location);
                } else {
                    $locationName = $location;
                }

                // Add country if available
                if ($eventDate->event_country) {
                    $locationName = $locationName . ', ' . $eventDate->event_country;
                }

                // Aynı location'ı tekrar ekleme
                if (!in_array($locationName, $locations)) {
                    $locations[] = $locationName;
                }
            }
        }

        // Date/time bilgisi için ilk event_date'i kullan
        if ($firstEventDate) {
            $startDate = Carbon::parse($firstEventDate->event_date);
            $startTime = $firstEventDate->event_start_time ?? '09:00 AM';
            $endTime = $firstEventDate->event_end_time ?? '05:00 PM';
        } else {
            // Fallback to event table dates if no event_dates found
            $startDate = $event->event_date ? Carbon::parse($event->event_date) : Carbon::now();
            $startTime = $event->event_start_time ?? '09:00 AM';
            $endTime = $event->event_end_time ?? '05:00 PM';
            $locations[] = $event->event_location ?? 'Location TBA';
        }

        $dateText = $startDate->format('M d, Y');
        $timeText = $this->formatTime($startTime) . '-' . $this->formatTime($endTime);

        return (object) [
            'id' => $event->id,
            'title' => $event->event_title,
            'formatted_date' => $dateText . ' | ' . $timeText,
            'locations' => $locations, // Array olarak döndür
            'image' => $event->event_banner ? asset(env('STORAGE_PATH') . '/event_banners/' . $event->event_banner) : asset('assets/bd/images/event.png'),
            'is_registered' => $isRegistered,
            'start_date' => $startDate->format('Y-m-d'),
            'start_time' => $startTime,
            'end_time' => $endTime,
            'description' => $event->event_description,
            'status' => $event->status,
            'sort_date' => $startDate->timestamp // For sorting
        ];
    }

    /**
     * Format time to consistent format
     */
    private function formatTime($time)
    {
        try {
            return Carbon::createFromFormat('h:i A', $time)->format('h:iA');
        } catch (\Exception $e) {
            return $time; // Return as-is if parsing fails
        }
    }

    /**
     * Get location name by ID - using actual data from event_dates table
     */
    private function getLocationName($locationId)
    {
        // Based on the actual event_dates table data
        // Since the table shows location IDs like "1" and "12",
        // we'll create meaningful names for these
        $locations = [
            '1' => 'Main Conference Hall',
            '2' => 'Secondary Meeting Room',
            '12' => 'Auditorium Complex',
            // Add more locations as they appear in your event_dates table
        ];

        return $locations[$locationId] ?? "Venue #{$locationId}";
    }
}
