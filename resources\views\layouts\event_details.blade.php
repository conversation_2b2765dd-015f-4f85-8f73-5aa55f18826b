<div class="event_details_container row">
    <div class="col-12 d-flex justify-content-between align-items-center flex-wrap">
        <h5 class="mb-3 event_title fw-bolder">
            {{ (request()->has('clone') ? 'Clone Event' : (request()->is('admin/new-event') ? 'Add Event' : (request()->is('admin/event-detail/*') ? '' : 'Event Details'))) }}
        </h5>
        <div class="d-flex flex-row align-items-center">
            <p class="pe-3 mb-0">Status: </p>
            <div class="btn-group">

                    @if (request()->is('admin/event-detail/*') || request()->is('admin/event/*'))
                        @if($event->status == 2)
                            <button
                                class="btn status dropdown-toggle pass"
                                type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                Draft
                            </button>
                        @else
                            @if ($event->event_dates()->orderBy('event_date', 'ASC')->first() && $event->event_dates()->orderBy('event_date', 'ASC')->first()->event_date >= date('Y-m-d'))
                                <button
                                    class="btn status dropdown-toggle active"
                                    type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    Active
                                </button>
                            @else
                                <button
                                    class="btn status dropdown-toggle pass"
                                    type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    Past
                                </button>
                            @endif
                        @endif
                    @else
                        <button
                            class="btn status dropdown-toggle active"
                            type="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Active
                        </button>
                    @endif

            </div>
        </div>
    </div>

    @if(request()->is('admin/event-detail/*') || request()->is('admin/new-event'))
        <div class="col-12">
            <div class="d-flex flex-wrap gap-3">
                @php
                    $header_toggles = [
                        'User Check-in' => 'user_check_in',
                        /* 'Feedback Survey' => 'feedback_survey', */
                        'Badge Creation' => 'badge_creation',
                        'Attendees Info' => 'attendees_info',
                        /* 'Interactive Tools' => 'interactive_tools',*/
                    ];
                @endphp
                @foreach ($header_toggles as $header_toggle => $id)
                    <div class="d-inline-flex form-check form-switch">
                        <label class="form-check-label" for="{{ $id }}">{{ $header_toggle }}</label>
                        <input class="form-check-input ms-2" type="checkbox" id="{{ $id }}"
                            name="{{ $id }}" @if((isset($event) && $event->$id == 1) || (isset($eventClone) && $eventClone->$id == 1) || request()->is('admin/new-event')) checked @endif>
                    </div>
                @endforeach
            </div>
        </div>
    @endif
    <hr class="my-3">
</div>
