<div class="row mt-4" id="templateDesign">
    <div class="mb-3">
        <h6 class="fw-bold text-primary">Template Design</h6>
    </div>
    <!-- Upload Section -->
    <div class="col-md-4 mt-4" style="">
        <p class="fw-bolder">Upload</p>
        <div class="upload-container position-relative d-flex flex-column justify-content-center mt-2">

            <!-- logo upload -->
            <div id="templateLogoButton" style="cursor:pointer;"
                class="upload-box d-flex flex-row justify-content-center align-items-center ">
                <div class="upload-icon d-flex align-items-center">
                    <img src="{{ asset('images/upload.png') }}" width="30px" />
                </div>

                <div class="d-flex flex-column justify-content-center">
                    <div class="upload-text fw-bolder">Upload Logo</div>
                    <div class="upload-subtext">Min Size: 150x60</div>
                </div>
            </div>

            <!-- logo upload end -->
            <div class="d-flex flex-column align-items-center">
                <p class="speaker mt-3"></p>
                <h2 class="fw-semibold template_preview_fn">Name Surname</h2> {{-- template_preview_ln template_preview_fn --}}
                <p class="fw-semibold template_preview_specialty">Specialty / Job Title</p>
                <br>
                <h3 class="fw-semibold template_preview_event_name">Name of Event</h3>
                <br>
                <p class="fw-semibold template_preview_company">Hospital / Organization</p>
                <p class="fw-semibold template_preview_c">Country</p>
            </div>

            <!-- qr -->
            <div class="d-flex justify-content-center align-items-center">
                <div
                    class="upload_qr_container d-flex justify-content-center align-items-center mt-3">
                    {{--<img src="qr_placeholder.png" alt="QR Code" class="qr-image">--}}
                    @isset($register)
                        <img src="/qr/{{ $register->user_id }}.png" class="qr-image">
                    @endisset
                </div>
            </div>
            <!-- qr end -->

            <!-- background upload -->
            <div class="background-upload-container d-flex flex-column position-absolute" id="templateBgButton" style="cursor:pointer;">
                <div class="upload-icon">
                    <img src="{{ asset('images/upload.png') }}" width="30px" />
                </div>

                <div class="">
                    <div class="upload-text fw-bolder">Upload <br> Background</div>
                    <div class="upload-subtext">Min Size: 575x850</div>
                </div>
            </div>
            <!-- background upload end -->
        </div>
        <p class="mt-2">Minimum image size: 575x850 px<br></p>
    </div>

    <!-- Preview Section justify-content-center -->
    <div class="col-md-4 mt-4">
        <p class="fw-bolder">Preview</p>
        <div id="preview-container-template"
             class="preview-container position-relative d-flex flex-column align-items-center text-center mt-2">
            {{--<img src="{{ asset('images/bd-logo.png') }}" />
            <p class="speaker mt-3">Speaker</p> --}}
            <div class="template_logo_container" style="max-width: 325px;">
                {{--<h3 class="template_first_name fw-bolder template_preview_fn">JOHN</h3>
                <h4 class="template_last_name fw-bolder template_preview_ln">DOE</h4>
                <p class="template_company_name fw-bolder template_preview_c">Country</p>--}}

                <h2 class="fw-semibold template_preview_fn template_full_name">Name Surname</h2> {{-- template_preview_ln template_preview_fn --}}
                <p class="fw-semibold template_specialty template_preview_specialty">Specialty / Job Title</p>
                <br>
                <h3 class="fw-semibold template_event_name template_preview_event_name"
                    style="max-width:325px;">
                    Name of Event
                </h3>
                <br>
                <p class="fw-semibold template_company template_preview_company">Hospital / Organization</p>
                <p class="fw-semibold template_country template_preview_c">Country</p>
            </div>
            {{-- <div
                class="qr_container d-flex justify-content-center align-items-center mt-3">
                <img src="qr_placeholder.png" alt="QR Code" class="qr-image">
            </div>--}}
        </div>
    </div>

    <!-- Form Section -->
    <div class="col-md-4 mt-4">
        <div class="form-container">
            <form id="template_form" method="post" enctype="multipart/form-data">
                @csrf
                <input type="hidden" name="template_id" value="1">
                <input type="file" id="template_badge_bg_file" name="template_badge_bg_file" style="display: none;" accept=".png">
                <input type="file" id="template_badge_logo_file" name="template_badge_logo_file" style="display: none;" accept=".png">

                @isset($register)
                <input type="hidden" id="template_badge_qr_code" name="template_badge_qr_code" value="{{ $register->qr_code }}">
                <input type="hidden" id="register_id" name="register_id" value="{{ $register->id }}">
                @endisset

                {{-- Name --}}
                <div class="d-flex justify-content-between">
                    <label for="name" class="form-label">Name</label>
                    <div class="change-color">
                        <img src="{{ asset('images/color-change.png') }}"
                             alt="Change Color Icon" class="color-icon">
                        <span class="change-color-text mr-2">Color:</span>
                        <input type="color" name="first_name_color"
                               value="{{ isset($badge_template_1) ? $badge_template_1->firstname_color : '#F0682F' }}"
                               style="height: 20px; margin-bottom:0px">
                    </div>
                </div>
                <div class="mb-3">
                    <input type="text" class="form-control" id="template_first_name" name="first_name"
                           value="@isset($register){{ $register->first_name }}@endisset"
                           placeholder="Name" required>
                </div>

                {{-- Surname --}}
                <div class="d-flex justify-content-between">
                    <label for="surname" class="form-label">Surname</label>
                    <div class="change-color">
                        <img src="{{ asset('images/color-change.png') }}"
                             alt="Change Color Icon" class="color-icon">
                        <span class="change-color-text">Color: </span>
                        <input type="color" name="last_name_color"
                               value="{{ isset($badge_template_1) ? $badge_template_1->lastname_color : '#F0682F' }}"
                               style="height: 20px; margin-bottom:0px">
                    </div>
                </div>
                <div class="mb-3">
                    <input type="text" class="form-control" id="template_last_name" name="last_name"
                           value="@isset($register){{ $register->last_name }}@endisset"
                           placeholder="Surname" required>
                </div>

                {{-- Specialty --}}
                <div class="d-flex justify-content-between">
                    <label for="surname" class="form-label">Specialty / Job Title</label>
                    <div class="change-color">
                        <img src="{{ asset('images/color-change.png') }}"
                             alt="Change Color Icon" class="color-icon">
                        <span class="change-color-text">Color: </span>
                        <input type="color" name="specialty_color"
                               value="{{ isset($badge_template_1) ? $badge_template_1->specialty_color : '#F0682F' }}"
                               style="height: 20px; margin-bottom:0px">
                    </div>
                </div>
                <div class="mb-3">
                    <input type="text" class="form-control" id="template_specialty" name="specialty"
                           placeholder="Specialty / Job Title" value="@isset($register){{ $register->title }}@endisset">
                </div>


                {{-- Organization / Company / Department --}}
                <div class="d-flex justify-content-between">
                    <label for="surname" class="form-label">Organization / Company</label>
                    <div class="change-color">
                        <img src="{{ asset('images/color-change.png') }}"
                             alt="Change Color Icon" class="color-icon">
                        <span class="change-color-text">Color: </span>
                        <input type="color" name="company_color"
                               value="{{ isset($badge_template_1) ? $badge_template_1->company_color : '#F0682F' }}"
                               style="height: 20px; margin-bottom:0px">
                    </div>
                </div>
                <div class="mb-3">
                    <input type="text" class="form-control" id="template_company" name="company"
                           placeholder="Company" value="@isset($register){{ $register->company }}@endisset">
                </div>

                {{-- Country --}}
                <div class="d-flex justify-content-between">
                    <label for="country" class="form-label">Country</label>
                    <div class="change-color">
                        <img src="{{ asset('images/color-change.png') }}"
                             alt="Change Color Icon" class="color-icon">
                        <span class="change-color-text">Color:</span>
                        <input type="color" name="country_color"
                               value="{{ isset($badge_template_1) ? $badge_template_1->country_color : '#F0682F' }}"
                               style="height: 20px; margin-bottom:0px">
                    </div>
                </div>
                <div class="mb-3">
                    <input type="text" class="form-control" id="template_country" name="country"
                           value="@isset($register){{ $register->country }}@endisset"
                           placeholder="Country">
                </div>

                {{-- Event Name --}}
                <div class="d-flex justify-content-between">
                    <label for="surname" class="form-label">Event Name</label>
                    <div class="change-color">
                        <img src="{{ asset('images/color-change.png') }}"
                             alt="Change Color Icon" class="color-icon">
                        <span class="change-color-text">Color: </span>
                        <input type="color" name="event_name_color"
                               value="{{ isset($badge_template_1) ? $badge_template_1->event_name_color : '#F0682F' }}"
                               style="height: 20px; margin-bottom:0px">
                    </div>
                </div>
                <div class="mb-3">
                    <input type="text" class="form-control" id="template_event_name" name="event_name"
                           placeholder="Event Name" value="{{ isset($event) ? $event->event_title : "" }}" required>
                </div>

                <input type="hidden" name="email" value="@isset($register){{ $register->email }}@endisset">
                <input type="hidden" name="user_id" value="@isset($register){{ $register->user_id }}@endisset">

                {{-- Business Unit --}}{{--
                <div class="d-flex justify-content-between">
                    <label for="role" class="form-label">Business Unit</label>
                    <div class="change-color">
                        --}}{{--<img src="{{ asset('images/color-change.png') }}"
                             alt="Change Color Icon" class="color-icon">
                        <span class="change-color-text">Change Color</span>--}}{{--
                    </div>
                </div>
                <div class="mb-3">
                    <select class="form-select" name="department" id="template_department" required>
                        <option value="">Select</option>
                        <option value="Omni" @isset($register) @if($register->company == 'Omni') selected @endif @endisset>Omni</option>
                        <option value="ComEx" @isset($register) @if($register->company == 'ComEx') selected @endif @endisset>ComEx</option>
                        <option value="Tgs" @isset($register) @if($register->company == 'TGS') selected @endif @endisset>TGS</option>
                    </select>
                </div>--}}

                <div class="" style="text-align: right">
                    <button class="badge_create_save_btn w-full-custom"
                            type="button"
                            id="template_save_btn"
                            data-action="{{ route('panel.badge_template_save', ['event_id' => $event->id]) }}">
                        Save Template
                    </button>
                    <br>
                    <button type="submit"
                            class="badge_create_save_btn w-full-custom mt-2"
                            data-action="{{ route('panel.badge_create_template', ['event_id' => $event->id]) }}">
                        Create Badge
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const templateFormButtons = document.querySelectorAll('#template_form button');
            templateFormButtons.forEach(button => {
                button.addEventListener('click', function(event) {
                    const form = document.getElementById('template_form');
                    if (form) {
                        form.action = this.getAttribute('data-action');
                        if (this.type === 'submit') {
                            const firstNameInput = document.getElementById('template_first_name');
                            const lastNameInput = document.getElementById('template_last_name');
                            const eventNameInput = document.getElementById('template_event_name');
                            const countryInput = document.getElementById('template_country');

                            if (!firstNameInput || !lastNameInput || !eventNameInput ||
                                !firstNameInput.value || !lastNameInput.value || !eventNameInput.value) {
                                event.preventDefault();
                                Swal.fire({
                                    icon: 'warning',
                                    title: 'Missing required fields',
                                    text: 'Please fill in all required fields before submitting the form.',
                                });
                            } else {
                                form.submit();
                            }
                        } else {
                            form.submit();
                        }
                    }
                });
            });
        });

        @isset($badge_template_1)
            const badge_template_1_bg = '{{$badge_template_1->bg_url}}';
            const badge_template_1_logo = '{{$badge_template_1->logo_url}}';

            const badgeTemplateColors = {
                1: {
                    firstname_color: '{{$badge_template_1->firstname_color}}',
                    lastname_color: '{{$badge_template_1->lastname_color}}',
                    specialty_color: '{{$badge_template_1->specialty_color}}',
                    event_name_color: '{{$badge_template_1->event_name_color}}',
                    company_color: '{{$badge_template_1->company_color}}',
                    country_color: '{{$badge_template_1->country_color}}',
                }
            };

            // Template 1'i otomatik yükle
            updateTemplate(1, badge_template_1_bg, badge_template_1_logo);
        @endisset

            function updateTemplate(templateId, bgUrl, logoUrl) {
                const templateIdInput = document.querySelector('input[name="template_id"]');
                if (templateIdInput) {
                    templateIdInput.value = templateId;
                }

                const previewContainer = document.querySelector('#preview-container-template');
                if (previewContainer) {
                    previewContainer.style.backgroundImage = 'url("{{ asset('/') }}' + bgUrl + '")';
                    previewContainer.style.backgroundRepeat = 'no-repeat';
                    previewContainer.style.backgroundPosition = 'center center';
                    previewContainer.style.setProperty('background-size', '288px 432px', 'important');

                    const existingLogo = previewContainer.querySelector('.template-logo');
                    if (existingLogo) {
                        existingLogo.remove();
                    }

                    const logoImage = document.createElement('img');
                    logoImage.src = '{{ asset('/') }}' + logoUrl;
                    logoImage.style.position = 'absolute';
                    logoImage.style.top = '50px';
                    logoImage.style.left = '50%';
                    logoImage.style.width = '150px';
                    logoImage.style.height = '60px';
                    logoImage.style.transform = 'translateX(-50%)';
                    logoImage.style.objectFit = 'contain';
                    logoImage.className = 'template-logo';
                    previewContainer.appendChild(logoImage);
                }

                // template degistiginde color inputları guncelle
                if (typeof badgeTemplateColors !== 'undefined' && badgeTemplateColors[templateId]) {
                    const colors = badgeTemplateColors[templateId];

                    // Color input'ları güvenli şekilde güncelle
                    const colorInputs = {
                        'first_name_color': colors.firstname_color,
                        'last_name_color': colors.lastname_color,
                        'specialty_color': colors.specialty_color,
                        'event_name_color': colors.event_name_color,
                        'company_color': colors.company_color,
                        'country_color': colors.country_color
                    };

                    Object.keys(colorInputs).forEach(inputName => {
                        const input = document.querySelector(`input[name="${inputName}"]`);
                        if (input && colorInputs[inputName]) {
                            input.value = colorInputs[inputName];
                        }
                    });

                    // Preview text color'ları güvenli şekilde güncelle
                    const previewElements = {
                        '.template_preview_fn': colors.firstname_color,
                        '.template_preview_ln': colors.lastname_color,
                        '.template_preview_specialty': colors.specialty_color,
                        '.template_preview_event_name': colors.event_name_color,
                        '.template_preview_c': colors.country_color,
                        '.template_preview_company': colors.company_color
                    };

                    Object.keys(previewElements).forEach(selector => {
                        const elements = document.querySelectorAll(selector);
                        if (elements.length > 0 && previewElements[selector]) {
                            elements.forEach(el => el.style.color = previewElements[selector]);
                        }
                    });
                }
            }

    </script>

</div>
