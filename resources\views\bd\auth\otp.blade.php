@extends('layouts.bd-app')

@section('title', 'BD | OTP Verification')
@section('body-class', 'login-body')

@section('content')
<div class="container-fluid vh-100 d-flex align-items-center justify-content-center px-3">
    <div class="w-100 login-container">
        <!-- Main Form -->
        <div id="loginForm">
            <!-- Logo -->
            <div class="text-center mb-5">
                <div class="d-flex align-items-center justify-content-start mb-4">
                    <div class="logo-circle">
                        <img src="{{ asset('assets/bd/images/bd-compass-logo.png') }}" alt="BD Logo" style="width: 200px" class="logo-image">
                    </div>
                </div>
            </div>

            <!-- Form -->
            <form id="otpForm" action="{{ url('/verify-otp') }}" method="POST">
                @csrf
                <input type="hidden" name="email" value="{{ $email }}">

                <div class="mb-4">
                    <label for="otp" class="email-label">
                        Please enter the OTP sent to the email address you provided.
                    </label>
                    <input type="text"
                           class="form-control form-control-lg mt-2 email-input @error('otp') is-invalid @enderror"
                           id="otp"
                           name="otp"
                           value="{{ old('otp') }}"
                           maxlength="6"
                           placeholder="Enter 6-digit OTP"
                           required>
                    @error('otp')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <!-- Buttons -->
                <div class="text-end d-flex justify-content-between align-items-center">
                    <div>
                        <p class="resend-otp mb-2">
                            Did not receive an OTP?<br>
                            <span id="resendBtn" style="cursor: pointer;">Resend</span>
                        </p>
                    </div>
                    <style>
                        .otp-button:hover {
                            color: #ffffff;
                            background-color: #0d79dd;
                            border-color: #04589c;
                        }
                    </style>
                    <button type="submit" class="btn btn-lg px-4 py-2 fw-semibold otp-button">
                        Sign in
                    </button>
                </div>
            </form>
        </div>

        <!-- Loading Screen -->
        <div id="loadingScreen" class="d-none">
            <div class="text-center mb-5">
                <div class="d-flex align-items-center justify-content-start mb-4">
                    <div class="logo-circle">
                         <img src="{{ asset('assets/bd/images/bd-logo.png') }}" alt="BD Logo" class="logo-image">
                    </div>
                </div>
            </div>

            <div class="loading-content">
                <p class="loading-text">
                    Becton Dickinson was founded in 1897 in New York City by Maxwell Becton and Fairleigh S. Dickinson. It later moved its headquarters to New Jersey.
                </p>

                <div class="text-center mt-5">
                    <div class="spinner-border loading-spinner" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@if(session('error'))
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            BDUtils.showToast('{{ session('error') }}', 'error');
        });
    </script>
@endif

@if(session('success'))
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            BDUtils.showToast('{{ session('success') }}', 'success');
        });
    </script>
@endif

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // OTP form submission with loading
    const otpForm = document.getElementById('otpForm');
    const loginForm = document.getElementById('loginForm');
    const loadingScreen = document.getElementById('loadingScreen');

    if (otpForm) {
        otpForm.addEventListener('submit', function(e) {
            // Show loading screen
            if (loginForm && loadingScreen) {
                loginForm.classList.add('d-none');
                loadingScreen.classList.remove('d-none');
            }
        });
    }

    // Resend OTP functionality
    let countdown = 0; // Set to 0 for immediate testing
    const resendBtn = document.getElementById('resendBtn');

    function startCountdown() {
        resendBtn.style.pointerEvents = 'none';
        resendBtn.style.opacity = '0.5';
        resendBtn.textContent = 'Resending...';

        const interval = setInterval(() => {
            countdown--;

            if (countdown <= 0) {
                clearInterval(interval);
                resendBtn.textContent = 'Resend';
                resendBtn.style.pointerEvents = 'auto';
                resendBtn.style.opacity = '1';
                countdown = 30;
            }
        }, 1000);
    }

    // No initial countdown since countdown span is removed

    // Resend OTP click handler
    if (resendBtn) {
        resendBtn.addEventListener('click', function() {
            console.log('Resend button clicked, countdown value:', countdown);
            if (countdown <= 0) {
                console.log('Countdown is 0 or less, sending OTP...');

                // Send AJAX request to resend OTP
                fetch('{{ route("bd.resend-otp") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        email: '{{ $email }}'
                    })
                })
                .then(response => {
                    console.log('Response status:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('Response data:', data);
                    if (data.success) {
                        BDUtils.showToast(data.message || 'OTP resent successfully!', 'success');
                        if (data.otp) {
                            console.log('Development OTP:', data.otp);
                        }
                        startCountdown();
                    } else {
                        BDUtils.showToast(data.message || 'Failed to resend OTP. Please try again.', 'error');
                        if (data.otp) {
                            console.log('Error but OTP generated:', data.otp);
                        }
                    }
                })
                .catch(error => {
                    console.error('Fetch error:', error);
                    BDUtils.showToast('Failed to resend OTP. Please try again.', 'error');
                });
            }
        });
    }
});
</script>
@endpush
@endsection
