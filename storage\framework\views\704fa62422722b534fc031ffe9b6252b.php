<?php $__env->startSection('content'); ?>
    <section>

        <!-- Select2 CSS -->
        <link href="https://cdn.jsdelivr.net/npm/select2@4.0.13/dist/css/select2.min.css" rel="stylesheet" />
        <link rel="stylesheet" href="<?php echo e(asset('css/mdtimepicker.css')); ?>" />
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/mdtimepicker/mdtimepicker.min.css">

        <style>
            .select2-container--default .select2-selection--single {
                border: none !important;
                background-color: rgba(232, 240, 254, 1);
                border: 1px solid rgba(223, 229, 239, 1);
                border-radius: 8px;
            }

            .custom-select {
                appearance: none;
                -webkit-appearance: none;
                -moz-appearance: none;
                background-image: url('<?php echo e(asset('images/down.png')); ?>');
                background-repeat: no-repeat;
                background-position: right 10px center;
                padding-right: 40px;/ cursor: pointer;
            }

            .select2-container .select2-selection--single {
                height: auto;
            }

            .select2-container .select2-selection--single .select2-selection__rendered {
                padding-top: 4px;
                padding-left: 16px;
                padding-right: 10px;
                border: 1px solid rgba(223, 229, 239, 1);
                border-radius: 7px;
            }

            .select2-container--default .select2-selection--single .select2-selection__arrow {
                display: none;
            }

            .select2-selection__clear {
                font-size: 20px;
            }

            /*speakers*/
            .selected-option {
                background-color: transparent !important;
                position: relative;
            }

            .selected-option::after {
                content: "";
                background-image: url('<?php echo e(asset('images/check.png')); ?>');
                background-size: contain;
                width: 16px;
                height: 16px;
                position: absolute;
                right: 10px;
                top: 50%;
                transform: translateY(-50%);
            }

            /* speaker check*/
            .dropdown {
                position: relative;
                font-size: 14px;
                color: #333;
            }

            .dropdown-list {
                padding: 12px;
                background: #fff;
                position: absolute;
                top: 40px;
                left: 2px;
                right: 2px;
                box-shadow: 0 1px 2px 1px rgba(0, 0, 0, .15);
                transform-origin: 50% 0;
                transform: scale(1, 0);
                transition: transform .15s ease-in-out .15s;
                max-height: 66vh;
                overflow-y: scroll;
                z-index: 1;
            }

            .dropdown-option {
                display: flex;
                padding: 8px 12px;
                opacity: 0;
                transition: opacity .15s ease-in-out;
            }

            .dropdown.on .dropdown-list {
                transform: scale(1, 1);
                transition-delay: 0s;
            }

            .dropdown.on .dropdown-option {
                opacity: 1;
                transition-delay: .2s;
            }

            .dropdown [type="checkbox"] {
                position: relative;
                margin-right: 4px;
            }

            #session_speakers_dropdown {
                border-radius: 7px;
            }

            #speaker-select-label {
                font-size: .875rem;
                font-weight: 400;
                line-height: 1.5;
                color: #5a6a85;
            }

            #moderator-select-label {
                font-size: .875rem;
                font-weight: 400;
                line-height: 1.5;
                color: #5a6a85;
            }

            @media (min-width: 768px) {
                .session-div .col-md-3 {
                    flex: 0 0 auto;
                    width: 20%;
                }
            }

            .date-time-row {
                border: 1px solid #b0b0b0;
                padding: 10px;
                margin: 10px 0px;
                border-radius: 10px;
            }

            .timepicker .form-control[readonly] {
                cursor: pointer;
                background-color: #fff;
            }

            /* Enable keyboard input for time holder */
            .mdtp__time_holder > span:not(.mdtp__timedots):not(.mdtp__ampm) {
                cursor: text !important;
                border: 1px solid transparent;
                border-radius: 4px;
                padding: 2px 4px;
                transition: all 0.2s ease;
                min-width: 40px;
                text-align: center;
                outline: none;
            }

            .mdtp__time_holder > span:not(.mdtp__timedots):not(.mdtp__ampm):hover {
                background-color: rgba(255, 255, 255, 0.1);
                border-color: rgba(255, 255, 255, 0.3);
            }

            .mdtp__time_holder > span:not(.mdtp__timedots):not(.mdtp__ampm):focus {
                background-color: rgba(255, 255, 255, 0.2);
                border-color: rgba(255, 255, 255, 0.5);
                box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.2);
            }

            .mdtp__time_holder > span:not(.mdtp__timedots):not(.mdtp__ampm).editing {
                background-color: rgba(255, 255, 255, 0.9);
                color: #333 !important;
                border-color: #007bff;
            }

            /* Tooltip styles */
            .tooltip-container {
                position: relative;
                display: inline-block;
                margin-left: 5px;
            }

            .tooltip-icon {
                width: 16px;
                height: 16px;
                background-color: #6c757d;
                color: white;
                border-radius: 50%;
                display: inline-flex;
                align-items: center;
                justify-content: center;
                font-size: 11px;
                font-weight: bold;
                cursor: help;
                vertical-align: middle;
            }

            .tooltip-text {
                visibility: hidden;
                width: 280px;
                background-color: #333;
                color: #fff;
                text-align: left;
                border-radius: 6px;
                padding: 8px 12px;
                position: absolute;
                z-index: 1000;
                bottom: 125%;
                left: 50%;
                margin-left: -140px;
                opacity: 0;
                transition: opacity 0.3s;
                font-size: 13px;
                line-height: 1.4;
                box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            }

            .tooltip-text::after {
                content: "";
                position: absolute;
                top: 100%;
                left: 50%;
                margin-left: -5px;
                border-width: 5px;
                border-style: solid;
                border-color: #333 transparent transparent transparent;
            }

            .tooltip-container:hover .tooltip-text {
                visibility: visible;
                opacity: 1;
            }
        </style>

        <div class="row">
            <div class="col-md-12 col-lg-12 d-flex align-items-stretch">
                <div class="card w-100">
                    <div class="card-body">

                        <div class="event_details_container row">
                            <div class="col-12 d-flex justify-content-between align-items-center flex-wrap">
                                <h5 class="mb-3 event_title fw-bolder">
                                    Event Details
                                </h5>
                                <div class="d-flex flex-row align-items-center">
                                    <p class="pe-3 mb-0">Status: </p>
                                    <div class="btn-group">
                                        <?php if($event->status == 2): ?>
                                            <button class="btn status dropdown-toggle pass" type="button"
                                                data-bs-toggle="dropdown" aria-expanded="false">
                                                Draft
                                            </button>
                                        <?php else: ?>
                                            <?php if(request()->is('admin/event-detail/*') || request()->is('admin/event/*')): ?>
                                                <?php if(
                                                    $event->event_dates()->orderBy('event_date', 'ASC')->first() &&
                                                        $event->event_dates()->orderBy('event_date', 'ASC')->first()->event_date >= date('Y-m-d')): ?>
                                                    <button class="btn status dropdown-toggle active" type="button"
                                                        data-bs-toggle="dropdown" aria-expanded="false">
                                                        Active
                                                    </button>
                                                <?php else: ?>
                                                    <button class="btn status dropdown-toggle pass" type="button"
                                                        data-bs-toggle="dropdown" aria-expanded="false">
                                                        Past
                                                    </button>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <button class="btn status dropdown-toggle active" type="button"
                                                    data-bs-toggle="dropdown" aria-expanded="false">
                                                    Active
                                                </button>
                                            <?php endif; ?>

                                        <?php endif; ?>


                                    </div>
                                </div>
                            </div>
                            <hr class="my-3">
                        </div>
                        <?php echo $__env->make('layouts.page_navigation', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                        <form action="<?php echo e(url('/admin/update-event')); ?>" id="event_form" method="POST"
                            enctype="multipart/form-data">
                            <?php echo csrf_field(); ?>


                            <div class="event_details_container row">

                                <?php if(request()->is('admin/event-detail/*') || request()->is('admin/new-event')): ?>
                                    <div class="col-12">
                                        <div class="d-flex flex-wrap gap-3">
                                            <?php
                                                $header_toggles = [
                                                    'User Check-in' => 'user_check_in',
                                                    /* 'Feedback Survey' => 'feedback_survey', */
                                                    'Badge Creation' => 'badge_creation',
                                                    'Attendees Info' => 'attendees_info',
                                                    /* 'Interactive Tools' => 'interactive_tools',*/
                                                ];
                                            ?>
                                            <?php $__currentLoopData = $header_toggles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $header_toggle => $id): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div class="d-inline-flex form-check form-switch">
                                                    <label class="form-check-label"
                                                        for="<?php echo e($id); ?>"><?php echo e($header_toggle); ?></label>
                                                    <input class="form-check-input ms-2" type="checkbox"
                                                        id="<?php echo e($id); ?>" name="<?php echo e($id); ?>"
                                                        <?php if((isset($event) && $event->$id == 1) || (isset($eventClone) && $eventClone->$id == 1)): ?> checked <?php endif; ?>>
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                    </div>
                                <?php endif; ?>
                                <hr class="my-3">
                            </div>



                            <input type="hidden" name="event_id" value="<?php echo e($event->id); ?>">
                            <div class="d-sm-flex align-items-center justify-content-between mb-3">

                                <?php if($errors->any()): ?>
                                    <div class="alert alert-danger">
                                        <ul>
                                            <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <li><?php echo e($error); ?></li>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </ul>
                                    </div>
                                <?php endif; ?>

                                <div>
                                    <h5 class="mb-0 fs-5">Event Info</h5>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="form-group mb-3">
                                                <label for="marketo_event_id">
                                                    Niceye Event ID
                                                    <small class="required_input_thing">*</small>
                                                </label>
                                                <input type="text" class="form-control" name="marketo_event_id"
                                                    id="marketo_event_id" value="<?php echo e($event->marketo_event_id); ?>"
                                                    data-label="Niceye Event ID" readonly>
                                            </div>
                                        </div>

                                        <div class="col-md-8">
                                            <div class="form-group mb-3">
                                                <label for="event_title">
                                                    Event Title
                                                    <small class="required_input_thing">*</small>
                                                </label>
                                                <input type="text" class="form-control" id="event_title"
                                                    name="event_title" value="<?php echo e($event->event_title); ?>"
                                                    data-label="Event Title">
                                            </div>
                                        </div>

                                        <div class="col-md-12">
                                            <div class="form-group mb-3">
                                                <label for="event_description">
                                                    Event Description
                                                    <small class="required_input_thing">*</small>
                                                </label>
                                                <input type="text" class="form-control" id="event_description"
                                                    name="event_description" value="<?php echo e($event->event_description); ?>"
                                                    data-label="Event Description">
                                            </div>
                                        </div>

                                        <div class="col-md-12">
                                            <div class="form-group mb-3">
                                                <label for="contact_email">
                                                    Organizer's Email
                                                    <small class="required_input_thing">*</small>
                                                    <span class="tooltip-container">
                                                        <span class="tooltip-icon">i</span>
                                                        <span class="tooltip-text">This is the BD associate who is initiating this event.</span>
                                                    </span>
                                                </label>
                                                <input type="text" class="form-control" id="contact_email"
                                                    name="contact_email" value="<?php echo e($event->contact_email); ?>"
                                                    data-label="Contact Email">
                                            </div>
                                        </div>

                                        <div class="col-md-12">
                                            <div class="form-group mb-3 d-flex flex-column  position-relative">
                                                <label for="duration_of_event">
                                                    Duration of Event
                                                    <small class="required_input_thing">*</small>
                                                </label>
                                                <select class="form-control custom-select" id="duration_of_event"
                                                    name="duration_pf_event" data-label="Duration of Event">
                                                    <option value="1"
                                                        <?php if($event->duration_pf_event == 1): ?> selected <?php endif; ?>>Single Day
                                                    </option>
                                                    <option value="2"
                                                        <?php if($event->duration_pf_event == 2): ?> selected <?php endif; ?>>Multiple Day
                                                    </option>
                                                </select>
                                            </div>
                                        </div>
                                        <div id="date-time-container">

                                            <?php if($event->event_dates): ?>
                                                <?php $__currentLoopData = $event->event_dates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $event_date): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <div class="row date-time-row" id="date_row_<?php echo e($event_date->id); ?>">
                                                        <div class="col-md-4 form-group">
                                                            <label for="event_date">
                                                                Date
                                                                <small class="required_input_thing">*</small>
                                                            </label>
                                                            <div class="input-group mb-3">
                                                                <input type="text"
                                                                    class="form-control singledate form-control event_date_overview"
                                                                    id="event_date_<?php echo e($event_date->id); ?>"
                                                                    name="event_date[]" autocomplete = "off"
                                                                    data-label="Date" onchange="set_single_date(this)" />

                                                                <span class="input-group-text date-icon">
                                                                    <i class="ti ti-calendar fs-5"></i>
                                                                </span>
                                                            </div>
                                                        </div>
                                                        

                                                        <div class="col-md-4">
                                                            <div class="form-group mb-3">
                                                                <label for="event_time">
                                                                    Time
                                                                    <small class="required_input_thing">*</small>
                                                                </label>
                                                                <div class="input-group mb-3">
                                                                    <input type="text"
                                                                        class="form-control timepicker event_time_overview"
                                                                        id="event_time_<?php echo e($event_date->id); ?>"
                                                                        name="event_time[]" autocomplete = "off"
                                                                        data-label="Time" />

                                                                    <span class="input-group-text time-icon">
                                                                        <svg xmlns="http://www.w3.org/2000/svg"
                                                                            width="16" height="16"
                                                                            fill="currentColor" class="bi bi-clock"
                                                                            viewBox="0 0 16 16">
                                                                            <path
                                                                                d="M8 3.5a.5.5 0 0 0-1 0V9a.5.5 0 0 0 .252.434l3.5 2a.5.5 0 0 0 .496-.868L8 8.71z" />
                                                                            <path
                                                                                d="M8 16A8 8 0 1 0 8 0a8 8 0 0 0 0 16m7-8A7 7 0 1 1 1 8a7 7 0 0 1 14 0" />
                                                                        </svg>
                                                                    </span>
                                                                </div>
                                                            </div>
                                                        </div>


                                                        <div class="col-md-4">
                                                            <div
                                                                class="form-group mb-3 d-flex flex-column  position-relative">
                                                                <label for="event_date_timezone">
                                                                    Timezone
                                                                    <small class="required_input_thing">*</small>
                                                                </label>
                                                                <select class="form-control custom-select"
                                                                    id="event_date_timezone" name="event_date_timezones[]"
                                                                    data-label="Timezone">
                                                                    <?php if($time_zones): ?>
                                                                        <?php $__currentLoopData = $time_zones; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                            <option value="<?php echo e($item->time_zone); ?>"
                                                                                <?php if($item->time_zone == $event_date->event_date_timezone): ?> selected <?php endif; ?>>
                                                                                <?php echo e($item->time_zone); ?></option>
                                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                    <?php endif; ?>
                                                                </select>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-8">
                                                            <div class="form-group mb-3">
                                                                <label for="event_location">
                                                                    Event Location
                                                                    <small class="required_input_thing">*</small>
                                                                </label>
                                                                <input type="text" class="form-control"
                                                                    id="event_location" name="event_location[]"
                                                                    value="<?php echo e($event_date->event_location); ?>"
                                                                    data-label="Event Location">
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div
                                                                class="form-group mb-3 d-flex flex-column  position-relative">
                                                                <label for="event_country">
                                                                    Country
                                                                    <small class="required_input_thing">*</small>
                                                                </label>
                                                                <select class="form-control custom-select"
                                                                    id="event_country" name="event_country[]"
                                                                    data-label="Country">
                                                                    <?php if($countries): ?>
                                                                        <?php $__currentLoopData = $countries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                            <option value="<?php echo e($item->country_name); ?>"
                                                                                <?php if($item->country_name == $event_date->event_country): ?> selected <?php endif; ?>>
                                                                                <?php echo e($item->country_name); ?></option>
                                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                    <?php endif; ?>
                                                                </select>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-12">
                                                            <div class="form-group mb-3">
                                                                <label for="event_location_url">
                                                                    Event Location URL
                                                                    <small class="required_input_thing">*</small>
                                                                </label>
                                                                <input type="text" class="form-control"
                                                                    id="event_location_url" name="event_location_url[]"
                                                                    value="<?php echo e($event_date->event_location_url); ?>"
                                                                    data-label="Event Location URL">
                                                            </div>
                                                        </div>
                                                        <?php if($key != 0): ?>
                                                            <div class="col-md-4">
                                                                <button type="button"
                                                                    onclick="remove_date_row(<?php echo e($event_date->id); ?>)"
                                                                    class="btn btn-danger remove-btn">Delete</button>
                                                            </div>
                                                        <?php endif; ?>

                                                    </div>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            <?php endif; ?>

                                        </div>

                                        <div class="d-flex justify-content-end">
                                            <a href="javascript:void(0);" id="new_date_button" onclick="add_new_date();"
                                                style="color: #fff;padding: 5px 20px;border-radius: 10px; <?php if($event->duration_pf_event == 1): ?> display:none; <?php else: ?> display: block; <?php endif; ?>">Add Next Day Details</a>
                                        </div>

                                        <div class="input-group flex-nowrap">
                                            <div class="form-group mb-3 w-100">
                                                <label class="fw-bold mb-2" for="event_banner"
                                                    style="font-weight: 700 !important">Upload Banner</label>
                                                <div class="border rounded position-relative"
                                                    style="border: 3px solid rgba(178, 199, 237, 1) !important; border-style: dashed !important; padding: 40px; text-align: center;">
                                                    <div id="preview-container" class="mb-4">
                                                        <?php if($event->event_banner): ?>
                                                            <img id="preview-img"
                                                                src="<?php echo e(asset(env('STORAGE_PATH') . '/event_banners/' . $event->event_banner)); ?>"
                                                                class="img-fluid" alt="Banner Preview"
                                                                style="max-width: 100%; display: inline;">
                                                        <?php else: ?>
                                                            <img id="preview-img"
                                                                src="<?php echo e(asset('images/banner_upload.png')); ?>"
                                                                class="img-fluid" alt="Banner Preview"
                                                                style="max-width: 100%; display: inline;">
                                                        <?php endif; ?>
                                                    </div>

                                                    <label for="event_banner"
                                                        class="d-inline-block px-4 py-2 add_image_btn"
                                                        style="color: #fff; border: none; border-radius: 8px">
                                                        Add image
                                                    </label>
                                                    <p class="mt-2 mb-3 text-muted"
                                                        style="color: rgba(0, 0, 0, 1) !important;">or drop an image to
                                                        upload</p>
                                                    <input type="file" id="event_banner" name="event_banner"
                                                        class="form-control d-none" accept="image/*">

                                                    <small class="text-muted" style="color: rgba(0, 0, 0, 1) !important;">
                                                        <strong>Max file size:</strong> 1 MB,
                                                        <strong>File formats:</strong> JPEG, PNG, GIF, Recommended
                                                        dimensions: 694x300 px
                                                    </small>
                                                </div>
                                            </div>
                                        </div>

                                    </div>

                                </div>
                                <div class="col-md-6">
                                    <div class="row">

                                        <div class="col-md-4">
                                            <div class="form-group mb-3">
                                                <label for="business_unit">
                                                    Business Unit
                                                    <small class="required_input_thing">*</small>
                                                </label>
                                                <select name="business_unit" id="business_unit"
                                                    class="form-control custom-select" data-label="Business Unit">
                                                    <?php $__currentLoopData = $business_units; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $business_unit): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <option value="<?php echo e($business_unit->name); ?>"
                                                            <?php if($event->business_unit == $business_unit->name): ?> selected <?php endif; ?>>
                                                            <?php echo e($business_unit->name); ?></option>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </select>
                                            </div>
                                        </div>

                                        <div class="col-md-4">
                                            <div class="form-group mb-3">
                                                <label for="smart_list_id">
                                                    Marketo List ID
                                                    <span class="tooltip-container">
                                                        <span class="tooltip-icon">i</span>
                                                        <span class="tooltip-text">This is the unique identifier for the Marketo static list used to sync event registrants.</span>
                                                    </span>
                                                </label>
                                                <input type="number" class="form-control" id="smart_list_id"
                                                    value="<?php echo e($event->smart_list_id); ?>" name="smart_list_id"
                                                    autocomplete="off" min="0">
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group mb-3">
                                                <label for="attendee_quota">
                                                    Attendee Quota
                                                    <small class="required_input_thing">*</small>
                                                </label>
                                                <input type="number" class="form-control" id="attendee_quota"
                                                    value="<?php echo e($event->attendee_quota); ?>" name="attendee_quota"
                                                    data-label="Attendee Quota" min="0">
                                            </div>
                                        </div>
                                        

                                        <div class="col-md-6">
                                            <div class="form-group mb-3 d-flex flex-column position-relative">
                                                <label for="moderators_count">Moderators<small
                                                        class="required_input_thing">*</small></label>
                                                <select class="form-control custom-select" id="moderators_count"
                                                    onchange="add_moderator()" name="moderators_count">
                                                    <?php for($i = 0; $i <= 10; $i++): ?>
                                                        <option value="<?php echo e($i); ?>"
                                                                <?php if($event->moderators_count == $i): ?> selected <?php endif; ?>
                                                                <?php if($i == 0): ?> disabled <?php endif; ?>>
                                                            <?php echo e($i == 0 ? 'None' : $i . ' person' . ($i > 1 ? 's' : '')); ?>

                                                        </option>
                                                    <?php endfor; ?>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group mb-3 d-flex flex-column position-relative">
                                                <label for="speakers_count">Speakers<small
                                                        class="required_input_thing">*</small></label>
                                                <select class="form-control custom-select" id="speakers_count"
                                                    name="speakers_count" onchange="add_speakers()">
                                                    <?php for($i = 0; $i <= 10; $i++): ?>
                                                        <option value="<?php echo e($i); ?>"
                                                                <?php if($event->speakers_count == $i): ?> selected <?php endif; ?>
                                                                <?php if($i == 0): ?> disabled <?php endif; ?>>
                                                            <?php echo e($i == 0 ? 'None' : $i . ' person' . ($i > 1 ? 's' : '')); ?>

                                                        </option>
                                                    <?php endfor; ?>
                                                </select>
                                            </div>
                                        </div>
                                        <input type="hidden" name="moderator_selected_count"
                                            value="<?php echo e($event->moderator_speaker->where('type', 2)->count()); ?>"
                                            id="moderator_selected_count">
                                        <input type="hidden" name="speaker_selected_count"
                                            value="<?php echo e($event->moderator_speaker->where('type', 1)->count()); ?>"
                                            id="speaker_selected_count">
                                        <div class="col-md-6">
                                            <div class="form-group mb-3">
                                                <div class="d-flex flex-row gap-2" id="moderator_div">
                                                    <?php if($event->moderator_speaker): ?>
                                                        <?php $count = 1; ?>
                                                        <?php $__currentLoopData = $event->moderator_speaker; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <?php if($item->type == 2): ?>
                                                                
                                                                <div
                                                                    class="d-flex flex-column col-md-6 div-count-<?php echo e($key + 1); ?>">
                                                                    <div class="pb-2"><span
                                                                            class="d-flex justify-content-center align-items-center w-100">
                                                                            <label
                                                                                for="moderator_banner<?php echo e($count); ?>"
                                                                                class="d-flex justify-content-center align-items-center w-100"
                                                                                style="background-color:rgba(232, 240, 254, 1); border: 1px solid rgba(223, 229, 239, 1); height: 100px; border-radius: 8px; cursor: pointer;">
                                                                                <img id="moderator-img<?php echo e($count); ?>"
                                                                                    src="<?php echo e(asset(env('STORAGE_PATH') . '/evet_moderator_moderator/' . $item->banner)); ?>"
                                                                                    alt="icon"
                                                                                    style="max-width: 100%; max-height: 100%; object-fit: contain;" />
                                                                            </label>
                                                                        </span>
                                                                    </div> <br>
                                                                    <input type="file"
                                                                        id="moderator_banner<?php echo e($count); ?>"
                                                                        name="moderator_banner[]"
                                                                        data-label="Moderator Banner"
                                                                        class="hidden_banner_input" accept="image/*">
                                                                    <input type="hidden"
                                                                        id="moderator_banner_path<?php echo e($count); ?>"
                                                                        value="<?php echo e(asset(env('STORAGE_PATH') . '/evet_moderator_moderator/' . $item->banner)); ?>">

                                                                    <div
                                                                        class="d-flex justify-content-between align-items-center mb-2">
                                                                        <div class="d-flex flex-col w-100">
                                                                            <input type="text" class="line-input"
                                                                                name="moderator_name[]"
                                                                                data-label="Moderator Name Surname"
                                                                                value="<?php echo e($item->name); ?>"
                                                                                style="border:none; border-bottom: 1px solid #000"
                                                                                Placeholder="Name Surname *" />
                                                                        </div>
                                                                        <img src="<?php echo e(asset('images/new-event-pencil.png')); ?>"
                                                                            class="pencil-icon" alt="icon" />
                                                                    </div>
                                                                    <div
                                                                        class="d-flex justify-content-between align-items-center">
                                                                        <div class="d-flex flex-col w-100">
                                                                            <input type="text" class="line-subinput"
                                                                                name="moderator_title[]"
                                                                                data-label="Moderator Title"
                                                                                value="<?php echo e($item->title); ?>"
                                                                                style="border:none; border-bottom: 1px solid #000"
                                                                                Placeholder="Title *" />
                                                                        </div>
                                                                        <img src="<?php echo e(asset('images/new-event-pencil.png')); ?>"
                                                                            class="pencil-icon" alt="icon" />
                                                                    </div>
                                                                    <div
                                                                        class="d-flex justify-content-between align-items-center">
                                                                        <div class="d-flex flex-col w-100">
                                                                            <input type="text" class="line-subinput"
                                                                                name="moderator_description[]"
                                                                                data-label="Moderator Description"
                                                                                value="<?php echo e($item->description); ?>"
                                                                                style="border:none; border-bottom: 1px solid #000; cursor: pointer;"
                                                                                placeholder="Click to add description"
                                                                                readonly />
                                                                        </div>
                                                                        <img src="<?php echo e(asset('images/new-event-pencil.png')); ?>"
                                                                            class="pencil-icon" alt="icon" />
                                                                    </div>
                                                                </div>
                                                                <?php $count++; ?>
                                                            <?php endif; ?>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    <?php endif; ?>

                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group mb-3">
                                                <div class="d-flex flex-row gap-2" id="speakers_div">
                                                    <?php if($event->moderator_speaker): ?>
                                                        <?php $count = 1; ?>
                                                        <?php $__currentLoopData = $event->moderator_speaker; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <?php if($item->type == 1): ?>
                                                                
                                                                <div
                                                                    class="d-flex flex-column col-md-6 div-speaker-count<?php echo e($key + 1); ?>">
                                                                    <div class="pb-2"><span
                                                                            class="d-flex justify-content-center align-items-center w-100">
                                                                            <label for="speaker_banner<?php echo e($count); ?>"
                                                                                class="d-flex justify-content-center align-items-center w-100"
                                                                                style="background-color:rgba(232, 240, 254, 1); border: 1px solid rgba(223, 229, 239, 1); height: 100px; border-radius: 8px; cursor: pointer;">
                                                                                <img id="speaker-img<?php echo e($count); ?>"
                                                                                    src="<?php echo e(asset(env('STORAGE_PATH') . '/evet_moderator_speaker/' . $item->banner)); ?>"
                                                                                    alt="icon"
                                                                                    style="max-width: 100%; max-height: 100%; object-fit: contain;" />
                                                                            </label>
                                                                        </span>
                                                                    </div> <br>
                                                                    <input type="file"
                                                                        id="speaker_banner<?php echo e($count); ?>"
                                                                        name="speaker_banner[]"
                                                                        data-label="Speaker Banner"
                                                                        class="hidden_banner_input" accept="image/*">
                                                                    <input type="hidden"
                                                                        id="speaker_banner_path<?php echo e($count); ?>"
                                                                        value="<?php echo e(asset(env('STORAGE_PATH') . '/evet_moderator_speaker/' . $item->banner)); ?>">

                                                                    <div
                                                                        class="d-flex justify-content-between align-items-center mb-2">
                                                                        <div class="d-flex flex-col w-100">
                                                                            <input type="text" class="line-input"
                                                                                style="border:none; border-bottom: 1px solid #000"
                                                                                name="speaker_name[]"
                                                                                data-label="Speaker Name"
                                                                                value="<?php echo e($item->name); ?>"
                                                                                Placeholder="Name Surname *" />
                                                                        </div>
                                                                        <img src="<?php echo e(asset('images/new-event-pencil.png')); ?>"
                                                                            class="pencil-icon" alt="icon" />
                                                                    </div>
                                                                    <div
                                                                        class="d-flex justify-content-between align-items-center">
                                                                        <div class="d-flex flex-col w-100">
                                                                            <input type="text" class="line-subinput"
                                                                                style="border:none; border-bottom: 1px solid #000"
                                                                                name="speaker_title[]"
                                                                                data-label="Speaker Title"
                                                                                value="<?php echo e($item->title); ?>"
                                                                                Placeholder="Title *" />
                                                                        </div>
                                                                        <img src="<?php echo e(asset('images/new-event-pencil.png')); ?>"
                                                                            class="pencil-icon" alt="icon" />
                                                                    </div>
                                                                    <div
                                                                        class="d-flex justify-content-between align-items-center">
                                                                        <div class="d-flex flex-col w-100">
                                                                            <input type="text" class="line-subinput"
                                                                                name="speaker_description[]"
                                                                                data-label="Speaker Description"
                                                                                value="<?php echo e($item->description); ?>"
                                                                                style="border:none; border-bottom: 1px solid #000; cursor: pointer;"
                                                                                placeholder="Click to add description"
                                                                                readonly />
                                                                        </div>
                                                                        <img src="<?php echo e(asset('images/new-event-pencil.png')); ?>"
                                                                            class="pencil-icon" alt="icon" />
                                                                    </div>
                                                                </div>
                                                                <?php $count++; ?>
                                                            <?php endif; ?>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>


                                    </div>
                                </div>

                                <div class="col-12 d-flex justify-content-end">
                                    <button onclick="set_moderator_speakers()" type="button" class="done_btn"
                                        style="color: #fff; padding: 8px 40px; border-radius: 8px; border: none;">
                                        Done
                                    </button>
                                </div>
                            </div>
                            <hr>

                            <div class="row session-div d-flex">
                                <div class="d-sm-flex align-items-center justify-content-between mb-3">
                                    <div>
                                        <h5 class="mb-0 fs-5">
                                            Add Session
                                            <div class="tooltip-container">
                                                <span class="tooltip-icon">i</span>
                                                <span class="tooltip-text">This will be the individual topics and/or sessions that will happen during the event.</span>
                                            </div>
                                        </h5>
                                    </div>
                                </div>

                                <div class="col-md-3" id="session_dates_div"
                                    <?php if($event->duration_pf_event == 1): ?> style="display:none;" <?php endif; ?>>
                                    <div class="form-group mb-3">
                                        <label for="session_speakers">Session Dates</label>
                                        <select class="form-control custom-select" id="session_dates">
                                            <?php if($event->event_dates): ?>
                                                <?php $__currentLoopData = $event->event_dates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $event_date): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option
                                                        value="<?php echo e(\Carbon\Carbon::parse($event_date->event_date)->format('d/m/Y')); ?>">
                                                        <?php echo e(\Carbon\Carbon::parse($event_date->event_date)->format('d/m/Y')); ?>

                                                    </option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            <?php endif; ?>
                                        </select>
                                        <input type="hidden" name="single_session_date" id="single_session_dates">
                                    </div>
                                </div>
                                

                                <div class="col-md-3">
                                    <div class="form-group mb-3">
                                        <label for="session_time_duration">Session Time/Duration</label>
                                        
                                        <div class="form-group mb-3">
                                            <div class="input-group mb-3">
                                                <input type="text" class="form-control timepicker"
                                                    id="session_time_duration" name="session_time_duration" />

                                                <span class="input-group-text time-icon-session">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                        fill="currentColor" class="bi bi-clock" viewBox="0 0 16 16">
                                                        <path
                                                            d="M8 3.5a.5.5 0 0 0-1 0V9a.5.5 0 0 0 .252.434l3.5 2a.5.5 0 0 0 .496-.868L8 8.71z" />
                                                        <path
                                                            d="M8 16A8 8 0 1 0 8 0a8 8 0 0 0 0 16m7-8A7 7 0 1 1 1 8a7 7 0 0 1 14 0" />
                                                    </svg>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-2">
                                    <div class="form-group mb-3">
                                        <label for="session_title">Session Title</label>
                                        <input type="text" class="form-control" id="session_title">
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-group mb-3">
                                        <label for="session_speakers">Speakers</label>

                                        <div class="dropdown" data-control="checkbox-dropdown"
                                            id="session_speakers_dropdown">
                                            <label class="form-control custom-select dropdown-label"
                                                id="speaker-select-label">Speakers</label>
                                            <div class="dropdown-list">
                                                <div id="session_speakers_list">
                                                    <?php if($event->moderator_speaker): ?>
                                                        <?php $__currentLoopData = $event->moderator_speaker; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <?php if($item->type == 1): ?>
                                                                <label class="dropdown-option">
                                                                    <input type="checkbox" name="dropdown-group"
                                                                        value="<?php echo e($item->name); ?>">
                                                                    <?php echo e($item->name); ?>

                                                                </label>
                                                            <?php endif; ?>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-group mb-3">
                                        <label for="session_speakers">Moderators</label>

                                        <div class="dropdown" data-control="checkbox-dropdown-moderator"
                                            id="session_moderators_dropdown">
                                            <label class="form-control custom-select dropdown-label"
                                                id="moderator-select-label">Moderators</label>
                                            <div class="dropdown-list">
                                                <div id="session_moderators_list">
                                                    <?php if($event->moderator_speaker): ?>
                                                        <?php $__currentLoopData = $event->moderator_speaker; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <?php if($item->type == 2): ?>
                                                                <label class="dropdown-option">
                                                                    <input type="checkbox" name="dropdown-group"
                                                                        value="<?php echo e($item->name); ?>">
                                                                    <?php echo e($item->name); ?>

                                                                </label>
                                                            <?php endif; ?>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                </div>

                                <div class="col-auto d-flex align-items-center">
                                    <div class="form-group mt-2">
                                        <button id="session_button" type="button" href="javascript:void(0);"
                                            onclick="add_session()"
                                            class="btn d-flex align-items-center justify-content-center"
                                            style="width: 40px; height: 40px; background-color: #f26925; border: none; font-size: 24px; font-weight: bold; color: white; padding: 0;">
                                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" width="50%"
                                                height="50%" fill="white">
                                                <path
                                                    d="M433.9 129.9l-83.9-83.9A48 48 0 0 0 316.1 32H48C21.5 32 0 53.5 0 80v352c0 26.5 21.5 48 48 48h352c26.5 0 48-21.5 48-48V163.9a48 48 0 0 0 -14.1-33.9zM224 416c-35.3 0-64-28.7-64-64 0-35.3 28.7-64 64-64s64 28.7 64 64c0 35.3-28.7 64-64 64zm96-304.5V212c0 6.6-5.4 12-12 12H76c-6.6 0-12-5.4-12-12V108c0-6.6 5.4-12 12-12h228.5c3.2 0 6.2 1.3 8.5 3.5l3.5 3.5A12 12 0 0 1 320 111.5z" />
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <hr>

                            <div class="row">
                                <div class="d-sm-flex align-items-center justify-content-between mb-3">
                                    <div>
                                        <h5 class="mb-0 fs-5">Session Details</h5>
                                    </div>
                                </div>
                                <input type="hidden" id="session_time_duration_input"
                                    name="session_time_duration_input">
                                <input type="hidden" id="session_title_input" name="session_title_input">
                                <input type="hidden" id="session_speakers_input" name="session_speakers_input">
                                <input type="hidden" id="session_moderators_input" name="session_moderators_input">
                                <input type="hidden" id="session_dates_input" name="session_dates_input">
                                <div class="table-responsive rounded-2 mb-4">
                                    <table id="sessions_table"
                                        class="table text-nowrap customize-table mb-0 align-middle">
                                        <thead class="text-dark fs-4">
                                            <tr>
                                                <th>
                                                    <h6 class="fs-4 fw-semibold mb-0">Session Dates</h6>
                                                </th>
                                                <th>
                                                    <h6 class="fs-4 fw-semibold mb-0">Session Time/Duration</h6>
                                                </th>
                                                <th>
                                                    <h6 class="fs-4 fw-semibold mb-0">Session Title</h6>
                                                </th>
                                                <th>
                                                    <h6 class="fs-4 fw-semibold mb-0">Speakers</h6>
                                                </th>
                                                <th>
                                                    <h6 class="fs-4 fw-semibold mb-0">Moderators</h6>
                                                </th>
                                                <th>
                                                    <h6 class="fs-4 fw-semibold mb-0">Action</h6>
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if($event->sessions): ?>
                                                <?php
                                                    $eventSessions = $event->sessions->sortBy(function ($session) {
                                                        try {
                                                            $date = \Carbon\Carbon::createFromFormat('d/m/Y', $session->session_dates);
                                                            [$start, $end] = array_map('trim', explode('-', $session->session_time_duration));
                                                            $startTime = \Carbon\Carbon::createFromFormat('g:i A', $start);
                                                            $endTime = \Carbon\Carbon::createFromFormat('g:i A', $end);
                                                            return [$date->timestamp, $startTime->timestamp, $endTime->timestamp];
                                                        } catch (\Exception $e) {
                                                            // Fallback to current timestamp if parsing fails
                                                            return [now()->timestamp, 0, 0];
                                                        }
                                                    });
                                                ?>
                                                <?php $__currentLoopData = $eventSessions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $session): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <tr>
                                                        <td><?php echo e($session->session_dates); ?></td>
                                                        <td><?php echo e($session->session_time_duration); ?></td>
                                                        <td><?php echo e($session->session_title); ?></td>
                                                        <td><?php echo str_replace('-', '<br>', $session->session_speakers); ?></td>
                                                        <td><?php echo str_replace('-', '<br>', $session->session_moderators); ?></td>
                                                        <td>
                                                            <button type="button" class="btn btn-primary ms-2 edit_btn"
                                                                onclick="editSession(this)"><i
                                                                    class="ti ti-pencil"></i></button>
                                                            <button type="button" class="btn btn-danger delete_btn"
                                                                onclick="deleteSession(this, <?php echo e($session->id); ?>, '<?php echo e($session->session_title); ?>')"><i
                                                                    class="ti ti-trash"></i></button>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <hr>

                            <div class="row" style="padding-bottom: 10px">
                                <div class="d-sm-flex align-items-center justify-content-between mb-3">
                                    <div>
                                        <h5 class="mb-0 fs-5">
                                            Add Document
                                            <div class="tooltip-container">
                                                <span class="tooltip-icon">i</span>
                                                <span class="tooltip-text">Provide documents, if any, for either the whole event or individual sessions.</span>
                                            </div>
                                        </h5>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group mb-3">
                                        <label for="document_name">Documents Name</label>
                                        <input type="text" class="form-control" name="document_name"
                                            id="document_name">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group mb-3 d-flex flex-column position-relative">
                                        <label for="document_type">Document Type</label>
                                        <select class="form-control custom-select" id="document_type"
                                            name="document_type">
                                            <option>PDF</option>
                                            <option>WORD</option>
                                            <option>PPT</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group mb-3 relative">
                                        <label for="uploaded_document">Upload Document</label>
                                        <div class="form-group mb-3">
                                            <label class="upload-cont form-control">
                                                <img src="<?php echo e(asset('images/upload2.png')); ?>" />
                                                <span style="color: rgba(43, 54, 71, 1):; font-weight: 400;">Upload
                                                    Document</span>
                                                <input type="file" name="uploaded_document" id="uploaded_document">
                                            </label>
                                            <span id="file_name"
                                                style="visibility: hidden; position: absolute; margin-bottom: 5px"
                                                class="mt-1"></span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-group mb-3 d-flex flex-column position-relative">

                                        <label for="selected_session">Select Session</label>
                                        <select class="form-control custom-select" id="selected_session"
                                            name="selected_session">
                                                <option value="Entire Event">Entire Event</option>
                                            <?php if($event->sessions): ?>
                                                <?php $__currentLoopData = $event->sessions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $session): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e($session->session_title); ?>">
                                                        <?php echo e($session->session_title); ?></option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            <?php endif; ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-1 d-flex align-items-center  mt-2">
                                    <button type="button" onclick="set_upload_data();" id="document_button"
                                        class="btn d-flex align-items-center justify-content-center" aria-label="Add"
                                        style="width: 40px; height: 40px; background-color: #f26925; border: none; font-size: 24px; font-weight: bold; color: white; padding: 0;">
                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" width="50%"
                                            height="50%" fill="white">
                                            <path
                                                d="M433.9 129.9l-83.9-83.9A48 48 0 0 0 316.1 32H48C21.5 32 0 53.5 0 80v352c0 26.5 21.5 48 48 48h352c26.5 0 48-21.5 48-48V163.9a48 48 0 0 0 -14.1-33.9zM224 416c-35.3 0-64-28.7-64-64 0-35.3 28.7-64 64-64s64 28.7 64 64c0 35.3-28.7 64-64 64zm96-304.5V212c0 6.6-5.4 12-12 12H76c-6.6 0-12-5.4-12-12V108c0-6.6 5.4-12 12-12h228.5c3.2 0 6.2 1.3 8.5 3.5l3.5 3.5A12 12 0 0 1 320 111.5z" />
                                        </svg>
                                    </button>
                                </div>
                                <input type="hidden" name="multiple_document_name" id="multiple_document_name">
                                <input type="hidden" name="multiple_document_type" id="multiple_document_type">
                                <input type="hidden" name="multiple_selected_session" id="multiple_selected_session">
                                <input type="file" name="duplicated_document[]" id="duplicated_document"
                                    style="display:none;" multiple>

                            </div>

                            <hr>

                            <div class="row">
                                <div class="d-sm-flex align-items-center justify-content-between mb-3">
                                    <div>
                                        <h5 class="mb-0 fs-5">Documents Uploaded</h5>
                                    </div>
                                </div>



                                <div class="table-responsive rounded-2 mb-4">
                                    <table id="documents_table"
                                        class="table text-nowrap customize-table mb-0 align-middle">
                                        <thead class="text-dark fs-4">
                                            <tr>
                                                <th>
                                                    <h6 class="fs-4 fw-semibold mb-0">Document Type</h6>
                                                </th>
                                                <th>
                                                    <h6 class="fs-4 fw-semibold mb-0">Document Name</h6>
                                                </th>
                                                <th>
                                                    <h6 class="fs-4 fw-semibold mb-0">Session</h6>
                                                </th>
                                                <th>
                                                    <h6 class="fs-4 fw-semibold mb-0">Action</h6>
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if($event->documents): ?>
                                                <?php $__currentLoopData = $event->documents; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $document): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <tr>
                                                        <td><?php echo e($document->document_type); ?></td>
                                                        <td><?php echo e($document->document_name); ?></td>
                                                        <td><?php echo e($document->selected_session); ?></td>
                                                        <td>
                                                            <a href="<?php echo e(asset(env('STORAGE_PATH') . '/event_documents/' . $document->document_name)); ?>"
                                                                target="_blank">
                                                                <button type="button" class="btn btn-light-primary"><i
                                                                        class="ti ti-download"></i></button>
                                                            </a>
                                                            <button type="button" class="btn btn-danger"
                                                                onclick="deleteDocument(this, <?php echo e($document->id); ?>)"><i
                                                                    class="ti ti-trash"></i></button>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>

                                <div class="d-flex justify-content-between">
                                    <button
                                        class="btn clone-event justify-content-center btn mb-1 btn-dark d-flex align-items-center"
                                        type="button" style="padding: 8px 40px;border-radius: 8px;height: 100%;">
                                        <i class="ti ti-layers-linked fs-4 me-2"></i>
                                        Clone Event
                                    </button>

                                    <input type="hidden" name="submit_type" id="submitTypeInput">
                                    <?php if($event->status == 2): ?>
                                    <button type="submit" class="save-btn" id="saveDraftBtn"
                                        style="background: none!important; border: none!important; color: #007bff; text-decoration: underline; padding: 0; cursor: pointer;">
                                        Save as Draft Event
                                    </button>
                                    <?php endif; ?>
                                    <button type="submit" class="save-btn" id="completeSaveBtn"
                                        style=" color: #fff; padding: 8px 40px; border-radius: 8px; border: none; margin-left: 10px;">Complete
                                        and Save Event</button>

                                </div>
                            </div>

                        </form>

                    </div>
                </div>
            </div>
        </div>

    </section>
<!-- Description Edit Modal -->
<div class="modal fade" id="descriptionModal" tabindex="-1" aria-labelledby="descriptionModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="descriptionModalLabel">Edit Description</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="descriptionTextarea">Description</label>
                    <textarea class="form-control" id="descriptionTextarea" rows="8" placeholder="Enter description..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveDescriptionBtn">Save</button>
            </div>
        </div>
    </div>
</div>

<script>
// Asset paths for JavaScript
const assetPaths = {
    plusIcon: '<?php echo e(asset("images/plus.png")); ?>',
    pencilIcon: '<?php echo e(asset("images/new-event-pencil.png")); ?>'
};

// Description edit functionality
document.addEventListener('DOMContentLoaded', function() {
    let currentDescriptionInput = null;

    // Description input click handler
    document.addEventListener('click', function(e) {
        if (e.target && (e.target.name === 'moderator_description[]' || e.target.name === 'speaker_description[]')) {
            currentDescriptionInput = e.target;

            // Set current value in modal
            document.getElementById('descriptionTextarea').value = currentDescriptionInput.value;

            // Update modal title based on type
            const type = e.target.name.includes('moderator') ? 'Moderator' : 'Speaker';
            document.getElementById('descriptionModalLabel').textContent = `Edit ${type} Description`;

            // Show modal using data attributes
            const modalElement = document.getElementById('descriptionModal');
            modalElement.style.display = 'block';
            modalElement.classList.add('show');
            document.body.classList.add('modal-open');

            // Add backdrop
            const backdrop = document.createElement('div');
            backdrop.className = 'modal-backdrop fade show';
            backdrop.id = 'modal-backdrop';
            document.body.appendChild(backdrop);
        }
    });

    // Save description button click handler
    const saveBtn = document.getElementById('saveDescriptionBtn');
    if (saveBtn) {
        saveBtn.addEventListener('click', function() {
            if (currentDescriptionInput) {
                const newDescription = document.getElementById('descriptionTextarea').value;
                currentDescriptionInput.value = newDescription;

                // Close modal
                closeModal();

                // Reset
                currentDescriptionInput = null;
            }
        });
    }

    // Close modal function
    function closeModal() {
        const modalElement = document.getElementById('descriptionModal');
        modalElement.style.display = 'none';
        modalElement.classList.remove('show');
        document.body.classList.remove('modal-open');

        // Remove backdrop
        const backdrop = document.getElementById('modal-backdrop');
        if (backdrop) {
            backdrop.remove();
        }

        // Reset
        currentDescriptionInput = null;
        document.getElementById('descriptionTextarea').value = '';
    }

    // Close button click handler
    const closeBtn = document.querySelector('#descriptionModal .btn-close');
    if (closeBtn) {
        closeBtn.addEventListener('click', closeModal);
    }

    // Cancel button click handler
    const cancelBtn = document.querySelector('#descriptionModal .btn-secondary');
    if (cancelBtn) {
        cancelBtn.addEventListener('click', closeModal);
    }

    // Click outside modal to close
    document.addEventListener('click', function(e) {
        if (e.target && e.target.id === 'descriptionModal') {
            closeModal();
        }
    });
});
</script>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
    <script src="<?php echo e(asset('js/mdtimepicker.js')); ?>"></script>
    <script src="<?php echo e(asset('js/moment.min.js')); ?>"></script>
    <script src="<?php echo e(asset('js/daterangepicker.js')); ?>"></script>

    <script src="https://cdn.jsdelivr.net/npm/select2@4.0.13/dist/js/select2.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.bundle.min.js"></script>

    
    <script>
        $(document).ready(function() {
            function formatTime(date) {
                let hours = date.getHours();
                let minutes = date.getMinutes();
                const ampm = hours >= 12 ? 'PM' : 'AM';
                hours = hours % 12 || 12;
                minutes = minutes < 10 ? '0' + minutes : minutes;
                return `${hours}:${minutes} ${ampm}`;
            }

            function timeToMinutes(timeStr) {
                const [h, m, ampm] = timeStr.split(/[ :]/);
                let hours = parseInt(h);
                if (ampm === 'PM' && hours !== 12) hours += 12;
                if (ampm === 'AM' && hours === 12) hours = 0;
                return hours * 60 + parseInt(m);
            }

            const timepickerState = {};

            <?php if($event->event_dates): ?>
                <?php $__currentLoopData = $event->event_dates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $event_date): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    const startTime_<?php echo e($event_date->id); ?> = moment("<?php echo e($event_date->event_start_time); ?>",
                        "HH:mm A").format("h:mm A");

                    const endTime_<?php echo e($event_date->id); ?> = moment("<?php echo e($event_date->event_end_time); ?>", "h:mm A")
                        .format("h:mm A");

                    timepickerState['#event_time_<?php echo e($event_date->id); ?>'] = {
                        start: startTime_<?php echo e($event_date->id); ?>,
                        end: endTime_<?php echo e($event_date->id); ?>

                    };
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php endif; ?>

            function bindTimePicker(inputSelector) {
                $(inputSelector).val(
                    `${timepickerState[inputSelector].start} - ${timepickerState[inputSelector].end}`
                );

                // Remove readonly attribute to allow keyboard input
                $(inputSelector).removeAttr('readonly');

                const openTimePickerFlow = function() {
                    const state = timepickerState[inputSelector];
                    const $startTemp = $('<input type="text" />').appendTo('body').hide().val(state.start);

                    $startTemp.mdtimepicker({
                        theme: 'blue',
                        value: state.start
                    }).data('mdtimepicker').show();

                    $startTemp.on('timechanged', function(e1) {
                        state.start = e1.value;

                        const $endTemp = $('<input type="text" />').appendTo('body').hide().val(state
                            .end);
                        $endTemp.mdtimepicker({
                            theme: 'blue',
                            value: state.end
                        }).data('mdtimepicker').show();

                        // label'ı "End" olarak güncelle
                        $('.mdtp__time_label').text('End:');

                        $endTemp.on('timechanged', function(e2) {
                            state.end = e2.value;

                            if (timeToMinutes(state.end) <= timeToMinutes(state.start)) {
                                alert('End time must be after start time.');
                                return;
                            }

                            $(inputSelector).val(`${state.start} - ${state.end}`);
                            $endTemp.mdtimepicker('destroy').remove();
                        });

                        $endTemp.on('hide.timepicker', function() {
                            $endTemp.remove();
                        });

                        $startTemp.mdtimepicker('destroy').remove();
                    });

                    $startTemp.on('hide.timepicker', function() {
                        $startTemp.remove();
                    });
                };

                $(inputSelector).on('click', openTimePickerFlow);
            }

            <?php if($event->event_dates): ?>
                <?php $__currentLoopData = $event->event_dates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $event_date): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    bindTimePicker('#event_time_<?php echo e($event_date->id); ?>');
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php endif; ?>

            // Sabit diğer alan
            const now = new Date();
            const defaultStart = formatTime(now);
            const defaultEnd = formatTime(new Date(now.getTime() + 2 * 60 * 60 * 1000));

            timepickerState['#session_time_duration'] = {
                start: defaultStart,
                end: defaultEnd
            };
            bindTimePicker('#session_time_duration');

            // Enable keyboard input for time holder
            enableTimeHolderKeyboardInput();

            // Enable keyboard input for time input fields
            enableTimeInputKeyboard();
        });

        function enableTimeHolderKeyboardInput() {
            // Function to make time holder spans editable
            $(document).on('click', '.mdtp__time_holder > span:not(.mdtp__timedots):not(.mdtp__ampm)', function(e) {
                e.stopPropagation();
                makeSpanEditable($(this));
            });

            // Function to handle keyboard input on time spans
            $(document).on('keydown', '.mdtp__time_holder > span:not(.mdtp__timedots):not(.mdtp__ampm)', function(e) {
                var $span = $(this);
                var isHour = $span.hasClass('mdtp__time_h');
                var isMinute = $span.hasClass('mdtp__time_m');

                // Allow numbers, backspace, delete, tab, escape, enter
                if ([8, 9, 27, 13, 46].indexOf(e.keyCode) !== -1 ||
                    // Allow Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X
                    (e.keyCode === 65 && e.ctrlKey === true) ||
                    (e.keyCode === 67 && e.ctrlKey === true) ||
                    (e.keyCode === 86 && e.ctrlKey === true) ||
                    (e.keyCode === 88 && e.ctrlKey === true) ||
                    // Allow home, end, left, right
                    (e.keyCode >= 35 && e.keyCode <= 39)) {

                    if (e.keyCode === 13) { // Enter key
                        e.preventDefault();
                        updateTimeFromSpan($span);
                        $span.blur();
                        return false;
                    }
                    if (e.keyCode === 27) { // Escape key
                        e.preventDefault();
                        $span.blur();
                        return false;
                    }
                    return;
                }

                // Ensure that it is a number and stop the keypress
                if ((e.shiftKey || (e.keyCode < 48 || e.keyCode > 57)) && (e.keyCode < 96 || e.keyCode > 105)) {
                    e.preventDefault();
                }
            });

            // Handle input validation and formatting
            $(document).on('input', '.mdtp__time_holder > span:not(.mdtp__timedots):not(.mdtp__ampm)', function(e) {
                var $span = $(this);
                var value = $span.text().replace(/\D/g, ''); // Remove non-digits
                var isHour = $span.hasClass('mdtp__time_h');
                var isMinute = $span.hasClass('mdtp__time_m');

                if (isHour) {
                    if (value.length > 2) value = value.substring(0, 2);
                    var num = parseInt(value) || 0;
                    if (num > 12) value = '12';
                    if (num < 1 && value.length === 2) value = '01';
                } else if (isMinute) {
                    if (value.length > 2) value = value.substring(0, 2);
                    var num = parseInt(value) || 0;
                    if (num > 59) value = '59';
                }

                $span.text(value);

                // Move cursor to end
                var range = document.createRange();
                var sel = window.getSelection();
                range.selectNodeContents($span[0]);
                range.collapse(false);
                sel.removeAllRanges();
                sel.addRange(range);
            });

            // Handle blur event to update the time
            $(document).on('blur', '.mdtp__time_holder > span:not(.mdtp__timedots):not(.mdtp__ampm)', function(e) {
                var $span = $(this);
                updateTimeFromSpan($span);
                $span.removeClass('editing');
                $span.removeAttr('contenteditable');
            });
        }

        function makeSpanEditable($span) {
            $span.addClass('editing');
            $span.attr('contenteditable', 'true');
            $span.focus();

            // Select all text
            var range = document.createRange();
            var sel = window.getSelection();
            range.selectNodeContents($span[0]);
            sel.removeAllRanges();
            sel.addRange(range);
        }

        function updateTimeFromSpan($span) {
            var isHour = $span.hasClass('mdtp__time_h');
            var isMinute = $span.hasClass('mdtp__time_m');
            var value = $span.text().replace(/\D/g, '');

            if (isHour) {
                var hour = parseInt(value) || 12;
                if (hour < 1) hour = 1;
                if (hour > 12) hour = 12;
                $span.text(hour.toString().padStart(2, '0'));

                // Update the timepicker's internal hour value
                var $wrapper = $span.closest('.mdtp__wrapper');
                if ($wrapper.length) {
                    $wrapper.find('.mdtp__clock_holder .mdtp__hour').removeClass('active');
                    $wrapper.find('.mdtp__clock_holder .mdtp__hour[data-hour="' + hour + '"]').addClass('active');
                }
            } else if (isMinute) {
                var minute = parseInt(value) || 0;
                if (minute > 59) minute = 59;
                $span.text(minute.toString().padStart(2, '0'));

                // Update the timepicker's internal minute value
                var $wrapper = $span.closest('.mdtp__wrapper');
                if ($wrapper.length) {
                    $wrapper.find('.mdtp__clock_holder .mdtp__minute').removeClass('active');
                    $wrapper.find('.mdtp__clock_holder .mdtp__minute[data-minute="' + minute + '"]').addClass('active');
                }
            }
        }

        function enableTimeInputKeyboard() {
            // Enable keyboard input for time input fields
            $(document).on('keyup', '.timepicker', function(e) {
                var value = $(this).val();
                var timePattern = /^(\d{1,2}):(\d{2})\s*(AM|PM)\s*-\s*(\d{1,2}):(\d{2})\s*(AM|PM)$/i;

                if (timePattern.test(value)) {
                    var parts = value.match(timePattern);
                    var startHour = parseInt(parts[1], 10);
                    var startMinute = parseInt(parts[2], 10);
                    var startAmPm = parts[3].toUpperCase();
                    var endHour = parseInt(parts[4], 10);
                    var endMinute = parseInt(parts[5], 10);
                    var endAmPm = parts[6].toUpperCase();

                    // Validate time ranges
                    if (startHour >= 1 && startHour <= 12 && startMinute >= 0 && startMinute <= 59 &&
                        endHour >= 1 && endHour <= 12 && endMinute >= 0 && endMinute <= 59 &&
                        (startAmPm === 'AM' || startAmPm === 'PM') && (endAmPm === 'AM' || endAmPm === 'PM')) {

                        var startTime = moment(startHour + ':' + startMinute.toString().padStart(2, '0') + ' ' + startAmPm, 'h:mm A');
                        var endTime = moment(endHour + ':' + endMinute.toString().padStart(2, '0') + ' ' + endAmPm, 'h:mm A');

                        if (startTime.isValid() && endTime.isValid() && endTime.isAfter(startTime)) {
                            // Update the corresponding timepicker state if it exists
                            var inputId = '#' + $(this).attr('id');
                            if (window.timepickerState && window.timepickerState[inputId]) {
                                window.timepickerState[inputId].start = startTime.format('h:mm A');
                                window.timepickerState[inputId].end = endTime.format('h:mm A');
                            }
                        }
                    }
                }
            });

            // Allow manual typing in time input fields
            $(document).on('focus', '.timepicker', function() {
                $(this).removeAttr('readonly');
            });
        }
    </script>



    

    <script>
        $(document).ready(function() {
            $('#event_country, #event_date_timezone').select2({
                placeholder: "Afghanistan",
                allowClear: true,
            });
        });

        setTimeout(function() {
            $('.select2.select2-container.select2-container--default').addClass('custom-select');
        }, 10);
    </script>

    <script>
        function initSelect2() {
            $('select.select2-hidden-accessible').select2({
                width: '100%'
            });
        }

        function resizeSelect2() {
            $('.select2-container').each(function() {
                $(this).css('width', '100%');
            });
        }

        $(window).on('resize', function() {
            resizeSelect2();
        });
    </script>

    <script>
        let eventDates = [];
        document.addEventListener('DOMContentLoaded', function() {

            if (document.getElementById('duration_of_event').value == 2) {
                <?php $__currentLoopData = $event->event_dates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $event_date): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>

                    eventDates.push(moment("<?php echo e($event_date->event_date); ?>").format('DD/MM/YYYY'));
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            }

            const durationSelect = document.getElementById('duration_of_event');
            const eventDateInput = document.getElementById('event_date');
            //const eventDateValue = moment("<?php echo e($event->event_date); ?>");
            const eventEndDateValue = "<?php echo e($event->event_end_date); ?>" ? moment("<?php echo e($event->event_end_date); ?>") :
                moment("<?php echo e($event->event_date); ?>");


            function initializeSingleDatePicker() {
                <?php $__currentLoopData = $event->event_dates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $event_date): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    const eventDateValue_<?php echo e($event_date->id); ?> = moment("<?php echo e($event_date->event_date); ?>");

                    $("#event_date_<?php echo e($event_date->id); ?>").daterangepicker({
                        singleDatePicker: true,
                        showDropdowns: true,
                        autoUpdateInput: false,
                        locale: {
                            format: 'DD/MM/YYYY'
                        },
                        startDate: eventDateValue_<?php echo e($event_date->id); ?>,
                    }).on('apply.daterangepicker', function(ev, picker) {
                        $(this).val(picker.startDate.format('DD/MM/YYYY'));
                    });

                    // Set initial value
                    $("#event_date_<?php echo e($event_date->id); ?>").val(eventDateValue_<?php echo e($event_date->id); ?>.format('DD/MM/YYYY'));

                    // Enable keyboard input for this specific date field
                    $("#event_date_<?php echo e($event_date->id); ?>").off('keyup.dateInput').on('keyup.dateInput', function(e) {
                        var value = $(this).val();
                        var datePattern = /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/;

                        if (datePattern.test(value)) {
                            var parts = value.match(datePattern);
                            var day = parseInt(parts[1], 10);
                            var month = parseInt(parts[2], 10);
                            var year = parseInt(parts[3], 10);

                            if (day >= 1 && day <= 31 && month >= 1 && month <= 12 && year >= 1900 && year <= 2100) {
                                var date = moment(value, 'DD/MM/YYYY');
                                if (date.isValid()) {
                                    $(this).data('daterangepicker').setStartDate(date);
                                    $(this).data('daterangepicker').setEndDate(date);
                                }
                            }
                        }
                    });

                    const startTimeValue_<?php echo e($event_date->id); ?> = moment("<?php echo e($event_date->event_start_time); ?>",
                        "h:mm A");
                    const endTimeValue_<?php echo e($event_date->id); ?> = moment("<?php echo e($event_date->event_end_time); ?>",
                        "h:mm A");

                    $(".datetime.timeselect#event_time_<?php echo e($event_date->id); ?>").daterangepicker({
                        timeSelect: true,
                        timePicker: true,
                        locale: {
                            format: "h:mm A",
                        },
                        startDate: startTimeValue_<?php echo e($event_date->id); ?>,
                        endDate: endTimeValue_<?php echo e($event_date->id); ?>,
                        ranges: {}
                    });
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            }



            initializeSingleDatePicker();

            // On change event
            durationSelect.addEventListener('change', function() {
                if (this.value === '1') {
                    initializeSingleDatePicker();
                    document.getElementById('new_date_button').style.display = 'none';
                    document.getElementById('session_dates_div').style.display = 'none';

                    var dateTimeRows = document.querySelectorAll('.date-time-row');
                    for (var i = 1; i < dateTimeRows.length; i++) {
                        dateTimeRows[i].remove();
                    }

                } else if (this.value === '2') {
                    initializeSingleDatePicker();
                    document.getElementById('new_date_button').style.display = 'block';
                    document.getElementById('session_dates_div').style.display = 'block';
                }
            });
        });
    </script>

    <script>
        window.formatTime = function(date) {
            let hours = date.getHours();
            let minutes = date.getMinutes();
            const ampm = hours >= 12 ? 'PM' : 'AM';
            hours = hours % 12 || 12;
            minutes = minutes < 10 ? '0' + minutes : minutes;
            return `${hours}:${minutes} ${ampm}`;
        };
        /*******************************************/
        // Basic Date Range Picker
        /*******************************************/
        $(".daterange").daterangepicker();

        /*******************************************/
        // Date & Time
        /*******************************************/
        // $(".datetime").daterangepicker({
        //    timePicker: true,
        //    timePickerIncrement: 30,
        //    locale: {
        //       format: "MM/DD/YYYY h:mm A",
        //    },
        //  });

        /*******************************************/
        //Calendars are not linked
        /*******************************************/
        $(".timeseconds").daterangepicker({
            timePicker: true,
            timePickerIncrement: 30,
            timePicker24Hour: true,
            timePickerSeconds: true,
            locale: {
                format: "MM/DD/YYYY h:mm:ss",
            },
        });

        /*******************************************/
        // Single Date Range Picker
        /*******************************************/
        $(".singledate").daterangepicker({
            singleDatePicker: true,
            showDropdowns: true,
            autoUpdateInput: false
        }).on('apply.daterangepicker', function(ev, picker) {
            $(this).val(picker.startDate.format('DD/MM/YYYY'));
        });

        // Enable keyboard input for date fields
        $(".singledate").on('keyup', function(e) {
            var value = $(this).val();
            var datePattern = /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/;

            if (datePattern.test(value)) {
                var parts = value.match(datePattern);
                var day = parseInt(parts[1], 10);
                var month = parseInt(parts[2], 10);
                var year = parseInt(parts[3], 10);

                if (day >= 1 && day <= 31 && month >= 1 && month <= 12 && year >= 1900 && year <= 2100) {
                    var date = moment(value, 'DD/MM/YYYY');
                    if (date.isValid()) {
                        $(this).data('daterangepicker').setStartDate(date);
                        $(this).data('daterangepicker').setEndDate(date);
                    }
                }
            }
        });

        /*******************************************/
        // Time Select
        /*******************************************/
        <?php if($event->event_dates): ?>
            <?php $__currentLoopData = $event->event_dates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $event_date): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>

                $(".datetime.timeselect#event_time_<?php echo e($event_date->id); ?>").daterangepicker({
                    timeSelect: true,
                    timePicker: true,
                    locale: {
                        format: "h:mm A",
                    },
                    startDate: moment().startOf('hour'),
                    endDate: moment().startOf('hour').add(2, 'hour'),
                    ranges: {}
                });
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <?php endif; ?>

        /*const eventStartTime = "<?php echo e($event->event_start_time); ?>";
        const eventEndTime = "<?php echo e($event->event_end_time); ?>";*/
        $(".datetime.timeselect").daterangepicker({
            timeSelect: true,
            timePicker: true,
            locale: {
                format: "h:mm A",
            },
            startDate: moment().startOf('hour'),
            endDate: moment().startOf('hour').add(2, 'hour'),
            ranges: {}
        });


        /*******************************************/
        // Auto Apply Date Range
        /*******************************************/
        $(".autoapply").daterangepicker({
            autoApply: true,
        });

        /*******************************************/
        // Calendars are not linked
        /*******************************************/
        $(".linkedCalendars").daterangepicker({
            linkedCalendars: false,
        });

        /*******************************************/
        // Date Limit
        /*******************************************/
        $(".dateLimit").daterangepicker({
            dateLimit: {
                days: 7,
            },
        });

        /*******************************************/
        // Show Dropdowns
        /*******************************************/
        $(".showdropdowns").daterangepicker({
            showDropdowns: true,
        });

        /*******************************************/
        // Show Week Numbers
        /*******************************************/
        $(".showweeknumbers").daterangepicker({
            showWeekNumbers: true,
        });

        /*******************************************/
        // Date Ranges
        /*******************************************/
        $(".dateranges").daterangepicker({
            ranges: {
                Today: [moment(), moment()],
                Yesterday: [
                    moment().subtract(1, "days"),
                    moment().subtract(1, "days"),
                ],
                "Last 7 Days": [moment().subtract(6, "days"), moment()],
                "Last 30 Days": [moment().subtract(29, "days"), moment()],
                "This Month": [moment().startOf("month"), moment().endOf("month")],
                "Last Month": [
                    moment().subtract(1, "month").startOf("month"),
                    moment().subtract(1, "month").endOf("month"),
                ],
            },
        });

        /*******************************************/
        // Always Show Calendar on Ranges
        /*******************************************/
        $(".shawCalRanges").daterangepicker({
            ranges: {
                Today: [moment(), moment()],
                Yesterday: [
                    moment().subtract(1, "days"),
                    moment().subtract(1, "days"),
                ],
                "Last 7 Days": [moment().subtract(6, "days"), moment()],
                "Last 30 Days": [moment().subtract(29, "days"), moment()],
                "This Month": [moment().startOf("month"), moment().endOf("month")],
                "Last Month": [
                    moment().subtract(1, "month").startOf("month"),
                    moment().subtract(1, "month").endOf("month"),
                ],
            },
            alwaysShowCalendars: true,
        });

        /*******************************************/
        // Top of the form-control open alignment
        /*******************************************/
        $(".drops").daterangepicker({
            drops: "up", // up/down
        });

        /*******************************************/
        // Custom button options
        /*******************************************/
        $(".buttonClass").daterangepicker({
            drops: "up",
            buttonClasses: "btn",
            applyClass: "btn-info",
            cancelClass: "btn-danger",
        });

        /*******************************************/
        // Language
        /*******************************************/
        $(".localeRange").daterangepicker({
            ranges: {
                "Aujourd'hui": [moment(), moment()],
                Hier: [moment().subtract("days", 1), moment().subtract("days", 1)],
                "Les 7 derniers jours": [moment().subtract("days", 6), moment()],
                "Les 30 derniers jours": [moment().subtract("days", 29), moment()],
                "Ce mois-ci": [moment().startOf("month"), moment().endOf("month")],
                "le mois dernier": [
                    moment().subtract("month", 1).startOf("month"),
                    moment().subtract("month", 1).endOf("month"),
                ],
            },
            locale: {
                applyLabel: "Vers l'avant",
                cancelLabel: "Annulation",
                startLabel: "Date initiale",
                endLabel: "Date limite",
                customRangeLabel: "SÃ©lectionner une date",
                // daysOfWeek: ['Dimanche', 'Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi','Samedi'],
                daysOfWeek: ["Di", "Lu", "Ma", "Me", "Je", "Ve", "Sa"],
                monthNames: [
                    "Janvier",
                    "fÃ©vrier",
                    "Mars",
                    "Avril",
                    "ÐœÐ°i",
                    "Juin",
                    "Juillet",
                    "AoÃ»t",
                    "Septembre",
                    "Octobre",
                    "Novembre",
                    "Decembre",
                ],
                firstDay: 1,
            },
        });

        const fileInput = document.getElementById('event_banner');
        const previewImg = document.getElementById('preview-img');

        fileInput.addEventListener('change', function(event) {
            const file = event.target.files[0];

            // Ensure the file is an image and under the size limit
            if (file && file.type.startsWith('image/') && file.size <= 1024 * 1024) { // Max 1MB
                const reader = new FileReader();

                reader.onload = function(e) {
                    previewImg.src = e.target.result; // Set the preview image to the selected file
                };

                reader.readAsDataURL(file);
            } else {
                // Clear the file input
                fileInput.value = '';

                // Show warning message
                Swal.fire({
                    title: "Warning",
                    html: "Please select a valid image file (JPEG, PNG, GIF) and make sure it is under 1MB.",
                    icon: "warning"
                });
            }
        });

        function add_speakers() {
            var selectBox = document.getElementById("speakers_count");
            var selectedValue = parseInt(selectBox.value);
            var speakersDiv = document.getElementById("speakers_div");

            let old_speaker_value = parseInt(document.getElementById('speaker_selected_count').value);
            document.getElementById('speaker_selected_count').value = selectedValue;

            if (selectedValue == 0) {
                speakersDiv.innerHTML = '';
                return;
            }

            // Get current speaker elements
            let currentSpeakers = Array.from(speakersDiv.querySelectorAll('.d-flex.flex-column.col-md-6'));
            let currentCount = currentSpeakers.length;

            if (selectedValue > currentCount) {
                // Add new speakers
                let loop_count = selectedValue - currentCount;
                for (let i = 1; i <= loop_count; i++) {
                    let div_class_count_s = currentCount + i;
                    var newDiv = document.createElement("div");
                    newDiv.classList.add("d-flex", "flex-column", "col-md-6");
                    newDiv.classList.add(`div-speaker-count-${div_class_count_s}`);

                    newDiv.innerHTML = `
                        <div class="pb-2">
                            <span class="d-flex justify-content-center align-items-center w-100">
                                <label for="speaker_banner${div_class_count_s}" class="d-flex justify-content-center align-items-center w-100" style="background-color:rgba(232, 240, 254, 1); border: 1px solid rgba(223, 229, 239, 1); height: 100px; border-radius: 8px; cursor: pointer;">
                                <img id="speaker-img${div_class_count_s}" src="${assetPaths.plusIcon}" alt="icon" style="max-width: 100%; max-height: 100%; object-fit: contain;"/>
                                </label>
                            </span>
                        </div> <br>
                        <input type="file" id="speaker_banner${div_class_count_s}" name="speaker_banner[]" class="hidden_banner_input" data-label="Speaker Banner" accept="image/*">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <div class="d-flex flex-col w-100">
                                <input type="text" class="line-input" name="speaker_name[]" data-label="Speaker Name Surname"
                                    style="border:none; border-bottom: 1px solid #000"
                                    placeholder="Name Surname *" />
                            </div>
                            <img src="${assetPaths.pencilIcon}"
                                class="pencil-icon" alt="icon" />
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="d-flex flex-col w-100">
                                <input type="text" class="line-subinput" name="speaker_title[]" data-label="Speaker Title"
                                    style="border:none; border-bottom: 1px solid #000"
                                    placeholder="Title *" />
                            </div>
                            <img src="${assetPaths.pencilIcon}"
                                class="pencil-icon" alt="icon" />
                        </div>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <div class="d-flex flex-col w-100">
                                <input type="text" class="line-input" name="speaker_description[]" data-label="Speaker Description"
                                    style="border:none; border-bottom: 1px solid #000; cursor: pointer;"
                                    placeholder="Click to add description"
                                    readonly />
                            </div>
                            <img src="${assetPaths.pencilIcon}"
                                class="pencil-icon" alt="icon" />
                        </div>
                    `;

                    speakersDiv.appendChild(newDiv);
                }
            } else if (selectedValue < currentCount) {
                // Keep only the first selectedValue speakers, remove the rest
                for (let i = selectedValue; i < currentCount; i++) {
                    if (currentSpeakers[i]) {
                        currentSpeakers[i].remove();
                    }
                }
            }
        }

        function add_moderator() {
            var selectBox = document.getElementById("moderators_count");
            var selectedValue = parseInt(selectBox.value);
            var moderatorDiv = document.getElementById("moderator_div");

            let old_moderator_value = parseInt(document.getElementById('moderator_selected_count').value);
            document.getElementById('moderator_selected_count').value = selectedValue;

            if (selectedValue == 0) {
                moderatorDiv.innerHTML = '';
                return;
            }

            // Get current moderator elements
            let currentModerators = Array.from(moderatorDiv.querySelectorAll('.d-flex.flex-column.col-md-6'));
            let currentCount = currentModerators.length;

            if (selectedValue > currentCount) {
                // Add new moderators
                let loop_count = selectedValue - currentCount;
                for (let i = 1; i <= loop_count; i++) {
                    let div_class_count = currentCount + i;
                    var newDiv = document.createElement("div");
                    newDiv.classList.add("d-flex", "flex-column", "col-md-6");
                    newDiv.classList.add(`div-count-${div_class_count}`);

                    newDiv.innerHTML = `
                        <div class="pb-2">
                            <span class="d-flex justify-content-center align-items-center w-100">
                                <label for="moderator_banner${div_class_count}" class="d-flex justify-content-center align-items-center w-100" style="background-color:rgba(232, 240, 254, 1); border: 1px solid rgba(223, 229, 239, 1); height: 100px; border-radius: 8px; cursor: pointer;">
                                <img id="moderator-img${div_class_count}" src="${assetPaths.plusIcon}" alt="icon" style="max-width: 100%; max-height: 100%; object-fit: contain;"/>
                                </label>
                            </span>
                        </div> <br>
                        <input type="file" id="moderator_banner${div_class_count}" name="moderator_banner[]" class="hidden_banner_input" data-label="Moderator Banner" accept="image/*">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <div class="d-flex flex-col w-100">
                                <input type="text" class="line-input" name="moderator_name[]" data-label="Moderator Name Surname"
                                    style="border:none; border-bottom: 1px solid #000"
                                    placeholder="Name Surname *" />
                            </div>
                            <img src="${assetPaths.pencilIcon}"
                                class="pencil-icon" alt="icon" />
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="d-flex flex-col w-100">
                                <input type="text" class="line-subinput" name="moderator_title[]" data-label="Moderator Title"
                                    style="border:none; border-bottom: 1px solid #000"
                                    placeholder="Title *" />
                            </div>
                            <img src="${assetPaths.pencilIcon}"
                                class="pencil-icon" alt="icon" />
                        </div>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <div class="d-flex flex-col w-100">
                                <input type="text" class="line-input" name="moderator_description[]" data-label="Moderator Description"
                                    style="border:none; border-bottom: 1px solid #000; cursor: pointer;"
                                    placeholder="Click to add description"
                                    readonly />
                            </div>
                            <img src="${assetPaths.pencilIcon}"
                                class="pencil-icon" alt="icon" />
                        </div>
                    `;

                    moderatorDiv.appendChild(newDiv);
                }
            } else if (selectedValue < currentCount) {
                // Keep only the first selectedValue moderators, remove the rest
                for (let i = selectedValue; i < currentCount; i++) {
                    if (currentModerators[i]) {
                        currentModerators[i].remove();
                    }
                }
            }
        }

        function set_moderator_speakers() {

            const container = document.getElementById('date-time-container');
            const dateInputs = container.querySelectorAll('input[name="event_date[]"]');
            const selectBox = document.getElementById('session_dates');
            selectBox.innerHTML = '<option value="">Select a date</option>';

            dateInputs.forEach(input => {
                const value = input.value.trim();
                if (value) {
                    const option = document.createElement('option');
                    option.value = value;
                    option.textContent = value;
                    selectBox.appendChild(option);
                }
            });

            const moderator_inputs = document.querySelectorAll('input[name="moderator_name[]"]');
            const speaker_inputs = document.querySelectorAll('input[name="speaker_name[]"]');

            if (!document.getElementById('event_title').value.trim() ||
                !document.getElementById('event_location').value.trim() ||
                !document.getElementById('business_unit').value.trim() ||
                !document.getElementById('attendee_quota').value.trim() ||
                !document.getElementById('event_description').value.trim() ||
                !document.getElementById('contact_email').value.trim()
            ) {
                Swal.fire({
                    title: "Warning",
                    text: "Please fill in the empty fields.",
                    icon: "warning"
                });
                return;
            }

            if (
                /*moderator_inputs.length === 0 || speaker_inputs.length === 0 ||*/
                Array.from(moderator_inputs).some(input => input.value.trim() === '') ||
                Array.from(speaker_inputs).some(input => input.value.trim() === '')
            ) {
                Swal.fire({
                    title: "Warning",
                    text: "Please fill in the empty Moderator and Speaker fields.",
                    icon: "warning"
                });
                return;
            }

            Swal.fire({
                title: "Success",
                text: "Event has been updated. You can fill in Session and Event documents details.",
                icon: "success"
            });

            document.getElementById('session_title').disabled = false;
            //document.getElementById('session_speakers').disabled = false;
            document.getElementById('session_time_duration').disabled = false;
            document.getElementById('session_button').disabled = false;

            function updateSpeakersDropdown() {
                var dropdownList = document.getElementById("session_speakers_list");
                dropdownList.innerHTML = '';

                var addOption = (input) => {
                    const label = document.createElement("label");
                    label.className = "dropdown-option";

                    const checkbox = document.createElement("input");
                    checkbox.type = "checkbox";
                    checkbox.name = "dropdown-group";
                    checkbox.value = input.value;
                    checkbox.disabled = true;

                    label.appendChild(checkbox);
                    label.appendChild(document.createTextNode(input.value));
                    dropdownList.appendChild(label);
                };

                // moderator_inputs.forEach(addOption);
                speaker_inputs.forEach(addOption);

                var options = document.querySelectorAll("input[name='dropdown-group']");
                options.forEach(option => {
                    option.disabled = false;
                });
            }

            function updateModeratorsDropdown() {
                var dropdownList = document.getElementById("session_moderators_list");
                dropdownList.innerHTML = '';

                var addOption = (input) => {
                    const label = document.createElement("label");
                    label.className = "dropdown-option";

                    const checkbox = document.createElement("input");
                    checkbox.type = "checkbox";
                    checkbox.name = "dropdown-group-moderator";
                    checkbox.value = input.value;
                    checkbox.disabled = true;

                    label.appendChild(checkbox);
                    label.appendChild(document.createTextNode(input.value));
                    dropdownList.appendChild(label);
                };

                moderator_inputs.forEach(addOption);

                var options = document.querySelectorAll("input[name='dropdown-group-moderator']");
                options.forEach(option => {
                    option.disabled = false;
                });
            }

            updateSpeakersDropdown();
            updateModeratorsDropdown();
        }

        document.getElementById('moderator_div').addEventListener('change', function(event) {
            if (event.target && event.target.id.startsWith('moderator_banner')) {
                const fileInput = event.target;
                const fileId = fileInput.id;
                const index = fileId.replace('moderator_banner', ''); // ID'den index numarasını almak
                const file = fileInput.files[0];

                // Ensure the file is an image and under the size limit
                if (file && file.type.startsWith('image/') && file.size <= 1024 * 1024) { // Max 1MB
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const imgElement = document.getElementById('moderator-img' + index);
                        if (imgElement) {
                            imgElement.src = e.target.result;
                            imgElement.style.width = '75%'; // Yüklenen resmin genişliği %75 olarak ayarlanıyor
                        }
                    };
                    reader.readAsDataURL(file);
                } else if (file) {
                    // Clear the file input
                    fileInput.value = '';

                    // Show warning message
                    Swal.fire({
                        title: "Warning",
                        html: "Please select a valid image file (JPEG, PNG, GIF) and make sure it is under 1MB.",
                        icon: "warning"
                    });
                }
            }
        });

        document.getElementById('speakers_div').addEventListener('change', function(event) {
            if (event.target && event.target.id.startsWith('speaker_banner')) {
                const fileInput = event.target;
                const fileId = fileInput.id;
                const index = fileId.replace('speaker_banner', ''); // ID'den index numarasını almak
                const file = fileInput.files[0];

                // Ensure the file is an image and under the size limit
                if (file && file.type.startsWith('image/') && file.size <= 1024 * 1024) { // Max 1MB
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const imgElement = document.getElementById('speaker-img' + index);
                        if (imgElement) {
                            imgElement.src = e.target.result;
                            imgElement.style.width = '75%'; // Yüklenen resmin genişliği %75 olarak ayarlanıyor
                        }
                    };
                    reader.readAsDataURL(file);
                } else if (file) {
                    // Clear the file input
                    fileInput.value = '';

                    // Show warning message
                    Swal.fire({
                        title: "Warning",
                        html: "Please select a valid image file (JPEG, PNG, GIF) and make sure it is under 1MB.",
                        icon: "warning"
                    });
                }
            }
        });

        document.getElementById('uploaded_document').addEventListener('change', function(event) {
            const fileInput = event.target;
            const fileName = fileInput.files.length > 0 ? fileInput.files[0].name : ''; // Seçilen dosyanın adı

            console.log(event);
            const fileNameSpan = document.getElementById('file_name');
            if (fileName) {
                fileNameSpan.textContent = fileName; // Dosya adını göster
                fileNameSpan.style.visibility = 'visible'; // Dosya adını görünür yap
            } else {
                fileNameSpan.style.visibility = 'hidden'; // Dosya yoksa gizle
            }
            //fileInput.value = '';
        });

        function set_upload_data() {
            const document_name = document.getElementById('document_name').value;
            const document_type = document.getElementById('document_type').value;
            const selected_session = document.getElementById('selected_session').value;
            const file_name = document.getElementById('file_name').textContent;

            if (document_name.trim() === "" || document_type.trim() === "" || selected_session.trim() === "" || file_name
                .trim() === "") {
                alert('Please fill all fields');
                return;
            }

            var file_extension = file_name.split('.').pop();

            if (document_type == 'PDF' && file_extension != 'pdf') {
                alert('Please select a PDF file');
                return;
            }
            if (document_type == 'WORD' && file_extension != 'docx') {
                alert('Please select a WORD file');
                return;
            }
            if (document_type == 'PPT' && file_extension != 'pptx') {
                alert('Please select a PPT file');
                return;
            }

            var table = document.getElementById("documents_table").getElementsByTagName('tbody')[0];
            var row = table.insertRow();
            var cell1 = row.insertCell(0);
            var cell2 = row.insertCell(1);
            var cell3 = row.insertCell(2);
            var cell4 = row.insertCell(3);
            cell1.innerHTML = document_type;
            cell2.innerHTML = document_name;
            cell3.innerHTML = selected_session;
            cell4.innerHTML = '<button type="button" class="btn btn-danger" onclick="confirmDeleteDocument(this, \'' +
                document_name + '\')"><i class="ti ti-trash"></i></button>';

            var multiple_document_name = document.getElementById('multiple_document_name');

            if (multiple_document_name.value) {
                multiple_document_name.value += ', ' + document_name;
            } else {
                multiple_document_name.value = document_name;
            }

            var multiple_document_type = document.getElementById('multiple_document_type');

            if (multiple_document_type.value) {
                multiple_document_type.value += ', ' + document_type;
            } else {
                multiple_document_type.value = document_type;
            }

            var multiple_selected_session = document.getElementById('multiple_selected_session');

            if (multiple_selected_session.value) {
                multiple_selected_session.value += ', ' + selected_session;
            } else {
                multiple_selected_session.value = selected_session;
            }

            copyFile();

            // İşlem sonrası inputlar temizlenir
            document.getElementById('document_name').value = "";
            //document.getElementById('document_type').selectedIndex = 0;
            document.getElementById('selected_session').selectedIndex = 0;
            document.getElementById('file_name').textContent = "";

            document.getElementById('uploaded_document').value = "";

            Swal.fire({
                title: "Success",
                text: "Document has been added.",
                icon: "success"
            });
        }

        function confirmDeleteDocument(button, documentName) {
            Swal.fire({
                title: 'Are you sure?',
                text: "This will delete the document.",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#aaa',
                confirmButtonText: 'Yes, delete it!',
                cancelButtonText: 'Cancel'
            }).then((result) => {
                if (result.isConfirmed) {
                    deleteTempDocument(button, documentName);
                    Swal.fire('Deleted!', 'The document has been deleted.', 'success');
                }
            });
        }


        function deleteTempDocument(button, documentName) {
            var row = button.closest('tr');
            row.remove();
        }

        let duplicatedFiles = new DataTransfer();

        function copyFile() {
            const fileInput = document.getElementById('uploaded_document'); // İlk dosya inputu
            const duplicatedInput = document.getElementById('duplicated_document'); // İkinci dosya inputu

            const files = fileInput.files; // İlk input'tan dosyaları alıyoruz

            // Eğer dosya seçildiyse
            if (files.length > 0) {
                // Önceden eklenen dosyaları yeni DataTransfer nesnesine taşıyoruz
                const newDataTransfer = new DataTransfer();

                // Önceki dosyaları taşıyoruz
                for (let i = 0; i < duplicatedFiles.files.length; i++) {
                    newDataTransfer.items.add(duplicatedFiles.files[i]);
                }

                // Yeni dosyaları DataTransfer'a ekliyoruz
                for (let i = 0; i < files.length; i++) {
                    newDataTransfer.items.add(files[i]);
                }

                // Güncellenmiş dosya listesini duplicated_input'a atıyoruz
                duplicatedFiles = newDataTransfer;

                // İkinci input'un file listesini güncelliyoruz
                duplicatedInput.files = duplicatedFiles.files;
            }
        }

        function add_session() {
            document.getElementById('selected_session').disabled = false;
            document.getElementById('uploaded_document').disabled = false;
            document.getElementById('document_type').disabled = false;
            document.getElementById('document_name').disabled = false;
            document.getElementById('document_button').disabled = false;

            var session_time_duration = document.getElementById('session_time_duration').value;
            var session_title = document.getElementById('session_title').value;
            if (document.getElementById('duration_of_event').value == 1) {
                var session_date = document.getElementById('single_session_dates').value
            } else {
                var session_date = document.getElementById('session_dates').value;
            }

            if (!isTimeRangeInside(getTimeByDate(session_date), session_time_duration)) {
                Swal.fire({
                    title: "Warning",
                    text: "This time range falls outside of the session period. Please select a valid time.",
                    icon: "warning"
                });
                return;
            }

            var session_speakers = document.getElementById('session_speakers_list');
            var selectedCheckboxes = session_speakers.querySelectorAll('input[type="checkbox"]:checked');
            var selectedValues = Array.from(selectedCheckboxes).map(checkbox => checkbox.value);
            var selectedValuesString = selectedValues.join('<br>');
            var session_speakers2 = document.getElementById('session_moderators_list');
            var selectedCheckboxes2 = session_speakers2.querySelectorAll('input[type="checkbox"]:checked');
            var selectedValues2 = Array.from(selectedCheckboxes2).map(checkbox => checkbox.value);
            var selectedValuesString2 = selectedValues2.join('<br>');

            if (session_time_duration == '' || session_title == '' || (selectedCheckboxes.length === 0 &&
                    selectedCheckboxes2.length === 0)) {
                alert('Please fill all fields');
                return;
            }

            var table = document.getElementById("sessions_table").getElementsByTagName('tbody')[0];
            var row = table.insertRow();
            var cell1 = row.insertCell(0);
            var cell2 = row.insertCell(1);
            var cell3 = row.insertCell(2);
            var cell4 = row.insertCell(3);
            var cell5 = row.insertCell(4);
            var cell6 = row.insertCell(5);
            cell1.innerHTML = session_date;
            cell2.innerHTML = session_time_duration;
            cell3.innerHTML = session_title;
            cell4.innerHTML = selectedValuesString;
            cell5.innerHTML = selectedValuesString2;
            cell6.innerHTML =
                '<button type="button" class="btn btn-primary ms-2 edit_btn" onclick="editSession(this)"><i class="ti ti-pencil"></i></button>' +
                '<button type="button" class="btn btn-danger delete_btn" onclick="confirmDelete(this, \'' +
                session_title + '\')"><i class="ti ti-trash"></i></button> ';


            var session_time_duration_input = document.getElementById('session_time_duration_input');

            if (session_time_duration_input.value) {
                session_time_duration_input.value += ', ' + session_time_duration;
            } else {
                session_time_duration_input.value = session_time_duration;
            }

            var session_title_input = document.getElementById('session_title_input');

            if (session_title_input.value) {
                session_title_input.value += ', ' + session_title;
            } else {
                session_title_input.value = session_title;
            }

            var session_speakers_input = document.getElementById('session_speakers_input');

            if (session_speakers_input.value && session_speakers_input.value !== ',') {
                session_speakers_input.value += ', ' + selectedValuesString;
                console.log(selectedValuesString);
            } else {
                if (session_speakers_input.value !== ',')
                    session_speakers_input.value = selectedValuesString ? selectedValuesString : ',';
                else
                    session_speakers_input.value += selectedValuesString;
            }

            var session_moderators_input = document.getElementById('session_moderators_input');

            if (session_moderators_input.value && session_moderators_input.value !== ',') {
                session_moderators_input.value += ', ' + selectedValuesString2;
            } else {
                if (session_moderators_input.value !== ',')
                    session_moderators_input.value = selectedValuesString2 ? selectedValuesString2 : ',';
                else
                    session_moderators_input.value += selectedValuesString2;
            }

            var session_dates_input = document.getElementById('session_dates_input');

            if (session_dates_input.value) {
                session_dates_input.value += ', ' + session_date;
            } else {
                session_dates_input.value = session_date;
            }

            var x = document.getElementById("selected_session");
            var option = document.createElement("option");
            option.text = session_title;
            x.add(option);

            // işlem sonrası inputlar temizlenir
            document.getElementById('session_title').value = "";
            //document.getElementById('session_speakers').selectedIndex = -1;

            Swal.fire({
                title: "Success",
                text: "Session has been added.",
                icon: "success"
            });
        }

        function confirmDelete(button, sessionTitle) {
            Swal.fire({
                title: 'Are you sure?',
                text: "This will delete the session.",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#aaa',
                confirmButtonText: 'Yes, delete it!',
                cancelButtonText: 'Cancel'
            }).then((result) => {
                if (result.isConfirmed) {
                    deleteTempSessionAndDocument(button, sessionTitle);
                    Swal.fire('Deleted!', 'The session has been deleted.', 'success');
                }
            });
        }


        function editSession(button) {
            // Butonun bulunduğu satır
            var row = button.closest('tr');

            // Satır hücrelerini al
            var cells = row.getElementsByTagName('td');

            var session_date = cells[0].innerText.trim();
            var session_time_duration = cells[1].innerText.trim();
            var session_title = cells[2].innerText.trim();
            var session_speakers_html = cells[3].innerHTML.trim(); // <br> ile ayrılmış speakerlar
            var session_moderators_html = cells[4].innerHTML.trim();

            // Form inputlarına verileri set et
            // session_dates inputu görünür/gizli olacak $event->duration_pf_event değerine göre:
            var durationPfEvent = parseInt(document.getElementById('duration_of_event').value);

            if (durationPfEvent === 1) {
                document.getElementById('session_dates_div').style.display = 'none';
                document.getElementById('single_session_dates').value = session_date;
            } else {
                document.getElementById('session_dates_div').style.display = 'block';
                document.getElementById('session_dates').value = session_date;
            }

            document.getElementById('session_time_duration').value = session_time_duration;
            document.getElementById('session_title').value = session_title;

            // Checkboxları temizle
            clearCheckboxes('#session_speakers_list');
            clearCheckboxes('#session_moderators_list');

            // Speaker ve moderatorları seçili hale getir
            setCheckboxes('#session_speakers_list', session_speakers_html);
            setCheckboxes('#session_moderators_list', session_moderators_html);

            // Düzenleme sırasında hangi satırın editlendiğini tutmak için formda data attribute ekleyebiliriz
            var sessionDiv = document.querySelector('.session-div');
            sessionDiv.setAttribute('data-editing-row-index', row.rowIndex - 1); // tbody içindeyiz, header yok

            var btn = document.getElementById('session_button');
            btn.innerHTML = '<i class="ti ti-pencil"></i>';
            btn.onclick = function() {
                saveSessionEdit(row);
            };

            Swal.fire({
                title: 'Edit Mode',
                text: 'You are now editing the selected session. Please make your changes and save.',
                icon: 'info',
                confirmButtonText: 'Got it'
            });
        }

        function clearCheckboxes(containerSelector) {
            var checkboxes = document.querySelectorAll(containerSelector + ' input[type="checkbox"]');
            checkboxes.forEach(cb => cb.checked = false);
        }

        function setCheckboxes(containerSelector, htmlContent) {
            // htmlContent <br> ile ayrılmış speaker/moderator isimleri
            var values = htmlContent.split('<br>').map(s => s.trim());
            var checkboxes = document.querySelectorAll(containerSelector + ' input[type="checkbox"]');
            checkboxes.forEach(cb => {
                if (values.includes(cb.value.trim())) {
                    cb.checked = true;
                }
            });
        }


        function saveSessionEdit(row) {
            var session_time_duration = document.getElementById('session_time_duration').value;
            var session_title = document.getElementById('session_title').value;
            var durationPfEvent = parseInt(document.getElementById('duration_of_event').value);
            var session_date = durationPfEvent === 1 ?
                document.getElementById('single_session_dates').value :
                document.getElementById('session_dates').value;

            var session_speakers_list = document.getElementById('session_speakers_list');
            var selectedSpeakers = Array.from(session_speakers_list.querySelectorAll('input[type="checkbox"]:checked'))
                .map(cb => cb.value).join('<br>');

            var session_moderators_list = document.getElementById('session_moderators_list');
            var selectedModerators = Array.from(session_moderators_list.querySelectorAll('input[type="checkbox"]:checked'))
                .map(cb => cb.value).join('<br>');

            if (session_time_duration == '' || session_title == '' || (selectedSpeakers.length === 0 && selectedModerators
                    .length === 0)) {
                alert('Please fill all fields');
                return;
            }

            // Satır hücrelerini güncelle
            var cells = row.getElementsByTagName('td');
            cells[0].innerText = session_date;
            cells[1].innerText = session_time_duration;
            cells[2].innerText = session_title;
            cells[3].innerHTML = selectedSpeakers;
            cells[4].innerHTML = selectedModerators;

            // Gizli inputları tekrar doldur (Tüm satırları dolaşarak)
            updateHiddenInputs();

            // Formu temizle ve butonu eski haline getir
            document.getElementById('session_title').value = '';
            clearCheckboxes('#session_speakers_list');
            clearCheckboxes('#session_moderators_list');

            var btn = document.getElementById('session_button');

            btn.innerHTML = `
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" width="50%" height="50%" fill="white">
  <path d="M433.9 129.9l-83.9-83.9A48 48 0 0 0 316.1 32H48C21.5 32 0 53.5 0 80v352c0 26.5 21.5 48 48 48h352c26.5 0 48-21.5 48-48V163.9a48 48 0 0 0 -14.1-33.9zM224 416c-35.3 0-64-28.7-64-64 0-35.3 28.7-64 64-64s64 28.7 64 64c0 35.3-28.7 64-64 64zm96-304.5V212c0 6.6-5.4 12-12 12H76c-6.6 0-12-5.4-12-12V108c0-6.6 5.4-12 12-12h228.5c3.2 0 6.2 1.3 8.5 3.5l3.5 3.5A12 12 0 0 1 320 111.5z"></path>
</svg>
`;

            btn.onclick = add_session;


            // Düzenleme modunu kaldır
            var sessionDiv = document.querySelector('.session-div');
            sessionDiv.removeAttribute('data-editing-row-index');

            Swal.fire({
                title: "Success",
                text: "Session has been updated.",
                icon: "success"
            });
        }

        function updateHiddenInputs() {
            var table = document.getElementById("sessions_table").getElementsByTagName('tbody')[0];
            var rows = table.rows;

            var dates = [],
                times = [],
                titles = [],
                speakers = [],
                moderators = [];

            for (var i = 0; i < rows.length; i++) {
                var c = rows[i].cells;
                dates.push(c[0].innerText.trim());
                times.push(c[1].innerText.trim());
                titles.push(c[2].innerText.trim());
                speakers.push(c[3].innerHTML.trim());
                moderators.push(c[4].innerHTML.trim());
            }

            document.getElementById('session_dates_input').value = dates.join(', ');
            document.getElementById('session_time_duration_input').value = times.join(', ');
            document.getElementById('session_title_input').value = titles.join(', ');
            document.getElementById('session_speakers_input').value = speakers.join(', ');
            document.getElementById('session_moderators_input').value = moderators.join(', ');
        }



        // Varolan moderator ve speaker görsellerini seçili yap
        document.addEventListener('DOMContentLoaded', function() {
            <?php if($event->moderator_speaker): ?>
                <?php $count = 1; ?>
                <?php $__currentLoopData = $event->moderator_speaker; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php if($item->type == 2): ?>
                        var fileInput = document.getElementById('moderator_banner<?php echo e($count); ?>');
                        var filePath = document.getElementById('moderator_banner_path<?php echo e($count); ?>').value;

                        if (fileInput && filePath) {
                            var dataTransfer = new DataTransfer();
                            var file = new File([filePath], filePath.split('/').pop(), {
                                type: 'image/*'
                            });
                            dataTransfer.items.add(file);
                            fileInput.files = dataTransfer.files;
                        }
                        <?php $count++; ?>
                    <?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                <?php $count = 1; ?>
                <?php $__currentLoopData = $event->moderator_speaker; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php if($item->type == 1): ?>
                        var fileInput = document.getElementById('speaker_banner<?php echo e($count); ?>');
                        var filePath = document.getElementById('speaker_banner_path<?php echo e($count); ?>').value;
                        if (fileInput && filePath) {
                            var dataTransfer = new DataTransfer();
                            var file = new File([filePath], filePath.split('/').pop(), {
                                type: 'image/*'
                            });
                            dataTransfer.items.add(file);
                            fileInput.files = dataTransfer.files;
                        }
                        <?php $count++; ?>
                    <?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php endif; ?>
        });

        // Varolan EventSessions'ları hidden inputa ekle
        document.addEventListener('DOMContentLoaded', function() {
            var sessionTimeDurationInput = document.getElementById('session_time_duration_input');
            var sessionTitleInput = document.getElementById('session_title_input');
            var sessionSpeakersInput = document.getElementById('session_speakers_input');
            var sessionModeratorsInput = document.getElementById('session_moderators_input');
            var sessionDatesInput = document.getElementById('session_dates_input');

            <?php if($event->sessions): ?>
                var sessionTimeDurations = [];
                var sessionTitles = [];
                var sessionSpeakers = [];
                var sessionModerators = [];
                var sessionDates = [];

                <?php $__currentLoopData = $event->sessions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $session): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    sessionTimeDurations.push('<?php echo e($session->session_time_duration); ?>');
                    sessionTitles.push('<?php echo e($session->session_title); ?>');
                    sessionSpeakers.push('<?php echo e($session->session_speakers); ?>');
                    sessionModerators.push('<?php echo e($session->session_moderators); ?>');
                    sessionDates.push('<?php echo e($session->session_dates); ?>');
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                sessionTimeDurationInput.value = sessionTimeDurations.join(', ');
                sessionTitleInput.value = sessionTitles.join(', ');
                sessionSpeakersInput.value = sessionSpeakers.join(', ');
                sessionModeratorsInput.value = sessionModerators.join(', ');
                sessionDatesInput.value = sessionDates.join(', ');
            <?php endif; ?>
        });

        function deleteTempSessionAndDocument(button, sessionTitle) {
            var row = button.closest('tr');
            row.remove();

            var x = document.getElementById("selected_session");
            for (var i = 0; i < x.length; i++) {
                if (x.options[i].text == sessionTitle) {
                    x.remove(i);
                }
            }
        }

        function deleteSession(button, sessionId, sesionTitle) {
            if (confirm('Are you sure you want to delete this session?')) {
                fetch(`/admin/delete-session/${sessionId}`, {
                        method: 'DELETE',
                        headers: {
                            'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            var row = button.closest('tr');
                            row.remove();

                            var x = document.getElementById("selected_session");
                            for (var i = 0; i < x.length; i++) {
                                if (x.options[i].text == sesionTitle) {
                                    x.remove(i);
                                }
                            }

                            location.reload();
                        } else {
                            alert(data.error);
                        }
                    })
                    .catch(error => console.error('Error:', error));
            }
        }

        function deleteDocument(button, documentId) {
            if (confirm('Are you sure you want to delete this document?')) {
                fetch(`/admin/delete-document/${documentId}`, {
                        method: 'DELETE',
                        headers: {
                            'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            var row = button.closest('tr');
                            row.remove();
                        } else {
                            alert(data.error);
                        }
                    })
                    .catch(error => console.error('Error:', error));
            }
        }

        // Event Clone
        document.querySelector('.btn.clone-event').addEventListener('click', function() {
            Swal.fire({
                title: 'Are you sure?',
                text: 'Do you want to clone this event?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, clone it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    // Add your clone event logic here
                    /*Swal.fire(
                        'Cloned!',
                        'The event has been cloned.',
                        'success'
                    );*/
                    window.location.href = '<?php echo e(route('panel.new_event')); ?>?clone=<?php echo e($event->id); ?>';

                }
            });
        });

        function add_new_date() {
            var row = document.querySelector('.date-time-row');

            var dateColumn = row.querySelector('.col-md-4:nth-child(1)');
            var timeColumn = row.querySelector('.col-md-4:nth-child(2)');
            var timezoneColumn = row.querySelector('.col-md-4:nth-child(3)');
            var locationColumn = row.querySelector('.col-md-8');
            var countryColumn = row.querySelector('.col-md-4:nth-child(5)');
            var locationUrlColumn = row.querySelector('.col-md-12');

            var newDateColumn = dateColumn.cloneNode(true);
            var newTimeColumn = timeColumn.cloneNode(true);
            var newTimezoneColumn = timezoneColumn.cloneNode(true);
            var newLocationColumn = locationColumn.cloneNode(true);
            var newCountryColumn = countryColumn.cloneNode(true);
            var newLocationUrlColumn = locationUrlColumn.cloneNode(true);

            //
            $(newTimezoneColumn).find('.select2-container').remove();
            $(newCountryColumn).find('.select2-container').remove();

            //
            var newTimezoneSelect = newTimezoneColumn.querySelector('select');
            var newCountrySelect = newCountryColumn.querySelector('select');

            //
            var uniqueId = Date.now();
            newTimezoneColumn.querySelector('select').id = "event_timezone_" + uniqueId;
            newCountryColumn.querySelector('select').id = "event_country_" + uniqueId;


            /*var inputsDate = newDateColumn.querySelectorAll('input, select');
            inputsDate.forEach(function(input) {
                input.value = '';
            });*/

            var inputsTime = newTimeColumn.querySelectorAll('input, select');
            inputsTime.forEach(function(input) {
                input.value = '';
            });

            var inputsTimezone = newTimezoneColumn.querySelectorAll('input, select');
            inputsTimezone.forEach(function(input) {
                input.value = '';
            });

            var inputsLocation = newLocationColumn.querySelectorAll('input, select');
            inputsLocation.forEach(function(input) {
                input.value = '';
            });

            var inputsCountry = newCountryColumn.querySelectorAll('input, select');
            inputsCountry.forEach(function(input) {
                input.value = '';
            });

            var inputsLocationUrl = newLocationUrlColumn.querySelectorAll('input');
            inputsLocationUrl.forEach(function(input) {
                input.value = ''; // ✅ Location URL temizleme
            });

            var newRow = document.createElement('div');
            newRow.classList.add('row', 'date-time-row');
            newRow.appendChild(newDateColumn);
            newRow.appendChild(newTimeColumn);
            newRow.appendChild(newTimezoneColumn);
            newRow.appendChild(newLocationColumn);
            newRow.appendChild(newCountryColumn);
            newRow.appendChild(newLocationUrlColumn);

            //
            let selectedTimezone = $("#event_date_timezone").val();
            let selectedCountry = $("#event_country").val();

            $(newTimezoneSelect).val(selectedTimezone);
            $(newCountrySelect).val(selectedCountry);

            //
            $(newTimezoneSelect).select2({
                placeholder: "Africa/Johannesburg",
                allowClear: true
            }).val(selectedTimezone).trigger('change');

            $(newCountrySelect).select2({
                placeholder: "Afghanistan",
                allowClear: true
            }).val(selectedCountry).trigger('change');

            $(document).ready(function() {
                $('#event_country, #event_country_clone').select2({
                    placeholder: "Afghanistan",
                    allowClear: true,
                });

                $('#event_date_timezone, #event_timezone_clone').select2({
                    placeholder: "Africa/Johannesburg",
                    allowClear: true,
                });

                $(document).ready(function() {
                    // Yeni klonlar için select2 uygulaması
                    $('#event_country_' + uniqueId).select2({
                        placeholder: "Afghanistan",
                        allowClear: true,
                    });

                    $('#event_timezone_' + uniqueId).select2({
                        placeholder: "Africa/Johannesburg",
                        allowClear: true,
                    });
                });
            });

            // Silme butonunu ekle
            var removeButton = document.createElement('button');
            removeButton.textContent = 'Delete';
            removeButton.classList.add('btn', 'btn-danger', 'remove-btn');
            removeButton.onclick = function() {
                // Silinen tarih, eventDates array'inden çıkarılır
                var dateToRemove = newDateColumn.querySelector('input').value;
                eventDates = eventDates.filter(function(date) {
                    return date !== dateToRemove;
                });

                // Silme butonuna basıldığında, selectbox'dan da bu tarihi kaldır
                update_session_speakers();

                newRow.remove();
            };

            // Silme butonunu satıra ekle
            var removeColumn = document.createElement('div');
            removeColumn.classList.add('col-md-4');
            removeColumn.appendChild(removeButton);
            newRow.appendChild(removeColumn);

            document.getElementById('date-time-container').appendChild(newRow);

            // Yeni event date input için daterangepicker'ı başlat
            var newEventDateInput = newDateColumn.querySelector('input');
            $(newEventDateInput).daterangepicker({
                singleDatePicker: true,
                showDropdowns: true,
                autoUpdateInput: false,
                locale: {
                    format: 'DD/MM/YYYY'
                }
            }).on('apply.daterangepicker', function(ev, picker) {
                $(this).val(picker.startDate.format('DD/MM/YYYY'));
                // Seçilen tarihi eventDates array'ine ekle
                eventDates.push($(this).val());
                update_session_speakers();
            });

            // Enable keyboard input for the new date field
            $(newEventDateInput).on('keyup.dateInput', function(e) {
                var value = $(this).val();
                var datePattern = /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/;

                if (datePattern.test(value)) {
                    var parts = value.match(datePattern);
                    var day = parseInt(parts[1], 10);
                    var month = parseInt(parts[2], 10);
                    var year = parseInt(parts[3], 10);

                    if (day >= 1 && day <= 31 && month >= 1 && month <= 12 && year >= 1900 && year <= 2100) {
                        var date = moment(value, 'DD/MM/YYYY');
                        if (date.isValid()) {
                            $(this).data('daterangepicker').setStartDate(date);
                            $(this).data('daterangepicker').setEndDate(date);

                            // Update eventDates array
                            var oldValue = eventDates.find(d => d === $(this).data('oldValue'));
                            if (oldValue) {
                                var index = eventDates.indexOf(oldValue);
                                eventDates[index] = value;
                            } else {
                                eventDates.push(value);
                            }
                            $(this).data('oldValue', value);
                            update_session_speakers();
                        }
                    }
                }
            });

            // new event time input mdtimepicker
            var newEventTimeInput = newTimeColumn.querySelector('input');
            var newEventTimeIcon = newTimeColumn.querySelector('.timepicker_icon');

            // Remove readonly attribute to allow keyboard input
            newEventTimeInput.removeAttribute('readonly');

            const now = new Date();
            const start = window.formatTime ? window.formatTime(now) : formatTime(now);
            const end = window.formatTime ? window.formatTime(new Date(now.getTime() + 2 * 60 * 60 * 1000)) : formatTime(
                new Date(now.getTime() + 2 * 60 * 60 * 1000));

            newEventTimeInput.value = `${start} - ${end}`;

            const state = {
                start: start,
                end: end
            };

            function timeToMinutes(timeStr) {
                const [h, m, ampm] = timeStr.split(/[ :]/);
                let hours = parseInt(h);
                if (ampm === 'PM' && hours !== 12) hours += 12;
                if (ampm === 'AM' && hours === 12) hours = 0;
                return hours * 60 + parseInt(m);
            }

            function openTimePicker() {
                const $startTemp = $('<input type="text" />').appendTo('body').hide().val(state.start);

                $startTemp.mdtimepicker({
                    theme: 'blue',
                    value: state.start
                }).data('mdtimepicker').show();

                $startTemp.on('timechanged', function(e1) {
                    state.start = e1.value;

                    const $endTemp = $('<input type="text" />').appendTo('body').hide().val(state.end);
                    $endTemp.mdtimepicker({
                        theme: 'blue',
                        value: state.end
                    }).data('mdtimepicker').show();

                    // label'ı "End" olarak güncelle
                    $('.mdtp__time_label').text('End:');

                    $endTemp.on('timechanged', function(e2) {
                        state.end = e2.value;

                        if (timeToMinutes(state.end) <= timeToMinutes(state.start)) {
                            alert('End time must be after start time.');
                            return;
                        }

                        newEventTimeInput.value = `${state.start} - ${state.end}`;
                        $endTemp.mdtimepicker('destroy').remove();
                    });

                    $endTemp.on('hide.timepicker', function() {
                        $endTemp.remove();
                    });

                    $startTemp.mdtimepicker('destroy').remove();
                });

                $startTemp.on('hide.timepicker', function() {
                    $startTemp.remove();
                });
            }

            newEventTimeInput.addEventListener('click', openTimePicker);
            newEventTimeIcon.addEventListener('click', openTimePicker);

            // Enable keyboard input for the new time input
            $(newEventTimeInput).on('keyup', function(e) {
                var value = $(this).val();
                var timePattern = /^(\d{1,2}):(\d{2})\s*(AM|PM)\s*-\s*(\d{1,2}):(\d{2})\s*(AM|PM)$/i;

                if (timePattern.test(value)) {
                    var parts = value.match(timePattern);
                    var startHour = parseInt(parts[1], 10);
                    var startMinute = parseInt(parts[2], 10);
                    var startAmPm = parts[3].toUpperCase();
                    var endHour = parseInt(parts[4], 10);
                    var endMinute = parseInt(parts[5], 10);
                    var endAmPm = parts[6].toUpperCase();

                    // Validate time ranges
                    if (startHour >= 1 && startHour <= 12 && startMinute >= 0 && startMinute <= 59 &&
                        endHour >= 1 && endHour <= 12 && endMinute >= 0 && endMinute <= 59 &&
                        (startAmPm === 'AM' || startAmPm === 'PM') && (endAmPm === 'AM' || endAmPm === 'PM')) {

                        var startTime = moment(startHour + ':' + startMinute.toString().padStart(2, '0') + ' ' + startAmPm, 'h:mm A');
                        var endTime = moment(endHour + ':' + endMinute.toString().padStart(2, '0') + ' ' + endAmPm, 'h:mm A');

                        if (startTime.isValid() && endTime.isValid() && endTime.isAfter(startTime)) {
                            state.start = startTime.format('h:mm A');
                            state.end = endTime.format('h:mm A');
                        }
                    }
                }
            });
        }

        function update_session_speakers() {
            let event_dates = Array.from(document.querySelectorAll('input[name="event_date[]"]')).map(input => input.value);

            /*let selectElement = document.getElementById('session_dates');
            selectElement.innerHTML = '';

            event_dates.forEach(date => {
                const option = document.createElement('option');
                option.value = date;
                option.textContent = date;
                selectElement.appendChild(option);
            });*/
        }

        function remove_date_row(id) {
            document.getElementById('date_row_' + id).remove();
            update_session_speakers();
        }

        function set_single_date(element) {
            if (document.getElementById('duration_of_event').value == 1) {
                document.getElementById('single_session_dates').value = element.value;
            } else {
                update_session_speakers();
            }
        }
    </script>

    <script>
        const timeSession = document.querySelector(".time-icon-session");

        if (document.querySelector(".date-icon")) {
            const date = document.querySelector(".date-icon");
            date.addEventListener("click", () => {
                document.querySelector(".event_date_overview").click();
            })
        }

        if (document.querySelector(".time-icon")) {
            const time = document.querySelector(".time-icon");
            time.addEventListener("click", () => {
                document.querySelector(".event_time_overview").click();
            })
        }

        timeSession.addEventListener("click", () => {
            document.querySelector("#session_time_duration").click();
        })
    </script>

    <script>
        document.getElementById('saveDraftBtn').addEventListener('click', function() {
            submitMode = "draft";
            document.getElementById('submitTypeInput').value = 2;
            document.getElementById('event_form').requestSubmit();
        });

        document.getElementById('completeSaveBtn').addEventListener('click', function() {
            submitMode = "complete";
            document.getElementById('submitTypeInput').value = 1;
            document.getElementById('event_form').requestSubmit();
        });

        document.getElementById('event_form').addEventListener('submit', function() {
            isFormSubmitted = true;

            // Add toggle values to form before submit
            const userCheckInToggle = document.getElementById('user_check_in');
            const badgeCreationToggle = document.getElementById('badge_creation');
            const attendeesInfoToggle = document.getElementById('attendees_info');

            // Create or update hidden inputs for toggle values
            updateOrCreateHiddenInput('user_check_in', userCheckInToggle ? (userCheckInToggle.checked ? '1' : '0') : '0');
            updateOrCreateHiddenInput('badge_creation', badgeCreationToggle ? (badgeCreationToggle.checked ? '1' : '0') : '0');
            updateOrCreateHiddenInput('attendees_info', attendeesInfoToggle ? (attendeesInfoToggle.checked ? '1' : '0') : '0');

            if (submitMode === "complete") {
                const missingFields = [];

                // Tüm data-label'li alanları kontrol et
                const requiredFields = document.querySelectorAll('#event_form [data-label]');
                requiredFields.forEach(function(field) {
                    if (!field.value.trim()) {
                        missingFields.push(field.getAttribute('data-label'));
                    }
                });

                if (missingFields.length > 0) {
                    Swal.fire({
                        title: "Missing Fields",
                        html: "Please fill out the following fields:<br><ul style='text-align:left'>" +
                            missingFields.map(f => `<li>${f}</li>`).join('') +
                            "</ul>",
                        icon: "warning"
                    });
                    event.preventDefault();
                    return;
                }

                // Event location karakter kontrolü
                const eventLocations = document.querySelectorAll('input[name="event_location[]"]');
                for (let i = 0; i < eventLocations.length; i++) {
                    const eventLocation = eventLocations[i].value.trim();
                    if (eventLocation.length > 500) {
                        Swal.fire({
                            title: "Warning",
                            text: "Event Location cannot exceed 500 characters!",
                            icon: "warning"
                        });
                        event.preventDefault();
                        return;
                    }
                }

                // Görsel kontrolü
                const fileInput = document.getElementById("event_banner");
                const file = fileInput.files[0];
                if (file) {
                    const fileExtension = file.name.split('.').pop().toLowerCase();
                    const validExtensions = ["jpg", "jpeg", "png", "gif"];
                    if (!validExtensions.includes(fileExtension)) {
                        Swal.fire({
                            title: "Warning",
                            text: "Please select a valid image file (jpg, jpeg, png, gif).",
                            icon: "warning"
                        });
                        event.preventDefault();
                        return;
                    }
                }
            }
        });
    </script>


    <script>
        //speaker checkbox
        (function($) {
            var CheckboxDropdown = function(el) {
                var _this = this;
                this.isOpen = false;
                this.areAllChecked = false;
                this.$el = $(el);
                this.$label = this.$el.find('.dropdown-label');
                this.$checkAll = this.$el.find('[data-toggle="check-all"]').first();
                this.$inputs = this.$el.find('[type="checkbox"]');

                this.onCheckBox();

                this.$label.on('click', function(e) {
                    e.preventDefault();
                    _this.toggleOpen();
                });

                this.$checkAll.on('click', function(e) {
                    e.preventDefault();
                    _this.onCheckAll();
                });

                this.$inputs.on('change', function(e) {
                    _this.onCheckBox();
                });
            };

            CheckboxDropdown.prototype.onCheckBox = function() {
                this.updateStatus();
            };

            CheckboxDropdown.prototype.updateStatus = function() {
                var checked = this.$el.find(':checked');

                this.areAllChecked = false;
                this.$checkAll.html('Check All');

                if (checked.length <= 0) {
                    this.$label.html('Speakers');
                }
            };

            CheckboxDropdown.prototype.onCheckAll = function(checkAll) {
                if (!this.areAllChecked || checkAll) {
                    this.areAllChecked = true;
                    this.$checkAll.html('Uncheck All');
                    this.$inputs.prop('checked', true);
                } else {
                    this.areAllChecked = false;
                    this.$checkAll.html('Check All');
                    this.$inputs.prop('checked', false);
                }

                this.updateStatus();
            };

            CheckboxDropdown.prototype.toggleOpen = function(forceOpen) {
                var _this = this;

                if (!this.isOpen || forceOpen) {
                    this.isOpen = true;
                    this.$el.addClass('on');
                    $(document).on('click', function(e) {
                        if (!$(e.target).closest('[data-control]').length) {
                            _this.toggleOpen();
                        }
                    });
                } else {
                    this.isOpen = false;
                    this.$el.removeClass('on');
                    $(document).off('click');
                }
            };

            var checkboxesDropdowns = document.querySelectorAll('[data-control="checkbox-dropdown"]');
            for (var i = 0, length = checkboxesDropdowns.length; i < length; i++) {
                new CheckboxDropdown(checkboxesDropdowns[i]);
            }

            document.querySelector("#session_button").addEventListener("click", function() {
                document.querySelectorAll('[data-control="checkbox-dropdown"] [type="checkbox"]').forEach(
                    function(checkbox) {
                        checkbox.checked = false;
                    });
                document.querySelectorAll('[data-control="checkbox-dropdown"] .dropdown-label').forEach(
                    function(label) {
                        label.innerHTML = 'Speakers';
                    });
            });
        })(jQuery);

        (function($) {
            var CheckboxDropdown2 = function(el) {
                var _this = this;
                this.isOpen = false;
                this.areAllChecked = false;
                this.$el = $(el);
                this.$label = this.$el.find('.dropdown-label');
                this.$checkAll = this.$el.find('[data-toggle="check-all"]').first();
                this.$inputs = this.$el.find('[type="checkbox"]');

                this.onCheckBox();

                this.$label.on('click', function(e) {
                    e.preventDefault();
                    _this.toggleOpen();
                });

                this.$checkAll.on('click', function(e) {
                    e.preventDefault();
                    _this.onCheckAll();
                });

                this.$inputs.on('change', function(e) {
                    _this.onCheckBox();
                });
            };

            CheckboxDropdown2.prototype.onCheckBox = function() {
                this.updateStatus();
            };

            CheckboxDropdown2.prototype.updateStatus = function() {
                var checked = this.$el.find(':checked');

                this.areAllChecked = false;
                this.$checkAll.html('Check All');

                if (checked.length <= 0) {
                    this.$label.html('Moderators');
                }
            };

            CheckboxDropdown2.prototype.onCheckAll = function(checkAll) {
                if (!this.areAllChecked || checkAll) {
                    this.areAllChecked = true;
                    this.$checkAll.html('Uncheck All');
                    this.$inputs.prop('checked', true);
                } else {
                    this.areAllChecked = false;
                    this.$checkAll.html('Check All');
                    this.$inputs.prop('checked', false);
                }

                this.updateStatus();
            };

            CheckboxDropdown2.prototype.toggleOpen = function(forceOpen) {
                var _this = this;

                if (!this.isOpen || forceOpen) {
                    this.isOpen = true;
                    this.$el.addClass('on');
                    $(document).on('click', function(e) {
                        if (!$(e.target).closest('[data-control]').length) {
                            _this.toggleOpen();
                        }
                    });
                } else {
                    this.isOpen = false;
                    this.$el.removeClass('on');
                    $(document).off('click');
                }
            };

            var checkboxesDropdowns = document.querySelectorAll('[data-control="checkbox-dropdown-moderator"]');
            for (var i = 0, length = checkboxesDropdowns.length; i < length; i++) {
                new CheckboxDropdown2(checkboxesDropdowns[i]);
            }

            document.querySelector("#session_button").addEventListener("click", function() {
                document.querySelectorAll('[data-control="checkbox-dropdown-moderator"] [type="checkbox"]')
                    .forEach(
                        function(checkbox) {
                            checkbox.checked = false;
                        });
                document.querySelectorAll('[data-control="checkbox-dropdown-moderators"] .dropdown-label')
                    .forEach(
                        function(label) {
                            label.innerHTML = 'Moderators';
                        });
            });
        })(jQuery);


        function getTimeByDate(targetDate) {
            const dateInputs = document.querySelectorAll('.event_date_overview');

            for (let dateInput of dateInputs) {
                if (dateInput.value === targetDate) {
                    const dateRow = dateInput.closest('.date-time-row');
                    const timeInput = dateRow.querySelector('.event_time_overview');

                    if (timeInput) {
                        return timeInput.value;
                    }
                }
            }

            return null;
        }

        function isTimeRangeInside(range1, range2) {
            function timeStringToMinutes(str) {
                const [time, modifier] = str.trim().split(' ');
                let [hours, minutes] = time.split(':').map(Number);

                if (modifier === 'PM' && hours !== 12) {
                    hours += 12;
                }
                if (modifier === 'AM' && hours === 12) {
                    hours = 0;
                }

                return hours * 60 + minutes;
            }

            const [start1, end1] = range1.split('-').map(s => s.trim());
            const [start2, end2] = range2.split('-').map(s => s.trim());

            const start1Min = timeStringToMinutes(start1);
            const end1Min = timeStringToMinutes(end1);
            const start2Min = timeStringToMinutes(start2);
            const end2Min = timeStringToMinutes(end2);

            return start2Min >= start1Min && end2Min <= end1Min;
        }

        // Helper function to create or update hidden inputs
        function updateOrCreateHiddenInput(name, value) {
            let hiddenInput = document.querySelector(`input[name="${name}"]`);
            if (!hiddenInput) {
                hiddenInput = document.createElement('input');
                hiddenInput.type = 'hidden';
                hiddenInput.name = name;
                document.getElementById('event_form').appendChild(hiddenInput);
            }
            hiddenInput.value = value;
        }
    </script>

    <?php if(session('success')): ?>
        <script>
            Swal.fire({
                title: "Success",
                text: "<?php echo e(session('success')); ?>",
                icon: "success"
            });
        </script>
    <?php endif; ?>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\bd-qr-badge\resources\views/event/event_detail/overview.blade.php ENDPATH**/ ?>