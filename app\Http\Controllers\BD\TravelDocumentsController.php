<?php

namespace App\Http\Controllers\BD;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use App\Models\Event;
use App\Models\Register;
use App\Models\AttendeesInfo;
use Carbon\Carbon;

class TravelDocumentsController extends Controller
{
    /**
     * Show travel documents page
     */
    public function index()
    {
        $user = Session::get('bd_user');

        if (!$user) {
            return redirect()->route('bd.login');
        }

        // Get user's registered events only
        $registeredEventIds = Register::where('email', $user->email)
            ->where('status', 1)
            ->pluck('event_id')
            ->toArray();

        $events = Event::whereIn('id', $registeredEventIds)
            ->where('status', 1)
            ->where('attendees_info', 1) // Active events only
            ->with('event_dates') // Load event dates
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function ($event) {
                return $this->formatEventForSelect($event);
            });

        return view('bd.travel-documents', compact('user', 'events'));
    }

    /**
     * Format event data for select dropdown
     */
    private function formatEventForSelect($event)
    {
        // Get the first event date
        $eventDate = $event->event_dates->first();

        if ($eventDate) {
            $startDate = Carbon::parse($eventDate->event_date);
            $dateText = $startDate->format('M d, Y');
        } else {
            $dateText = 'Date TBA';
        }

        return (object) [
            'id' => $event->id,
            'title' => $event->event_title,
            'date' => $dateText,
            'display_text' => $event->event_title . ' - ' . $dateText
        ];
    }

    /**
     * Get event details for travel documents
     */
    public function getEventDetails(Request $request)
    {
        try {
            $user = Session::get('bd_user');

            \Log::info('Travel Documents - getEventDetails called', [
                'user' => $user ? $user->email : 'No user',
                'event_id' => $request->event_id,
                'request_data' => $request->all()
            ]);

            if (!$user) {
                return response()->json(['error' => 'Unauthorized'], 401);
            }

            $eventId = $request->event_id;

            if (!$eventId) {
                return response()->json(['error' => 'Event ID is required'], 400);
            }

        // Verify user is registered for this event
        $isRegistered = Register::where('email', $user->email)
            ->where('event_id', $eventId)
            ->exists();

        if (!$isRegistered) {
            return response()->json(['error' => 'You are not registered for this event'], 403);
        }

        // Get event details
        $event = Event::with('event_dates')->find($eventId);

        if (!$event) {
            return response()->json(['error' => 'Event not found'], 404);
        }

        $eventDate = $event->event_dates->first();

        // Get user's register ID for this event
        $register = Register::where('email', $user->email)
            ->where('event_id', $eventId)
            ->first();

        // Get existing attendees info (uploaded documents)
        $attendeesInfo = null;
        $existingFiles = [];

        if ($register) {
            $attendeesInfo = AttendeesInfo::where('event_id', $eventId)
                ->where('register_id', $register->id)
                ->first();

            if ($attendeesInfo) {
                $existingFiles = [
                    'flight' => $attendeesInfo->flight_information,
                    'hotel' => $attendeesInfo->hotel_information,
                    'passport' => $attendeesInfo->passport_details
                ];
            }
        }

            $response = [
                'success' => true,
                'event' => [
                    'id' => $event->id,
                    'title' => $event->event_title,
                    'banner' => $event->event_banner ? asset(env('STORAGE_PATH') . '/event_banners/' . $event->event_banner) : asset('assets/bd/images/event.png'),
                    'description' => $event->event_description,
                    'date' => $eventDate ? $eventDate->event_date : null,
                    'start_time' => $eventDate ? $eventDate->event_start_time : null,
                    'end_time' => $eventDate ? $eventDate->event_end_time : null,
                    'location' => $eventDate ? $eventDate->event_location : null,
                    'country' => $eventDate ? $eventDate->event_country : null
                ],
                'existing_files' => $existingFiles,
                'register_id' => $register ? $register->id : null
            ];

            \Log::info('Travel Documents - Response prepared', $response);

            return response()->json($response);

        } catch (\Exception $e) {
            \Log::error('Travel Documents - getEventDetails error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'event_id' => $request->event_id ?? 'N/A'
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Internal server error: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Upload travel documents
     */
    public function uploadDocuments(Request $request)
    {
        $user = Session::get('bd_user');

        if (!$user) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $eventId = $request->event_id;

        // Verify user is registered for this event
        $isRegistered = Register::where('email', $user->email)
            ->where('event_id', $eventId)
            ->exists();

        if (!$isRegistered) {
            return response()->json(['error' => 'You are not registered for this event'], 403);
        }

        try {
            // Get user's register ID for this event
            $register = Register::where('email', $user->email)
                ->where('event_id', $eventId)
                ->first();

            if (!$register) {
                return response()->json(['error' => 'Registration not found'], 404);
            }

            // Find or create attendees info record
            $attendeesInfo = AttendeesInfo::firstOrCreate(
                [
                    'event_id' => $eventId,
                    'register_id' => $register->id
                ],
                [
                    'flight_information' => null,
                    'hotel_information' => null,
                    'passport_details' => null,
                ]
            );

            $uploadedFiles = [];

            // Handle file uploads
            $fileTypes = [
                'flight' => 'flight_information',
                'hotel' => 'hotel_information',
                'passport' => 'passport_details'
            ];

            foreach ($fileTypes as $inputName => $dbField) {
                if ($request->hasFile($inputName . '_file')) {
                    $file = $request->file($inputName . '_file');
                    $fileName = $inputName . '_' . $register->id . '_' . time() . '.' . $file->getClientOriginalExtension();

                    // Store file in attendees_files directory
                    $path = $file->storeAs('public/attendees_files', $fileName);

                    // Delete old file if exists
                    if ($attendeesInfo->$dbField) {
                        \Storage::delete('public/attendees_files/' . $attendeesInfo->$dbField);
                    }

                    // Update database
                    $attendeesInfo->$dbField = $fileName;
                    $uploadedFiles[$inputName] = $fileName;
                }
            }

            // Save the record
            $attendeesInfo->save();

            return response()->json([
                'success' => true,
                'message' => 'Documents uploaded successfully',
                'uploaded_files' => $uploadedFiles
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Upload failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Download travel document
     */
    public function downloadDocument(Request $request)
    {
        $user = Session::get('bd_user');

        if (!$user) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $eventId = $request->event_id;
        $fileType = $request->file_type; // flight, hotel, passport

        // Verify user is registered for this event
        $register = Register::where('email', $user->email)
            ->where('event_id', $eventId)
            ->first();

        if (!$register) {
            return response()->json(['error' => 'You are not registered for this event'], 403);
        }

        // Get attendees info
        $attendeesInfo = AttendeesInfo::where('event_id', $eventId)
            ->where('register_id', $register->id)
            ->first();

        if (!$attendeesInfo) {
            return response()->json(['error' => 'No documents found'], 404);
        }

        // Get file name based on type
        $fileName = null;
        switch ($fileType) {
            case 'flight':
                $fileName = $attendeesInfo->flight_information;
                break;
            case 'hotel':
                $fileName = $attendeesInfo->hotel_information;
                break;
            case 'passport':
                $fileName = $attendeesInfo->passport_details;
                break;
            default:
                return response()->json(['error' => 'Invalid file type'], 400);
        }

        if (!$fileName) {
            return response()->json(['error' => 'File not found'], 404);
        }

        $filePath = storage_path('app/public/attendees_files/' . $fileName);

        if (!file_exists($filePath)) {
            return response()->json(['error' => 'File does not exist'], 404);
        }

        return response()->download($filePath, $fileName);
    }

    /**
     * Delete travel document
     */
    public function deleteDocument(Request $request)
    {
        $user = Session::get('bd_user');

        if (!$user) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $eventId = $request->event_id;
        $fileType = $request->file_type; // flight, hotel, passport

        // Verify user is registered for this event
        $register = Register::where('email', $user->email)
            ->where('event_id', $eventId)
            ->first();

        if (!$register) {
            return response()->json(['error' => 'You are not registered for this event'], 403);
        }

        // Get attendees info
        $attendeesInfo = AttendeesInfo::where('event_id', $eventId)
            ->where('register_id', $register->id)
            ->first();

        if (!$attendeesInfo) {
            return response()->json(['error' => 'No documents found'], 404);
        }

        try {
            // Get file name and database field based on type
            $fileName = null;
            $dbField = null;

            switch ($fileType) {
                case 'flight':
                    $fileName = $attendeesInfo->flight_information;
                    $dbField = 'flight_information';
                    break;
                case 'hotel':
                    $fileName = $attendeesInfo->hotel_information;
                    $dbField = 'hotel_information';
                    break;
                case 'passport':
                    $fileName = $attendeesInfo->passport_details;
                    $dbField = 'passport_details';
                    break;
                default:
                    return response()->json(['error' => 'Invalid file type'], 400);
            }

            if (!$fileName) {
                return response()->json(['error' => 'File not found'], 404);
            }

            // Delete file from storage
            $filePath = 'public/attendees_files/' . $fileName;
            if (\Storage::exists($filePath)) {
                \Storage::delete($filePath);
            }

            // Update database - set field to null
            $attendeesInfo->$dbField = null;
            $attendeesInfo->save();

            return response()->json([
                'success' => true,
                'message' => ucfirst($fileType) . ' document deleted successfully'
            ]);

        } catch (\Exception $e) {
            \Log::error('Travel Documents - Delete error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'event_id' => $eventId,
                'file_type' => $fileType
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Delete failed: ' . $e->getMessage()
            ], 500);
        }
    }
}
