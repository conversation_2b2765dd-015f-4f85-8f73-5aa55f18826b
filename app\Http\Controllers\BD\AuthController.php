<?php

namespace App\Http\Controllers\BD;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Mail;
use App\Models\Register;
use App\Mail\BDOtpMail;

class AuthController extends Controller
{
    /**
     * Show login form (email input)
     */
    public function showLogin()
    {
        // If already authenticated, redirect to dashboard
        if (Session::has('bd_user')) {
            return redirect('/');
        }

        return view('bd.auth.login');
    }

    /**
     * Send OTP to email
     */
    public function sendOtp(Request $request)
    {
        $request->validate([
            'email' => 'required|email'
        ]);

        // Check if email exists in register table
        $register = Register::where('email', $request->email)->first();

        if (!$register) {
            return back()->withErrors(['email' => 'Email not found in our records.'])->withInput();
        }

        // Generate 6-digit OTP
        $otp = str_pad(rand(0, 999999), 6, '0', STR_PAD_LEFT);

        // Update register table with OTP
        $register->update(['otp' => $otp]);

        // Store email in session for OTP verification
        Session::put('bd_otp_email', $request->email);

        try {
            // Send OTP email
            $userName = trim($register->first_name . ' ' . $register->last_name);

            // Always show OTP in development for now
            if (config('app.env') === 'local') {
                // For development, show OTP directly and also try to send email
                \Log::info('OTP Email (Development Mode)', [
                    'to' => $request->email,
                    'otp' => $otp,
                    'user' => $userName,
                    'mail_config' => [
                        'mailer' => config('mail.default'),
                        'host' => config('mail.mailers.smtp.host'),
                        'from' => config('mail.from.address'),
                        'username' => config('mail.mailers.smtp.username')
                    ]
                ]);

                // Try to send email but don't fail if it doesn't work
                try {
                    $mail = new BDOtpMail($otp, $userName);
                    Mail::to($request->email)->send($mail);
                    \Log::info('OTP Email sent successfully to: ' . $request->email);
                } catch (\Exception $mailError) {
                    \Log::error('Mail sending failed in development', [
                        'error' => $mailError->getMessage(),
                        'trace' => $mailError->getTraceAsString()
                    ]);
                }

                return redirect()->route('bd.otp')->with('success', "Development Mode: OTP is {$otp}. Email sending attempted - check logs.");
            }

            // Production mode - try to send email
            $mail = new BDOtpMail($otp, $userName);
            Mail::to($request->email)->send($mail);

            return redirect()->route('bd.otp')->with('success', "OTP has been sent to {$request->email}. Please check your email.");
        } catch (\Exception $e) {
            // Log the detailed error
            \Log::error('Failed to send OTP email', [
                'email' => $request->email,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'mail_config' => [
                    'driver' => config('mail.default'),
                    'host' => config('mail.mailers.smtp.host'),
                    'from' => config('mail.from.address')
                ]
            ]);

            // Always show OTP in case of error
            return redirect()->route('bd.otp')->with('warning', "Email sending failed, but you can continue. OTP: {$otp}");
        }
    }

    /**
     * Show OTP verification form
     */
    public function showOtp()
    {
        $email = Session::get('bd_otp_email');

        if (!$email) {
            return redirect()->route('bd.login')->with('error', 'Please enter your email first.');
        }

        return view('bd.auth.otp', compact('email'));
    }

    /**
     * Verify OTP and login
     */
    public function verifyOtp(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'otp' => 'required|string|min:1'
        ]);

        // Find register with email first
        $register = Register::where('email', $request->email)->first();

        if (!$register) {
            \Log::error('Register not found for email: ' . $request->email);
            return back()->withErrors(['otp' => 'Email not found in our records.'])->withInput();
        }
        // Check OTP match
        if ($register->otp !== $request->otp) {
            \Log::error('OTP mismatch', [
                'stored_otp' => $register->otp,
                'input_otp' => $request->otp
            ]);
            return back()->withErrors(['otp' => 'Invalid OTP. Please try again.'])->withInput();
        }

        // Clear OTP after successful verification
        $register->update(['otp' => null]);

        // Create user session
        $user = (object) [
            'id' => $register->user_id,
            'name' => trim($register->first_name . ' ' . $register->last_name),
            'email' => $register->email,
            'title' => $register->title ?? 'Participant',
            'avatar' => asset('assets/bd/images/user.png'),
            'company' => $register->company,
            'country' => $register->country
        ];

        Session::put('bd_user', $user);
        Session::forget('bd_otp_email');

        return redirect('/')->with('success', 'Welcome to BD Events Dashboard!');
    }

    /**
     * Resend OTP
     */
    public function resendOtp(Request $request)
    {
        $request->validate([
            'email' => 'required|email'
        ]);

        $register = Register::where('email', $request->email)->first();

        if (!$register) {
            return response()->json(['success' => false, 'message' => 'Email not found.']);
        }

        // Generate new OTP
        $otp = str_pad(rand(0, 999999), 6, '0', STR_PAD_LEFT);
        $register->update(['otp' => $otp]);

        try {
            // Send OTP email
            $userName = trim($register->first_name . ' ' . $register->last_name);

            // Always show OTP in development
            if (config('app.env') === 'local') {
                \Log::info('Resend OTP Email (Development Mode)', [
                    'to' => $request->email,
                    'otp' => $otp,
                    'user' => $userName
                ]);

                // Try to send email but don't fail
                try {
                    Mail::to($request->email)->send(new BDOtpMail($otp, $userName));
                    \Log::info('Resend OTP Email sent successfully to: ' . $request->email);
                } catch (\Exception $mailError) {
                    \Log::error('Resend mail failed in development', [
                        'error' => $mailError->getMessage()
                    ]);
                }

                return response()->json([
                    'success' => true,
                    'message' => 'Development Mode: OTP resent. Check logs.',
                    'otp' => $otp
                ]);
            }

            // Production mode
            Mail::to($request->email)->send(new BDOtpMail($otp, $userName));

            return response()->json([
                'success' => true,
                'message' => 'OTP has been resent to your email successfully.'
            ]);
        } catch (\Exception $e) {
            // Log the error
            \Log::error('Failed to resend OTP email', [
                'email' => $request->email,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to send email. Please try again.',
                'otp' => $otp // Show OTP in case of error
            ]);
        }
    }

    /**
     * Handle logout
     */
    public function logout()
    {
        Session::forget('bd_user');
        Session::forget('bd_otp_email');
        return redirect('/login')->with('success', 'You have been logged out successfully.');
    }
}
