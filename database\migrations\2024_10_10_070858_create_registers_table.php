<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('registers', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            $table->integer('event_id')->default(1);
            $table->integer('user_id')->default(1);
            $table->string('first_name')->nullable();
            $table->string('last_name')->nullable();
            $table->string('email')->nullable();
            $table->string('company')->nullable();
            $table->string('country')->nullable();
            $table->string('specialty')->nullable();
            $table->string('title')->nullable();
            $table->string('phone')->nullable();
            $table->text('qr_code')->nullable();
            $table->boolean('check_in')->default(0);
            $table->timestamp('check_in_time')->nullable();
            $table->string('badge', 200)->nullable();
            $table->boolean('status')->default(1);

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('registers');
    }
};
