<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class EventDate extends Model
{
    use HasFactory;

    protected $fillable = [
        'event_id',
        'event_date',
        'event_end_date',
        'event_start_time',
        'event_end_time',
        'event_date_timezone',
        'event_location',
        'event_location_url',
        'event_country'
    ];

    /**
     * Get the event that owns the event date.
     */
    public function event(): BelongsTo
    {
        return $this->belongsTo(Event::class);
    }
}
