<?php

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Session;
use App\Http\Controllers\IndexController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\EventController;
use App\Http\Controllers\BadgeController;
use App\Http\Controllers\RegisterController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// BD Frontend Routes (no prefix)
Route::get('/', [App\Http\Controllers\BD\DashboardController::class, 'index'])->middleware('bd.auth')->name('bd.dashboard');

Route::get('/aa', function(){
        \Session::forget('bd_user');
        \Session::forget('bd_otp_email');
});

// Test mail route
Route::get('/test-mail', function(){
    try {
        $otp = '123456';
        $userName = 'Test User';
        $email = '<EMAIL>'; // Gerçek test email adresi

        \Log::info('Testing mail configuration', [
            'to' => $email,
            'otp' => $otp,
            'mail_config' => [
                'mailer' => config('mail.default'),
                'host' => config('mail.mailers.smtp.host'),
                'port' => config('mail.mailers.smtp.port'),
                'username' => config('mail.mailers.smtp.username'),
                'from_address' => config('mail.from.address'),
                'from_name' => config('mail.from.name')
            ]
        ]);

        \Mail::to($email)->send(new \App\Mail\BDOtpMail($otp, $userName));

        return 'Mail sent successfully to ' . $email . '! Check your inbox.';
    } catch (\Exception $e) {
        \Log::error('Test mail failed', [
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
        return 'Mail failed: ' . $e->getMessage() . '<br><br>Check logs for more details.';
    }
});

Route::get('/login', [App\Http\Controllers\BD\AuthController::class, 'showLogin'])->name('bd.login');
Route::post('/send-otp', [App\Http\Controllers\BD\AuthController::class, 'sendOtp'])->name('bd.send-otp');
Route::get('/otp', [App\Http\Controllers\BD\AuthController::class, 'showOtp'])->name('bd.otp');
Route::post('/verify-otp', [App\Http\Controllers\BD\AuthController::class, 'verifyOtp'])->name('bd.verify-otp');
Route::post('/resend-otp', [App\Http\Controllers\BD\AuthController::class, 'resendOtp'])->name('bd.resend-otp');

// BD Protected routes (no prefix)
Route::middleware(['bd.auth'])->group(function () {
    Route::get('/dashboard', [App\Http\Controllers\BD\DashboardController::class, 'index'])->name('bd.dashboard.alt');
    Route::get('/event-detail', [App\Http\Controllers\BD\EventController::class, 'detail'])->name('bd.event-detail');
    Route::get('/event/{id}', [App\Http\Controllers\BD\EventController::class, 'show'])->name('bd.event.show');
    Route::get('/speaker-detail/{id?}', [App\Http\Controllers\BD\SpeakerController::class, 'detail'])->name('bd.speaker-detail');
    Route::get('/travel-documents', [App\Http\Controllers\BD\TravelDocumentsController::class, 'index'])->name('bd.travel-documents');
    Route::get('/travel-documents/event-details', [App\Http\Controllers\BD\TravelDocumentsController::class, 'getEventDetails'])->name('bd.travel-documents.event-details');
    Route::post('/travel-documents/upload', [App\Http\Controllers\BD\TravelDocumentsController::class, 'uploadDocuments'])->name('bd.travel-documents.upload');
    Route::get('/travel-documents/download', [App\Http\Controllers\BD\TravelDocumentsController::class, 'downloadDocument'])->name('bd.travel-documents.download');
    Route::delete('/travel-documents/delete', [App\Http\Controllers\BD\TravelDocumentsController::class, 'deleteDocument'])->name('bd.travel-documents.delete');
    Route::post('/event/contact', [App\Http\Controllers\BD\EventController::class, 'sendContactMessage'])->name('bd.event.contact');
    Route::get('/my-qr', [App\Http\Controllers\BD\QrController::class, 'index'])->name('bd.my-qr');

    // Test route for travel documents
    Route::get('/travel-documents/test', function() {
        return response()->json([
            'success' => true,
            'message' => 'Travel documents API is working',
            'user' => Session::get('bd_user') ? 'Authenticated' : 'Not authenticated'
        ]);
    });

    Route::post('/logout', [App\Http\Controllers\BD\AuthController::class, 'logout'])->name('bd.logout');
});

// Panel Auth routes with prefix
Route::prefix('admin')->group(function () {
    Auth::routes();
});

Route::get('/qr-preview/{user_id}', [IndexController::class, 'qrCodePreview'])->name('qr_preview');
Route::get('/qr', [IndexController::class, 'qrCodePreview2']);
Route::get('/test', [IndexController::class, 'testFunc']);

Route::group(['prefix' => 'admin', 'as' => 'panel.'], function () {
    Route::get('/', [HomeController::class, 'index'])->name('home');
    Route::post('/', [HomeController::class, 'filter'])->name('home.filter');
    Route::get('/phpinfo', [HomeController::class, 'phpinfo'])->name('phpinfo');
    Route::get('/new-event', [EventController::class, 'new_event'])->name('new_event');
    Route::post('/new-event', [EventController::class, 'store'])->name('new_event_post');
    Route::post('/update-event', [EventController::class, 'update']);
    Route::get('/event-detail/{event_id}', [EventController::class, 'event_detail'])->name('event_detail');
    Route::get('/update-uuids-all-registers', [RegisterController::class, 'updateAllUuids'])->name('update_uuids_all_registers');

    // AttendeesInfo routes
    Route::post('/attendees-info/upload-files', [App\Http\Controllers\AttendeesInfoController::class, 'uploadFiles'])->name('attendees_info.upload_files');
    Route::get('/attendees-info/get-files/{register_id}', [App\Http\Controllers\AttendeesInfoController::class, 'getFiles'])->name('attendees_info.get_files');
    Route::post('/attendees-info/delete-file', [App\Http\Controllers\AttendeesInfoController::class, 'deleteFile'])->name('attendees_info.delete_file');

    Route::group(['prefix' => 'event/{event_id}', 'middleware' => 'event_id'], function () {
        Route::get('/attendees-info', [EventController::class, 'attendees_info'])->name('attendees_info');
        Route::get('/checkin/', [EventController::class, 'event_manuelcheckin__or__qrcheckin'])->name('event_manuelcheckin__or__qrcheckin');
        Route::post('/checkin/search', [EventController::class, 'event_manuelcheckin__or__qrcheckin_search'])->name('event_manuelcheckin__or__qrcheckin_search');
        Route::post('/session_checkin', [EventController::class, 'sessionCheckin'])->name('session_checkin');
        Route::post('/session_checkin_qr', [EventController::class, 'sessionCheckinQr'])->name('session_checkin_qr');
        Route::get('/badge-list', [BadgeController::class, 'badge_list'])->name('badge_list');
        Route::get('/badge-print/{id}', [BadgeController::class, 'badgePrintPage'])->name('badge_print_page');
        Route::get('/badge-preview/{badge_id}', [BadgeController::class, 'badgePreviewAjax'])->name('badge_preview');
        Route::get('/badge-download/{id}', [BadgeController::class, 'badgeDownload'])->name('badge_download');
        Route::get('/badge-creation/{register?}', [BadgeController::class, 'badge_creation'])->name('badge_creation');
        Route::get('/badge-creation-qr/{register}', [BadgeController::class, 'badge_creation_qr']);
        Route::get('/badge-creation-manual', [BadgeController::class, 'badge_creation_manual'])->name('badge_creation_manual');
        Route::post('/badge-create-manual', [BadgeController::class, 'createManualBadge'])->name('badge_create_manual');
        Route::post('/badge-generate', [BadgeController::class, 'pngBadgeGenerate'])->name('png_badge_generate');
        Route::post('/badge-generate-bulk', [BadgeController::class, 'generateBulkBadges'])->name('badge_generate_bulk');
        Route::get('/badge-template-check', [BadgeController::class, 'checkBadgeTemplate'])->name('badge_template_check');
        Route::get('/badge-edit/{id}', [BadgeController::class, 'badgeEdit'])->name('badge_edit');
        Route::post('/badge-update/{id}', [BadgeController::class, 'badgeUpdate'])->name('badge_update');
        Route::get('/badge-delete/{id}', [BadgeController::class, 'badgeDelete'])->name('badge_delete');

        Route::post('/badge-template-save', [BadgeController::class, 'badgeTemplateSave'])->name('badge_template_save');
        Route::post('/badge-create-template', [BadgeController::class, 'badgeCreateTemplate'])->name('badge_create_template');

        Route::get('/register-list', [RegisterController::class, 'registerList'])->name('register_list');
        Route::get('/sync-with-marketo', [RegisterController::class, 'sycnWithMarketo']);
        Route::post('/complete-shortlist', [RegisterController::class, 'completeShortlist']);
        Route::get('/qr_generate/{id}', [RegisterController::class, 'generateQrCode'])->name('qr_generate');
        Route::get('/getRegisterDetails/{uuid}', [RegisterController::class, 'getRegisterDetails']);
        Route::get('/register/set_check_in/{uuid}', [RegisterController::class, 'setCheckinManual'])->name('set_check_in_manual');
        Route::get('/register/set_check_in_qr/{uuid}', [RegisterController::class, 'setCheckinWithQR']);
    });


    // Overview EventSession, EventDocument Delete
    Route::delete('/delete-session/{id}', [EventController::class, 'deleteSession'])->name('delete-session');
    Route::delete('/delete-document/{id}', [EventController::class, 'deleteDocument'])->name('delete-document');

});
