<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\AttendeesInfo;
use App\Models\Register;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class AttendeesInfoController extends Controller
{
    public function uploadFiles(Request $request)
    {
        try {
            // Validate request
            $request->validate([
                'register_id' => 'required|exists:registers,id',
                'flight_file' => 'nullable|file|mimes:pdf,doc,docx,jpg,jpeg,png|max:10240', // 10MB
                'hotel_file' => 'nullable|file|mimes:pdf,doc,docx,jpg,jpeg,png|max:10240',
                'passport_file' => 'nullable|file|mimes:pdf,doc,docx,jpg,jpeg,png|max:10240',
            ]);

            $registerId = $request->register_id;

            // Get register to get event_id
            $register = Register::findOrFail($registerId);
            $eventId = $register->event_id;

            // Find or create attendees info record
            $attendeesInfo = AttendeesInfo::firstOrCreate(
                [
                    'event_id' => $eventId,
                    'register_id' => $registerId
                ],
                [
                    'flight_information' => null,
                    'hotel_information' => null,
                    'passport_details' => null,
                ]
            );

            // Handle file uploads
            $uploadedFiles = [];

            // Flight Information
            if ($request->hasFile('flight_file')) {
                $flightFile = $request->file('flight_file');
                $flightFileName = 'flight_' . $registerId . '_' . time() . '.' . $flightFile->getClientOriginalExtension();
                $flightPath = $flightFile->storeAs('public/attendees_files', $flightFileName);

                // Delete old file if exists
                if ($attendeesInfo->flight_information) {
                    Storage::delete('public/attendees_files/' . basename($attendeesInfo->flight_information));
                }

                $attendeesInfo->flight_information = $flightFileName;
                $uploadedFiles['flight'] = $flightFileName;
            }

            // Hotel Information
            if ($request->hasFile('hotel_file')) {
                $hotelFile = $request->file('hotel_file');
                $hotelFileName = 'hotel_' . $registerId . '_' . time() . '.' . $hotelFile->getClientOriginalExtension();
                $hotelPath = $hotelFile->storeAs('public/attendees_files', $hotelFileName);

                // Delete old file if exists
                if ($attendeesInfo->hotel_information) {
                    Storage::delete('public/attendees_files/' . basename($attendeesInfo->hotel_information));
                }

                $attendeesInfo->hotel_information = $hotelFileName;
                $uploadedFiles['hotel'] = $hotelFileName;
            }

            // Passport Details
            if ($request->hasFile('passport_file')) {
                $passportFile = $request->file('passport_file');
                $passportFileName = 'passport_' . $registerId . '_' . time() . '.' . $passportFile->getClientOriginalExtension();
                $passportPath = $passportFile->storeAs('public/attendees_files', $passportFileName);

                // Delete old file if exists
                if ($attendeesInfo->passport_details) {
                    Storage::delete('public/attendees_files/' . basename($attendeesInfo->passport_details));
                }

                $attendeesInfo->passport_details = $passportFileName;
                $uploadedFiles['passport'] = $passportFileName;
            }

            // Save the record
            $attendeesInfo->save();

            return response()->json([
                'success' => true,
                'message' => 'Files uploaded successfully!',
                'uploaded_files' => $uploadedFiles,
                'attendees_info' => $attendeesInfo
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Upload failed: ' . $e->getMessage()
            ], 500);
        }
    }

    public function getFiles($register_id)
    {
        try {
            $attendeesInfo = AttendeesInfo::where('register_id', $register_id)->first();

            return response()->json([
                'success' => true,
                'attendees_info' => $attendeesInfo
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get files: ' . $e->getMessage()
            ], 500);
        }
    }

    public function deleteFile(Request $request)
    {
        try {
            // Validate request
            $request->validate([
                'register_id' => 'required|exists:registers,id',
                'file_type' => 'required|in:flight,hotel,passport'
            ]);

            $registerId = $request->register_id;
            $fileType = $request->file_type;

            // Get attendees info record
            $attendeesInfo = AttendeesInfo::where('register_id', $registerId)->first();

            if (!$attendeesInfo) {
                return response()->json([
                    'success' => false,
                    'message' => 'Attendees info not found'
                ], 404);
            }

            // Determine which field to update based on file type
            $fieldName = '';
            switch ($fileType) {
                case 'flight':
                    $fieldName = 'flight_information';
                    break;
                case 'hotel':
                    $fieldName = 'hotel_information';
                    break;
                case 'passport':
                    $fieldName = 'passport_details';
                    break;
            }

            // Get the current file name
            $currentFileName = $attendeesInfo->$fieldName;

            if (!$currentFileName) {
                return response()->json([
                    'success' => false,
                    'message' => 'No file found to delete'
                ], 404);
            }

            // Delete the physical file from storage
            $filePath = 'public/attendees_files/' . $currentFileName;
            if (Storage::exists($filePath)) {
                Storage::delete($filePath);
            }

            // Update database record - set field to null
            $attendeesInfo->$fieldName = null;
            $attendeesInfo->save();

            return response()->json([
                'success' => true,
                'message' => 'File deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Delete failed: ' . $e->getMessage()
            ], 500);
        }
    }
}
