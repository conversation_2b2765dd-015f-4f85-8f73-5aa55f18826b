<aside class="left-sidebar">
    <!-- Sidebar scroll-->
    <div>
        <div class="brand-logo d-flex align-items-center justify-content-between p-4">
            <a href="{{ route('panel.home') }}" class="text-nowrap logo-img">
                <img src="{{ asset('images/bd-compass-logo.png') }}" class="dark-logo" width="70%" alt="logo" />
                <img src="{{ asset('images/bd-compass-logo.png') }}" class="light-logo" width="70%" alt="logo" />
            </a>
            <div class="close-btn d-lg-none d-block sidebartoggler cursor-pointer" id="sidebarCollapse">
                <i class="ti ti-x fs-8"></i>
            </div>
        </div>
        <!-- Sidebar navigation-->
        <nav class="sidebar-nav scroll-sidebar" data-simplebar>
            <ul id="sidebarnav">
                <li class="sidebar-item">
                    <a class="sidebar-link sidebar-eventlist" href="{{ route('panel.home') }}" aria-expanded="false">
                        <span class="d-flex">
                            <i class="ti ti-calendar-event"></i>
                        </span>
                        <span class="hide-menu">Event List</span>
                    </a>
                    <ul aria-expanded="false" class="collapse first-level in">
                        {{-- @if ($events)
                            @foreach ($events as $event)
                                <li class="sidebar-item">
                                    <a href="{{ route('panel.event_detail', ['event_id' => $event->id]) }}"
                                        class="sidebar-link active">
                                        <div class="round-16 d-flex align-items-center justify-content-center">
                                        </div>
                                        <span class="hide-menu"
                                            style="white-space: pre-line;">{{ $event->event_title }}</span>
                                    </a>
                                </li>
                            @endforeach
                        @endif --}}
                        <li class="sidebar-item">
                            <a href="{{ route('panel.new_event') }}" class="sidebar-link active" style="gap: 15px">
                                <span>
                                    <svg width="22" height="22" viewBox="0 0 22 22" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M11 21C16.5228 21 21 16.5228 21 11C21 5.47715 16.5228 1 11 1C5.47715 1 1 5.47715 1 11C1 16.5228 5.47715 21 11 21Z"
                                            stroke="black" stroke-width="2" stroke-linecap="round"
                                            stroke-linejoin="round" />
                                        <path d="M11 7V15" stroke="black" stroke-width="2" stroke-linecap="round"
                                            stroke-linejoin="round" />
                                        <path d="M7 11H15" stroke="black" stroke-width="2" stroke-linecap="round"
                                            stroke-linejoin="round" />
                                    </svg>
                                </span>
                                <span class="hide-menu"> Add Event
                                </span>
                            </a>
                        </li>
                    </ul>
                </li>
            </ul>
        </nav>
        <div class="fixed-profile p-3 bg-light-secondary rounded sidebar-ad mt-3">
            <div class="hstack gap-3">
                <div class="john-img">
                    <img src="{{ asset('images/user-1.jpg') }}" class="rounded-circle" width="40" height="40"
                        alt="">
                </div>
                <div class="john-title">
                    <h6 class="mb-0 fs-4 fw-semibold">Mathew</h6>
                    <span class="fs-2 text-dark">Designer</span>
                </div>
                <button class="border-0 bg-transparent text-primary ms-auto" tabindex="0" type="button"
                    aria-label="logout" data-bs-toggle="tooltip" data-bs-placement="top" data-bs-title="s">
                    <i class="ti ti-power fs-6"></i>
                </button>
            </div>
        </div>
        <!-- End Sidebar navigation -->
    </div>
    <!-- End Sidebar scroll-->
</aside>
