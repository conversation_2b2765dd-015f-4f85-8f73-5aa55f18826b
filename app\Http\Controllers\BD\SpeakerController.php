<?php

namespace App\Http\Controllers\BD;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use App\Models\EventModeratorSpearker;

class SpeakerController extends Controller
{
    /**
     * Show speaker detail
     */
    public function detail($id = null)
    {
        $user = Session::get('bd_user');

        if (!$user) {
            return redirect()->route('bd.login');
        }

        // Get speaker from event_moderator_speakers table
        $speakerData = EventModeratorSpearker::find($id);

        if (!$speakerData) {
            return redirect()->route('bd.dashboard')->with('error', 'Speaker not found');
        }

        // Format speaker data
        $speaker = $this->formatSpeakerData($speakerData);

        return view('bd.speaker-detail', compact('user', 'speaker'));
    }

    /**
     * Format speaker data for detail view
     */
    private function formatSpeakerData($speakerData)
    {
        // Determine image path based on type and folder structure
        $imageUrl = asset('assets/bd/images/user.png'); // Default image

        if ($speakerData->banner) {
            if ($speakerData->type == 1) {
                // Moderator images are in evet_moderator_moderator folder
                $imageUrl = asset(env('STORAGE_PATH') . '/evet_moderator_speaker/' . $speakerData->banner);
            } else {
                // Speaker images are in evet_moderator_speaker folder
                $imageUrl = asset(env('STORAGE_PATH') . '/evet_moderator_moderator/' . $speakerData->banner);
            }
        }

        return (object) [
            'id' => $speakerData->id,
            'name' => $speakerData->name ?? 'Speaker Name',
            'title' => $speakerData->title ?? 'Professional Title',
            'image' => $imageUrl,
            'type' => $speakerData->type, // 1 = moderator, 2 = speaker
            'type_label' => $speakerData->type == 1 ? 'Moderator' : 'Speaker',
            'description' => $speakerData->description ?? '',
            'bio' => $this->generateBio($speakerData),
            'event_id' => $speakerData->event_id
        ];
    }

    /**
     * Generate bio based on speaker data
     */
    private function generateBio($speakerData)
    {
        // If description exists, use it as bio
        if ($speakerData->description) {
            return $speakerData->description;
        }

        // Generate a professional bio based on available data
        $name = $speakerData->name ?? 'This speaker';
        $title = $speakerData->title ?? 'professional';
        $type = $speakerData->type == 1 ? 'moderator' : 'speaker';

        return "{$name} is an experienced {$title} who will be serving as a {$type} for this event. " .
               "With extensive expertise in their field, {$name} brings valuable insights and knowledge to our healthcare symposium. " .
               "Their professional background and experience make them an ideal contributor to the discussions and presentations planned for this event.";
    }
}
