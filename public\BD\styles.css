body {
    background: linear-gradient(180deg, #e3f2fd 0%, #bbdefb 100%);
    min-height: 100vh;
}

.login-container {
    max-width: 400px;
}

.logo-text {
    font-weight: bold;
    margin-bottom: 0;
    color: #0A599A;
    font-size: 3rem;
}

.email-label {
    color: #0A599A;
    font-size: 1.1rem;
    font-weight: 600;
}

.email-input {
    background-color: #0A599A26;
    border: 2px solid #0A599A;
    border-radius: 8px;
    padding: 15px;
    font-family: Inter;
    font-weight: 700;
    font-style: Bold;
    font-size: 16px;
    leading-trim: NONE;
    line-height: 100%;
    letter-spacing: 0%;

}

.otp-button {
    background-color: #4caf50;
    border: none;
    border-radius: 8px;
    color: white;
    min-width: 120px;
}

.loading-content {
    text-align: center;
}

.loading-text {
    color: #0A599A;
    font-size: 1rem;
    line-height: 1.5;
    margin-bottom: 2rem;
    max-width: 300px;
    margin-left: auto;
    margin-right: auto;
}

.loading-spinner {
    color: #0A599A;
    width: 3rem;
    height: 3rem;
}

.resend-otp {
    font-size: 0.875rem;
    color: #666;
    margin-bottom: 0;
    text-align: left;
}

.resend-otp span {
    text-decoration: underline;
    cursor: pointer;
}

.dashboard-body {
    background-color: #f8f9fa;
    min-height: 100vh;
}

.dashboard-header {
    border-bottom: 1px solid #e9ecef;
}

.logo-text-small {
    color: #0A599A;
    font-weight: bold;
}

.welcome-text {
    color: #0A599A;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.profile-image {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
}

.user-name {
    color: #0A599A;
    font-weight: 600;
}

.user-title {
    color: #0A599A!important;
}

.event-tabs {
    display: flex;
}

.tab-content {
    opacity: 0;
    transform: translateY(10px);
    transition: all 0.3s ease;
    display: none;
}

.tab-content.active {
    opacity: 1;
    transform: translateY(0);
    display: block;
}

.tab-btn {
    background-color: transparent;
    border: none;
    border-radius: 8px;
    padding: 8px 20px;
    font-size: 0.9rem;
    color: #0A599A;
    border: 1px solid #0A599A;
    transition: all 0.2s ease;
    transform: scale(1);
}

.tab-btn:hover {
    transform: scale(1.05);
}

.tab-btn:active {
    transform: scale(0.95);
}

.tab-btn.active {
    background-color: #0A599A;
    color: white;
    transform: scale(1.02);
}

.events-container {
    max-height: 70vh;
    overflow-y: auto;
}

.event-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    position: relative;
}

.event-image-container {
    /* border-radius: 12px;
    overflow: hidden;
    background: linear-gradient(45deg, #e3f2fd, #bbdefb, #90caf9);
    height: 150px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid #1976d2; */
    position: relative;
}

.event-image{
    width:100%;
}

.travel-event-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.event-info {
    text-align: left;
}

.event-time {
    color: #1976d2;
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 5px;
}

.event-location {
    color: #1976d2;
    font-style: italic;
    font-size: 0.9rem;
}

.event-divider {
    border-color: #1976d2;
    margin: 20px 0;
}

.travel-section-title {
    color: #1976d2;
    font-weight: 600;
}

.travel-item {
    border-radius: 12px;
    padding: 15px 0px;
}

.travel-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.flight-icon {
    background-color: #f44336;
}

.hotel-icon {
    background-color: #2196f3;
}

.passport-icon {
    background-color: #4caf50;
}

.travel-item-title {
    color: #1976d2;
    font-weight: 600;
    font-size: 1rem;
}

.travel-item-subtitle {
    color: #1976d2;
    font-size: 0.85rem;
}

.travel-add-btn {
    background-color: #1976d2;
    color: white;
    border: none;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.travel-view-btn {
    background-color: #1976d2;
    color: white;
    border: none;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 5px;
}

.travel-edit-btn {
    background-color: #1976d2;
    color: white;
    border: none;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.event-content {
    padding: 15px;
}

.event-title {
    color: #0A599A;
    font-weight: 600;
    margin-bottom: 8px;
}

.event-description{
    color: #0A599A;
}

.event-details {
    margin-bottom: 10px;
    color: #0A599A!important;
}

.event-details small{
    color: #0A599A!important;
}

.contact-btn{
    background-color: #0A599A;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 10px 20px;
    font-size: 0.9rem;
    font-weight: 600;
    transition: all 0.2s ease;
    width: 50%!important;
}

.registered-badge {
    background-color: #4caf50;
    color: white;
    padding: 4px 12px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
    position: absolute;
    top: 15px;
    right: 15px;
}

.section-title {
    color: #0A599A;
    font-weight: 600;
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.expect-section {
    background-color: white;
    margin-bottom: 1rem;
    padding: 15px;
}

.expect-text {
    color: #0A599A;
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 20px;
    font-weight: 400;
}

.expect-text:last-child {
    margin-bottom: 0;
}

.agenda-section {
    padding: 25px 20px;
    border-radius: 12px;
}

.agenda-item {
    padding: 20px 0;
    border-bottom: 1px solid #0A599A;
}

.agenda-item:last-child {
    border-bottom: none;
}

.agenda-time {
    color: #0A599A;
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 8px;
}

.agenda-content {
    width: 100%;
}

.agenda-topic {
    color: #0A599A;
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 8px;
    line-height: 1.4;
}

.agenda-speaker {
    color: #0A599A;
    font-style: italic;
    font-size: 0.9rem;
    margin-bottom: 0;
}

.speakers-section {
    padding: 25px 20px;
    margin-bottom: 1rem;
    border-top: 1px solid #0A599A
}

.speaker-card {
    text-align: left;
    margin-bottom: 15px;
}

.speaker-image-placeholder {
    width: 100%;
    height: 120px;
    background-color: white;
    border: 2px solid #0A599A;
    border-radius: 12px;
    margin-bottom: 10px;
}

.speaker-name {
    color: #333;
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 8px;
}

.speaker-read-btn {
    background-color: #4caf50;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 6px 16px;
    font-size: 0.85rem;
    font-weight: 500;
}

.speaker-read-btn:hover {
    background-color: #45a049;
}

.speaker-detail-body {
    background: linear-gradient(180deg, #e3f2fd 0%, #bbdefb 100%);
    min-height: 100vh;
}

.back-btn {
    color: #1976d2 !important;
    font-weight: 600;
    text-decoration: none;
    font-size: 1rem;
}

.back-btn:hover {
    color: #0d47a1 !important;
}

.speaker-profile-image {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
}

.speaker-profile-name {
    color: #1976d2;
    font-weight: 600;
}

.speaker-profile-title {
    color: #1976d2;
}

.speaker-bio {
    background-color: transparent;
}

.bio-text {
    color: #1976d2;
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 20px;
    text-align: justify;
}

.bio-text:last-child {
    margin-bottom: 0;
}

.travel-documents-body {
    background: linear-gradient(180deg, #e3f2fd 0%, #bbdefb 100%);
    min-height: 100vh;
}

.travel-logo {
    width: 50px;
    height: 50px;
}

.travel-logo-text {
    color: #1976d2;
    font-weight: bold;
    font-size: 2.5rem;
}

.event-selection-container {
    max-width: 400px;
    margin: 0 auto;
}

.select-event-title {
    color: #1976d2;
    font-weight: 600;
    font-size: 1.1rem;
}

.event-dropdown-btn {
    background-color: white;
    border: 2px solid #1976d2;
    color: #666;
    padding: 15px 20px;
    border-radius: 8px;
    font-size: 1rem;
}

.event-dropdown-btn:hover {
    background-color: white;
    border-color: #1976d2;
    color: #666;
}

.event-dropdown-btn:focus {
    background-color: white;
    border-color: #1976d2;
    box-shadow: none;
    color: #666;
}

.event-dropdown-menu {
    background-color: white;
    border: 2px solid #1976d2;
    border-radius: 8px;
    padding: 0;
    margin-top: 5px;
    box-shadow: none;
    position: static !important;
    transform: none !important;
    display: none;
    max-height: 300px;
    overflow-y: auto;
}

.event-dropdown-menu.show {
    display: block;
}

.event-option {
    color: #1976d2;
    padding: 15px 20px;
    border-bottom: 1px solid #e9ecef;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.2s;
}

.event-option:last-child {
    border-bottom: none;
}

.event-option:hover {
    background-color: #f8f9fa;
}

.event-option.active {
    background-color: #e3f2fd;
    font-weight: 600;
}

.dropdown-text {
    flex-grow: 1;
    text-align: left;
}

.bi-chevron-up, .bi-chevron-down {
    color: #1976d2;
    font-size: 1.2rem;
}

/* Side Menu Styles */
.side-menu {
    position: fixed;
    top: 0;
    right: -100%;
    width: 100%;
    height: auto;
    border-bottom-left-radius: 15px;
    border-bottom-right-radius: 15px;
    background-color: white;
    z-index: 1050;
    transition: right 0.3s ease;
    overflow-y: auto;
}

.side-menu.show {
    right: 0;
}

.side-menu-header {
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.side-menu-logo {
    display: flex;
    align-items: center;
}

.side-menu-logo img {
    margin-right: 10px;
}

.side-menu-logo-text {
    color: #1976d2;
    font-weight: bold;
    font-size: 2rem;
    margin: 0;
}

.side-menu-close {
    background: none;
    border: none;
    font-size: 2rem;
    color: #333;
    cursor: pointer;
    padding: 0;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.side-menu-profile {
    padding: 30px 20px;
    border-bottom: 2px solid #1976d2;
    display: flex;
    align-items: center;
}

.side-menu-profile-image {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    margin-right: 15px;
    object-fit: cover;
}

.side-menu-profile-info h5 {
    color: #1976d2;
    font-weight: 600;
    margin-bottom: 5px;
    font-size: 1.3rem;
}

.side-menu-profile-info small {
    color: #1976d2;
    font-size: 1rem;
}

.side-menu-nav {
    padding: 30px 0;
}

.side-menu-item {
    display: block;
    padding: 20px 30px;
    color: #1976d2;
    text-decoration: none;
    font-size: 1.5rem;
    font-weight: 600;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s;
}

.side-menu-item:hover {
    background-color: #f8f9fa;
    color: #1976d2;
}

.side-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1040;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.side-menu-overlay.show {
    opacity: 1;
    visibility: visible;
}

.nav-link {
    color: #1976d2;
    text-decoration: none;
    font-weight: 500;
    font-size: 1rem;
    padding: 8px 0;
    position: relative;
}

.nav-link:hover {
    color: #0d47a1;
}

.nav-link.active {
    color: #1976d2;
    font-weight: 600;
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background-color: #1976d2;
}

.welcome-text-desktop {
    color: #1976d2;
    font-size: 1rem;
    font-weight: 500;
}

.user-name-desktop {
    color: #1976d2;
    font-weight: 600;
    font-size: 1rem;
}

.user-title-desktop {
    color: #1976d2;
    font-size: 0.875rem;
}

.profile-image-desktop {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
}

@media screen and (min-width: 992px) {
    .dashboard-header {
        padding: 15px 0;
        border-bottom: 1px solid #e9ecef;
    }
    
    .dashboard-header .container-fluid {
        max-width: 1200px;
        margin: 0 auto;
    }
}

@media screen and (min-width:1024px) {
    .events-container{
        overflow-y: unset;
    }
    .tab-content {
        display: none;
        justify-content: space-between;
        gap: 15px;
    }
    .tab-content.active {
        display: flex;
    }
}
