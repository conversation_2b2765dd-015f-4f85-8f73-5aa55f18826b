<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('events', function (Blueprint $table) {
            $table->id();
            $table->string('marketo_event_id');
            $table->string('duration_pf_event');
            $table->string('event_description');
            $table->date('event_date');
            $table->string('event_start_time', 50);
            $table->string('event_end_time', 50);
            $table->string('event_banner');
            $table->string('event_title');
            $table->string('event_location');
            $table->string('business_unit');
            $table->string('smart_list_id')->nullable();
            $table->string('attendee_quota')->nullable();
            $table->tinyInteger('moderators_count');
            $table->tinyInteger('speakers_count');
            $table->tinyInteger('status')->default(1);
            $table->tinyInteger('user_check_in')->default(1);
            $table->tinyInteger('badge_creation')->default(1);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('events');
    }
};
