@extends('layouts.app')
@section('content')

    <link rel="stylesheet" href="https://cdn.datatables.net/2.1.8/css/dataTables.dataTables.min.css" />

    <style>
        .dt-input {
            margin-right: 10px;
        }
    </style>


    <div id="loading-overlay" style="display: none">
        <div class="spinner-border" role="status">
            <span class="sr-only">Loading...</span>
        </div>
    </div>


    <div class="checkout">
        <div class="card shadow-none border">
            <div class="card-body p-4">
                @include('layouts.event_details')
                @include('layouts.page_navigation')

                <!-- Dropdown and Search -->
                <div class="d-flex flex-row justify-content-between ">
                    <div class="d-flex flex-row gap-3">
                        <p class="checkin active" id="full_list_button">All Registrants</p>
                        <p class="checkin" id="short_list_button">Short List</p>
                    </div>
                </div>

                <div id="full_list">
                    @if($manualBadgeCount > 0)
                        <div class="alert alert-info mb-3">
                            <i class="ti ti-info-circle"></i>
                            <strong>Manual Badges:</strong> {{ $manualBadgeCount }} external participant(s) added manually.
                            These are highlighted in yellow and cannot be selected for shortlist.
                        </div>
                    @endif

                    <table class="table table-bordered table-hover" id="register_list">
                        <thead class="">
                            <tr style="border-top: none">
                                <th>
                                    <input type="checkbox" id="select_all"> Select All
                                </th>
                                <th>Email</th>
                                <th>Title</th>
                                <th>First name</th>
                                <th>Last name</th>
                                <th>Company</th>
                                <th>Phone</th>
                            </tr>
                        </thead>
                        <tbody>

                            @if ($users)
                                @foreach ($users as $item)
                                    <tr class="{{ $item->status == 3 ? 'table-warning' : '' }}">
                                        <td>
                                            @if($item->status == 3)
                                                <span class="badge bg-warning text-dark" title="Manual/External - Cannot be selected">
                                                    <i class="ti ti-user-plus"></i>
                                                </span>
                                            @else
                                                <input type="checkbox"
                                                       class="row_checkbox {{ $item->status == 1 ? 'previously-selected' : '' }}"
                                                       {{ $item->status == 1 ? 'checked' : '' }}
                                                       {{ $item->status == 1 ? 'disabled' : '' }}
                                                       value="{{ $item->id }}"
                                                       data-previously-selected="{{ $item->status == 1 ? 'true' : 'false' }}">
                                            @endif
                                        </td>
                                        <td>
                                            {{ $item->email }}
                                            @if($item->status == 3)
                                                <br><small class="text-warning">Manual Entry</small>
                                            @endif
                                        </td>
                                        <td>{{ $item->title }}</td>
                                        <td>{{ $item->first_name }}</td>
                                        <td>{{ $item->last_name }}</td>
                                        <td>{{ $item->company }}</td>
                                        <td>{{ $item->phone }}</td>
                                    </tr>
                                @endforeach
                            @endif

                        </tbody>
                    </table>

                    <a class="btn btn-primary sync_marketo_btn"
                        href="{{ url('/admin/event/' . $event_id . '/sync-with-marketo') }}">Sync With Marketo</a>
                    <a class="btn btn-primary" id="complete-shortlist-button"
                        @if ($event->is_completed == 2 || $users->isEmpty()) style="pointer-events: none; opacity: 0.5;" @endif href="#">
                        @if ($event->is_completed == 2)
                            Shortlist Completed
                        @else
                            Complete Shortlist
                        @endif
                    </a>

                    <input type="hidden" name="is_completed" id="is_completed" value="{{ $event->is_completed }}">

                    <span id="selected-count-label" class="badge bg-secondary ms-2 d-none">0 /
                        {{ $event->attendee_quota }}</span>

                </div>

                <div id="short_list" style="display: none">
                    <table class="table table-bordered table-hover" id="register_list2">
                        <thead class="">
                            <tr style="border-top: none">
                                <th>Email</th>
                                <th>Title</th>
                                <th>First name</th>
                                <th>Last name</th>
                                <th>Company</th>
                                <th>Phone</th>
                                <th>Event Check-in</th>
                                <th>Qr</th>
                                <th>#</th>
                            </tr>
                        </thead>
                        <tbody>

                            @if ($users && $event->is_completed == 2)
                                @foreach ($users as $item)
                                    @if ($item->status == 1)
                                        <tr>
                                            <td>{{ $item->email }}</td>
                                            <td>{{ $item->title }}</td>
                                            <td>{{ $item->first_name }}</td>
                                            <td>{{ $item->last_name }}</td>
                                            <td>{{ $item->company }}</td>
                                            <td>{{ $item->phone }}</td>
                                            <td>{{ $item->check_in ? 'Yes' : 'No' }}</td>
                                            <td>
                                                {{-- <img src="data:image/png;base64, {!! $item->qr_code !!}" width="80"> --}}
                                                <img src="{{ url('/qr/' . $item->user_id . '.png') }}?v={{ time() }}" width="80">
                                            </td>
                                            <td>
                                                {{-- <a class="btn btn-primary" href="{{ url('/qr/'.$event->smart_list_id.'/'.$item->user_id.'.png') }}" target="_blank">Open Qr</a> --}}
                                                <a class="btn btn-primary"
                                                    href="{{ route('qr_preview', ['user_id' => $item->user_id]) }}"
                                                    target="_blank">Open QR</a>
                                            </td>
                                        </tr>
                                    @endif
                                @endforeach
                            @endif

                        </tbody>
                    </table>
                </div>


            </div>
        </div>
    </div>

@endsection

@push('css')
    <style>
        .qr-code-link {
            position: relative;
            display: inline-block;
        }

        .qr-code-container {
            position: relative;
            display: inline-block;
        }

        .qr-hover-icon {
            position: absolute;
            font-weight: 800;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 48px;
            color: #000000;
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .qr-code-link:hover .qr-hover-icon {
            opacity: 1;
        }

        .qr-code-link:hover img {
            opacity: 0.4;
        }

        #loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.5);
            z-index: 9998;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .spinner-border {
            width: 3rem;
            height: 3rem;
            border-width: 0.3rem;
        }

        /* Daha önce seçili olan checkbox'lar için stil */
        .previously-selected {
            opacity: 0.7;
        }

        .previously-selected:disabled {
            cursor: not-allowed;
        }

        /* Daha önce seçili olan satırlar için arka plan rengi */
        tr:has(.previously-selected) {
            background-color: #f8f9fa;
        }
    </style>
@endpush

@push('js')
    <!-- ---------------------------------------------- -->
    <!-- current page js files -->
    <!-- ---------------------------------------------- -->
    <script src="{{ asset('js/manualCheckin__QRCheckin.js') }}"></script>

    <script src="https://cdn.jsdelivr.net/npm/jsqr/dist/jsQR.js"></script>

    <script src="//cdn.jsdelivr.net/npm/sweetalert2@11"></script>


    <script src="https://cdn.datatables.net/2.1.8/js/dataTables.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const fullListButton = document.getElementById('full_list_button');
            const fullListDiv = document.getElementById('full_list');
            fullListButton.addEventListener('click', function() {
                fullListDiv.style.display = 'block';
                shortListDiv.style.display = 'none';

                fullListButton.classList.add('active');
                shortListButton.classList.remove('active');
            });

            const shortListButton = document.getElementById('short_list_button');
            const shortListDiv = document.getElementById('short_list');
            shortListButton.addEventListener('click', function() {
                shortListDiv.style.display = 'block';
                fullListDiv.style.display = 'none';

                fullListButton.classList.remove('active');
                shortListButton.classList.add('active');
            });
        });

        const dataTable = new DataTable('#register_list', {
            columnDefs: [{
                orderable: false,
                targets: 0
            }],
            drawCallback: function() {
                // DataTable her çizildiğinde checkbox durumlarını güncelle
                setTimeout(function() {
                    updateCheckboxStates();
                }, 100);
            }
        });
        new DataTable('#register_list2');

        document.addEventListener('DOMContentLoaded', function() {
            const selectAll = document.getElementById('select_all');

            if (selectAll) {
                selectAll.addEventListener('change', function() {
                    // DataTables API kullanarak sadece disabled olmayan checkbox'ları seç/seçme
                    dataTable.rows().every(function() {
                        const checkbox = this.node().querySelector('.row_checkbox');
                        if (checkbox && !checkbox.disabled) {
                            checkbox.checked = selectAll.checked;
                        }
                    });
                    updateSelectedCount(); // Sayacı güncelle
                });
            }
        });
    </script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const selectedCountLabel = document.getElementById('selected-count-label');
            const completeButton = document.getElementById('complete-shortlist-button');

            const attendeeQuota = {{ $event->attendee_quota }};

            // Checkbox'ların disabled durumunu kontrol et
            function updateCheckboxStates() {
                const isCompleted = document.getElementById('is_completed').value;
            }

            function updateSelectedCount() {
                // DataTables API kullanarak tüm sayfalardaki seçili checkbox'ları al
                let selected = 0;
                let previouslySelected = 0;
                let newlySelected = 0;

                dataTable.rows().every(function() {
                    const checkbox = this.node().querySelector('.row_checkbox');
                    if (checkbox && checkbox.checked) {
                        selected++;
                        if (checkbox.dataset.previouslySelected === 'true') {
                            previouslySelected++;
                        } else {
                            newlySelected++;
                        }
                    }
                });

                selectedCountLabel.textContent = `${selected} / ${attendeeQuota} (${previouslySelected} previous + ${newlySelected} new)`;

                const isCompleted = document.getElementById('is_completed').value;

                if (selected) {
                    if (selected <= attendeeQuota && (isCompleted == '1' || isCompleted == '2')) {
                        // Kota dolduysa ve event henüz tamamlanmamışsa VEYA tamamlanmışsa complete/update edilebilir
                        completeButton.classList.remove('disabled');
                        completeButton.style.pointerEvents = 'auto';
                        completeButton.style.opacity = '1';

                        // Button text'ini güncelle
                        if (isCompleted == '2') {
                            completeButton.textContent = 'Update Shortlist';
                        } else {
                            completeButton.textContent = 'Complete Shortlist';
                        }
                    } else {
                        // Kota dolmamışsa disabled
                        completeButton.classList.add('disabled');
                        completeButton.style.pointerEvents = 'none';
                        completeButton.style.opacity = '0.5';
                    }
                }

                if (selected > 0) {
                    selectedCountLabel.classList.remove('d-none');
                } else {
                    selectedCountLabel.classList.add('d-none');
                }
            }

            // Tüm checkbox'lara event listener ekle (mevcut sayfa + gelecek sayfalar için)
            document.addEventListener('change', function(e) {
                if (e.target.classList.contains('row_checkbox')) {
                    updateSelectedCount();
                }
            });

            updateSelectedCount(); // sayfa yüklenince kontrol et
            updateCheckboxStates(); // checkbox durumlarını güncelle

            // Event completed ise uyarı göster
            if (document.getElementById('is_completed').value == '2') {
                console.log('Event shortlist is completed. No changes allowed.');
            }

            // Toplu badge oluşturma butonu
            const bulkBadgeBtn = document.getElementById('bulk-badge-generate-btn');
            if (bulkBadgeBtn) {
                bulkBadgeBtn.addEventListener('click', function() {
                    if (document.getElementById('is_completed').value != '2') {
                        Swal.fire({
                            title: 'Warning',
                            text: 'Please complete the shortlist first before generating badges.',
                            icon: 'warning'
                        });
                        return;
                    }

                    // Template kontrolü yap
                    fetch(`{{ route('panel.badge_template_check', ['event_id' => $event_id]) }}`)
                        .then(response => response.json())
                        .then(data => {
                            if (!data.has_template) {
                                Swal.fire({
                                    title: 'Template Required',
                                    text: data.message || 'Please create a badge template first before generating badges.',
                                    icon: 'warning',
                                    confirmButtonText: 'Go to Badge Creation',
                                    showCancelButton: true,
                                    cancelButtonText: 'Cancel'
                                }).then((result) => {
                                    if (result.isConfirmed) {
                                        window.location.href = "{{ route('panel.badge_creation', ['event_id' => $event_id]) }}";
                                    }
                                });
                                return;
                            }

                            // Template varsa badge oluşturmaya devam et
                            Swal.fire({
                                title: 'Generate All Badges?',
                                text: "This will create badges for all users in the shortlist. Existing badges will be skipped.",
                                icon: 'question',
                                showCancelButton: true,
                                confirmButtonColor: '#3085d6',
                                cancelButtonColor: '#d33',
                                confirmButtonText: 'Yes, generate all!',
                                showLoaderOnConfirm: true,
                                preConfirm: () => {
                                    // Form oluştur ve gönder
                                    const form = document.createElement('form');
                                    form.method = 'POST';
                                    form.action = "{{ route('panel.badge_generate_bulk', ['event_id' => $event_id]) }}";

                                    // CSRF token
                                    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
                                    const csrfInput = document.createElement('input');
                                    csrfInput.type = 'hidden';
                                    csrfInput.name = '_token';
                                    csrfInput.value = csrfToken;
                                    form.appendChild(csrfInput);

                                    // Template ID (varsayılan 1)
                                    const templateInput = document.createElement('input');
                                    templateInput.type = 'hidden';
                                    templateInput.name = 'template_id';
                                    templateInput.value = '1';
                                    form.appendChild(templateInput);

                                    document.body.appendChild(form);
                                    form.submit();
                                },
                                allowOutsideClick: () => !Swal.isLoading()
                            });
                        })
                        .catch(error => {
                            console.error('Template check failed:', error);
                            Swal.fire({
                                title: 'Error',
                                text: 'Failed to check template. Please try again.',
                                icon: 'error'
                            });
                        });
                });
            }
        });
    </script>



    <script>
        document.getElementById('complete-shortlist-button').addEventListener('click', function(event) {
            event.preventDefault();

            // DataTables API kullanarak tüm sayfalardaki seçili checkbox'ları al
            const selectedIds = [];
            const previouslySelectedIds = [];
            const newlySelectedIds = [];

            dataTable.rows().every(function() {
                const checkbox = this.node().querySelector('.row_checkbox');
                if (checkbox && checkbox.checked) {
                    selectedIds.push(checkbox.value);
                    if (checkbox.dataset.previouslySelected === 'true') {
                        previouslySelectedIds.push(checkbox.value);
                    } else {
                        newlySelectedIds.push(checkbox.value);
                    }
                }
            });

            Swal.fire({
                title: 'Are you sure?',
                text: "This action cannot be undone",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, complete it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    document.getElementById('loading-overlay').style.display = 'flex';

                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = "{{ url('/admin/event/' . $event_id . '/complete-shortlist') }}";

                    // CSRF token
                    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute(
                        'content');
                    const csrfInput = document.createElement('input');
                    csrfInput.type = 'hidden';
                    csrfInput.name = '_token';
                    csrfInput.value = csrfToken;
                    form.appendChild(csrfInput);

                    // Event ID her durumda gönderilsin (gerekirse backend'de tekrar alınabilir)
                    const eventIdInput = document.createElement('input');
                    eventIdInput.type = 'hidden';
                    eventIdInput.name = 'event_id';
                    eventIdInput.value = "{{ $event_id }}";
                    form.appendChild(eventIdInput);

                    // Eğer seçili varsa onları da gönder
                    if (selectedIds.length > 0) {
                        selectedIds.forEach(id => {
                            const input = document.createElement('input');
                            input.type = 'hidden';
                            input.name = 'selected_ids[]';
                            input.value = id;
                            form.appendChild(input);
                        });
                    }

                    document.body.appendChild(form);
                    form.submit();
                }
            });
        });


        // Url den list parametresi geliyor ise o listi aktif et
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const listType = urlParams.get('list');

            if (listType === 'short') {
                document.querySelector('#full_list_button').classList.remove('active');
                document.querySelector('#short_list_button').classList.add('active');

                document.querySelector('#full_list').style.display = 'none';
                document.querySelector('#short_list').style.display = 'block';
            }

            // Sync Marketo butonuna tıklandiginda loading swal goster
            const syncMarketoBtn = document.querySelector('.sync_marketo_btn');
            if (syncMarketoBtn) {
                syncMarketoBtn.addEventListener('click', function(event) {
                    // Show loading SweetAlert
                    Swal.fire({
                        title: 'Syncing with Marketo...',
                        text: 'Please wait while we sync the data with Marketo.',
                        icon: 'info',
                        allowOutsideClick: false,
                        allowEscapeKey: false,
                        showConfirmButton: false,
                        didOpen: () => {
                        }
                    });
                });
            }

        });

    </script>

    @if (session('success'))
        <script>
            Swal.fire({
                title: "Success",
                text: "{!! session('success') !!}",
                icon: "success"
            });
        </script>
    @endif

    @if (session('warning'))
        <script>
            Swal.fire({
                title: "Warning",
                text: "{{ session('warning') }}",
                icon: "warning"
            });
        </script>
    @endif

@endpush
