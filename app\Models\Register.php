<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\hasMany;

class Register extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function get_event(): HasOne
    {
        return $this->HasOne(Event::class, 'id', 'event_id');
    }

    public function sessionRegisters() : hasMany
    {
        return $this->hasMany(SessionRegister::class, 'register_id');
    }
}
