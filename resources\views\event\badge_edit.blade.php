@extends('layouts.app')
@section('content')

    <div class="checkout">
        <div class="card shadow-none border">
            <div class="card-body p-4">
                @include('layouts.event_details')
                @include('layouts.page_navigation')

                <div class="mb-4">
                    <h5 class="fw-bold">Edit Badge</h5>
                    <p class="text-muted">Edit badge information for {{ $badge->first_name }} {{ $badge->last_name }}</p>
                </div>

                <div class="row">
                    <!-- Form Section -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="fw-bold mb-0">Badge Information</h6>
                            </div>
                            <div class="card-body">
                                <form id="badge_edit_form" method="POST" action="{{ route('panel.badge_update', ['event_id' => $event->id, 'id' => $badge->id]) }}" enctype="multipart/form-data">
                                    @csrf
                                    <input type="hidden" name="template_id" value="1">

                                    <!-- File inputs for background and logo -->
                                    <input type="file" id="badge_bg_file" name="badge_bg_file" style="display: none;" accept=".png">
                                    <input type="file" id="badge_logo_file" name="badge_logo_file" style="display: none;" accept=".png">

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group mb-3">
                                                <label for="first_name" class="form-label">First Name *</label>
                                                <input type="text" class="form-control @error('first_name') is-invalid @enderror"
                                                       id="first_name" name="first_name"
                                                       value="{{ old('first_name', $badge->first_name) }}" required>
                                                @error('first_name')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group mb-3">
                                                <label for="last_name" class="form-label">Last Name *</label>
                                                <input type="text" class="form-control @error('last_name') is-invalid @enderror"
                                                       id="last_name" name="last_name"
                                                       value="{{ old('last_name', $badge->last_name) }}" required>
                                                @error('last_name')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group mb-3">
                                        <label for="department" class="form-label">Job Title</label>
                                        <input type="text" class="form-control @error('department') is-invalid @enderror"
                                               id="department" name="department"
                                               value="{{ old('department', $badge->department ?? '') }}">
                                        @error('department')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="form-group mb-3">
                                        <label for="company" class="form-label">Company / Organization</label>
                                        <input type="text" class="form-control @error('company') is-invalid @enderror"
                                               id="company" name="company"
                                               value="{{ old('company', $badge->company ?? '') }}">
                                        @error('company')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="form-group mb-3">
                                        <label for="country" class="form-label">Country</label>
                                        <input type="text" class="form-control @error('country') is-invalid @enderror"
                                               id="country" name="country"
                                               value="{{ old('country', $badge->country ?? '') }}">
                                        @error('country')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="form-group mb-3">
                                        <label for="event_name" class="form-label">Event Name *</label>
                                        <input type="text" class="form-control @error('event_name') is-invalid @enderror"
                                               id="event_name" name="event_name"
                                               value="{{ old('event_name', $event->event_title) }}" required>
                                        @error('event_name')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="form-group mb-3">
                                        <label class="form-label">Email (Read Only)</label>
                                        <input type="text" class="form-control" value="{{ $badge->email }}" readonly>
                                        <small class="text-muted">Email cannot be changed</small>
                                    </div>

                                    <hr>
                                    <h6 class="fw-bold mb-3">Badge Template</h6>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group mb-3">
                                                <label class="form-label">Background Image</label>
                                                @if(isset($badge_templates[0]) && $badge_templates[0]->bg_url)
                                                    <div class="mb-2">
                                                        <small class="text-success">
                                                            <i class="ti ti-check"></i> Current: {{ basename($badge_templates[0]->bg_url) }}
                                                        </small>
                                                    </div>
                                                @endif
                                                <div class="d-flex gap-2">
                                                    <button type="button" class="btn btn-outline-primary btn-sm" id="upload-bg-btn">
                                                        <i class="ti ti-upload"></i> {{ isset($badge_templates[0]) && $badge_templates[0]->bg_url ? 'Change Background' : 'Upload Background' }}
                                                    </button>
                                                    <button type="button" class="btn btn-outline-secondary btn-sm" id="remove-bg-btn" style="display: none;">
                                                        <i class="ti ti-x"></i> Remove
                                                    </button>
                                                </div>
                                                <small class="text-muted">Recommended: 288x432px, PNG only</small>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group mb-3">
                                                <label class="form-label">Logo Image</label>
                                                @if(isset($badge_templates[0]) && $badge_templates[0]->logo_url)
                                                    <div class="mb-2">
                                                        <small class="text-success">
                                                            <i class="ti ti-check"></i> Current: {{ basename($badge_templates[0]->logo_url) }}
                                                        </small>
                                                    </div>
                                                @endif
                                                <div class="d-flex gap-2">
                                                    <button type="button" class="btn btn-outline-primary btn-sm" id="upload-logo-btn">
                                                        <i class="ti ti-upload"></i> {{ isset($badge_templates[0]) && $badge_templates[0]->logo_url ? 'Change Logo' : 'Upload Logo' }}
                                                    </button>
                                                    <button type="button" class="btn btn-outline-secondary btn-sm" id="remove-logo-btn" style="display: none;">
                                                        <i class="ti ti-x"></i> Remove
                                                    </button>
                                                </div>
                                                <small class="text-muted">Recommended: 150x60px, PNG only</small>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="d-flex gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="ti ti-device-floppy"></i> Update Badge
                                        </button>
                                        <a href="{{ route('panel.badge_list', ['event_id' => $event->id]) }}" class="btn btn-secondary">
                                            <i class="ti ti-arrow-left"></i> Back to List
                                        </a>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Preview Section -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="fw-bold mb-0">Badge Preview</h6>
                            </div>
                            <div class="card-body text-center">
                                <div id="badge-preview" class="badge-preview-container mx-auto"
                                     style="width: 288px; height: 432px; border: 1px solid #ddd; position: relative; background-size: cover; background-position: center;">

                                    @if(isset($badge_templates[0]) && $badge_templates[0]->bg_url)
                                        <div style="background-image: url('{{ asset($badge_templates[0]->bg_url) }}');
                                                    background-size: 288px 432px;
                                                    background-repeat: no-repeat;
                                                    background-position: center;
                                                    width: 100%;
                                                    height: 100%;
                                                    position: absolute;
                                                    top: 0;
                                                    left: 0;">
                                        </div>
                                    @endif

                                    @if(isset($badge_templates[0]) && $badge_templates[0]->logo_url)
                                        <img id="logo-preview" src="{{ asset($badge_templates[0]->logo_url) }}"
                                             style="position: absolute; top: 50px; left: 50%; width: 150px; height: 60px; object-fit: contain; transform: translateX(-50%);">
                                    @endif

                                    <div style="position: absolute; top: 150px; left: 50%; transform: translateX(-50%); text-align: center; width: 90%;">
                                        <h3 id="preview-name" class="fw-bold mb-2">{{ $badge->first_name }} {{ $badge->last_name }}</h3>
                                        <p id="preview-department" class="mb-1">{{ $badge->department ?? '' }}</p>
                                        <p id="preview-company" class="mb-2">{{ $badge->company ?? '' }}</p>
                                        <h4 id="preview-event" class="fw-bold mb-2">{{ $badge->event_name ?? $event->event_title }}</h4>
                                        <p id="preview-country" class="mb-2">{{ $badge->country ?? '' }}</p>
                                    </div>

                                    @if($badge->user_id)
                                        <img src="/qr/{{ $badge->user_id }}.png"
                                             style="position: absolute; bottom: 20px; width: 95px; height: 95px; right: 95px;">
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

@endsection

@push('css')
    <style>
        .badge-preview-container {
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .btn-group .btn {
            margin-right: 2px;
        }

        .btn-group .btn:last-child {
            margin-right: 0;
        }
    </style>
@endpush

@push('js')
    <script src="//cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Form input'larını dinle ve preview'ı güncelle
            const form = document.getElementById('badge_edit_form');
            const inputs = form.querySelectorAll('input[type="text"]');

            inputs.forEach(input => {
                input.addEventListener('input', updatePreview);
            });

            function updatePreview() {
                const firstName = document.getElementById('first_name').value;
                const lastName = document.getElementById('last_name').value;
                const department = document.getElementById('department').value;
                const company = document.getElementById('company').value;
                const country = document.getElementById('country').value;
                const eventName = document.getElementById('event_name').value;

                document.getElementById('preview-name').textContent = `${firstName} ${lastName}`;
                document.getElementById('preview-department').textContent = department;
                document.getElementById('preview-company').textContent = company;
                document.getElementById('preview-event').textContent = eventName;
                document.getElementById('preview-country').textContent = country;
            }

            // Background upload
            document.getElementById('upload-bg-btn').addEventListener('click', function() {
                document.getElementById('badge_bg_file').click();
            });

            document.getElementById('badge_bg_file').addEventListener('change', function(event) {
                const file = event.target.files[0];
                if (file) {
                    // PNG formatı kontrolü
                    if (!file.type.includes('png')) {
                        Swal.fire({
                            icon: 'error',
                            title: 'Invalid File Format',
                            text: 'Please select only PNG format images for background.',
                            confirmButtonText: 'OK'
                        });
                        this.value = ''; // Input'u temizle
                        return;
                    }

                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const previewContainer = document.getElementById('badge-preview');
                        const bgDiv = previewContainer.querySelector('div[style*="background-image"]');
                        if (bgDiv) {
                            bgDiv.style.backgroundImage = `url(${e.target.result})`;
                        } else {
                            previewContainer.style.backgroundImage = `url(${e.target.result})`;
                        }
                        document.getElementById('remove-bg-btn').style.display = 'inline-block';
                    };
                    reader.readAsDataURL(file);
                }
            });

            document.getElementById('remove-bg-btn').addEventListener('click', function() {
                document.getElementById('badge_bg_file').value = '';
                const previewContainer = document.getElementById('badge-preview');
                const bgDiv = previewContainer.querySelector('div[style*="background-image"]');
                if (bgDiv) {
                    bgDiv.style.backgroundImage = '';
                } else {
                    previewContainer.style.backgroundImage = '';
                }
                this.style.display = 'none';
            });

            // Logo upload
            document.getElementById('upload-logo-btn').addEventListener('click', function() {
                document.getElementById('badge_logo_file').click();
            });

            document.getElementById('badge_logo_file').addEventListener('change', function(event) {
                const file = event.target.files[0];
                if (file) {
                    // PNG formatı kontrolü
                    if (!file.type.includes('png')) {
                        Swal.fire({
                            icon: 'error',
                            title: 'Invalid File Format',
                            text: 'Please select only PNG format images for logo.',
                            confirmButtonText: 'OK'
                        });
                        this.value = ''; // Input'u temizle
                        return;
                    }

                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const previewContainer = document.getElementById('badge-preview');
                        let logoImg = previewContainer.querySelector('#logo-preview');

                        if (!logoImg) {
                            logoImg = document.createElement('img');
                            logoImg.className = 'uploaded-logo';
                            logoImg.style.cssText = 'position: absolute; top: 50px; left: 50%; width: 150px; height: 60px; object-fit: contain; transform: translateX(-50%); z-index: 10;';
                            previewContainer.appendChild(logoImg);
                        }

                        logoImg.src = e.target.result;
                        document.getElementById('remove-logo-btn').style.display = 'inline-block';
                    };
                    reader.readAsDataURL(file);
                }
            });

            document.getElementById('remove-logo-btn').addEventListener('click', function() {
                document.getElementById('badge_logo_file').value = '';
                const logoImg = document.querySelector('.uploaded-logo');
                if (logoImg) {
                    logoImg.remove();
                }
                this.style.display = 'none';
            });

            // Form submit validation
            form.addEventListener('submit', function(e) {
                const firstName = document.getElementById('first_name').value.trim();
                const lastName = document.getElementById('last_name').value.trim();
                const eventName = document.getElementById('event_name').value.trim();

                if (!firstName || !lastName || !eventName) {
                    e.preventDefault();
                    Swal.fire({
                        icon: 'warning',
                        title: 'Missing Required Fields',
                        text: 'Please fill in all required fields (First Name, Last Name, Event Name).',
                    });
                }
            });
        });
    </script>
@endpush
