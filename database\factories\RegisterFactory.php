<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Register>
 */
class RegisterFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'uuid' => $this->faker->uuid,
            'event_id' => 1,
            'first_name' => $this->faker->firstName,
            'last_name' => $this->faker->lastName,
            'email' => $this->faker->unique()->safeEmail,
            'country' => $this->faker->country,
            'specialty' => $this->faker->word,
            'company' => $this->faker->company,
            'qr_code' => null,
            'check_in' => 0,
            'check_in_time' => null,
            'badge' => null,
            'status' => 1,
        ];
    }
}
