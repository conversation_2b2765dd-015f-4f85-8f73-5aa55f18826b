@extends('layouts.bd-app')

@section('title', 'BD | Login')
@section('body-class', 'login-body')

@section('content')
<div class="container-fluid vh-100 d-flex align-items-center justify-content-center px-3">
    <div class="w-100 login-container">
        <!-- Logo -->
        <div class="text-center mb-5">
            <div class="d-flex align-items-center justify-content-start mb-4">
                <div class="logo-circle">
                    <img src="{{ asset('assets/bd/images/bd-compass-logo.png') }}" alt="BD Logo" style="width: 200px" class="logo-image">
                </div>
            </div>
        </div>

        <!-- Email Form -->
        <form action="{{ route('bd.send-otp') }}" method="POST">
            @csrf
            <div class="mb-4">
                <label for="email" class="email-label">
                    Your email address:
                </label>
                <input type="email"
                       class="form-control form-control-lg mt-2 email-input @error('email') is-invalid @enderror"
                       id="email"
                       name="email"
                       value="{{ old('email') }}"
                       required>
                @error('email')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <!-- Get OTP Button -->
            <div class="text-end">
                <style>
                    .otp-button:hover {
                        color: #ffffff;
                        background-color: #0d79dd;
                        border-color: #04589c;
                    }
                </style>
                <button type="submit" class="btn btn-lg px-4 py-2 fw-semibold otp-button">
                    Get OTP
                </button>
            </div>
        </form>
    </div>
</div>

@if(session('error'))
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            BDUtils.showToast('{{ session('error') }}', 'error');
        });
    </script>
@endif

@if(session('success'))
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            BDUtils.showToast('{{ session('success') }}', 'success');
        });
    </script>
@endif
@endsection
