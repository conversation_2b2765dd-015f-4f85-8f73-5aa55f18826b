@extends('layouts.app')
@section('content')

    <div class="checkout">
        <div class="card shadow-none border">
            <div class="card-body p-4">
                @include('layouts.event_details')
                @include('layouts.page_navigation')

                <!-- Dropdown and Search -->
                <div class="d-flex flex-row justify-content-between ">
                    <div class="d-flex flex-row gap-3">
                        <p class="checkin manualcheckin active">Manual Check-in
                        </p>
                        <p class="checkin qrcheckin">QR Check-in</p>
                    </div>
                    <form action="{{ url('/admin/event/' . $event->id . '/checkin/search') }}" method="POST"
                        class="align-items-center dropdown__search__block">
                        @csrf
                        <div class="input-group" style="position: relative; border-radius: 8px;">
                            <input type="text" name="search" class="form-control" placeholder="Search Attendees"
                                value="{{ request('search') }}">
                            <span style="position: absolute; right: 10px; top: 10px;">
                                <button type="submit" style="border: none; background: transparent; padding: 0;">
                                    <i class="ti ti-search"></i>
                                </button>
                            </span>
                        </div>
                    </form>

                </div>

                {{-- manuel checkin --}}
                <div class="manuel__checkin" id="manualCheckin">
                    <!-- table part -->

                    <div>
                        <table class="table table-bordered table-hover">
                            <thead class="">
                                <tr style="border-top: none">
                                    <th>Email</th>
                                    <th>First name</th>
                                    <th>Last name</th>
                                    <th>Country</th>
                                    <th>Company</th>
                                    <th>Event Check-in</th>
                                    @if ($event->sessions)
                                        @foreach ($event->sessions as $session)
                                            <th>{{ $session->session_title }}</th>
                                        @endforeach
                                    @endif
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($registers as $register)
                                    <tr>
                                        <td>{{ $register->email }}</td>
                                        <td>{{ $register->first_name }}</td>
                                        <td>{{ $register->last_name }}</td>
                                        <td>{{ $register->country }}</td>
                                        <td>{{ $register->company }}</td>
                                        <td>
                                            <form id="checkInForm_{{ $register->id }}"
                                                action="{{ route('panel.set_check_in_manual', ['uuid' => $register->uuid, 'event_id' => $event->id]) }}"
                                                method="GET">
                                                @csrf
                                                <select name="check_in_status"
                                                    class="form-select form-select-sm checkInDropdown"
                                                    onchange="submitCheckInForm({{ $register->id }})"
                                                    @if ($register->check_in) style="background-color: #E6FFFA" @else style="background-color: #fff7f5" @endif>
                                                    <option value="1" {{ $register->check_in ? 'selected' : '' }}>Yes
                                                    </option>
                                                    <option value="0" {{ !$register->check_in ? 'selected' : '' }}>No
                                                    </option>
                                                </select>
                                            </form>
                                        </td>
                                        @if ($event->sessions)
                                            @foreach ($event->sessions as $session)
                                                @php
                                                    $sessionRegister = $register->sessionRegisters
                                                        ->where('session_id', $session->id)
                                                        ->first();
                                                @endphp
                                                <td>
                                                    <select name="check_in_session_status"
                                                        id="check_in_session_status_{{ $event->id }}_{{ $register->id }}_{{ $session->id }}"
                                                        class="form-select form-select-sm checkInDropdown"
                                                        onchange="changeResigterSessionCheckIn({{ $event->id }}, {{ $register->id }}, {{ $session->id }})"
                                                        @if ($register->check_in == 0) disabled @endif
                                                        @if (optional($sessionRegister)->status) style="background-color: #E6FFFA" @else style="background-color: #fff7f5" @endif>
                                                        <option value="1"
                                                            {{ optional($sessionRegister)->status == 1 ? 'selected' : '' }}>
                                                            Yes</option>
                                                        <option value="0"
                                                            {{ optional($sessionRegister)->status == 0 ? 'selected' : '' }}>
                                                            No</option>
                                                    </select>
                                                </td>
                                            @endforeach
                                        @endif
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>


                    <div class="d-flex justify-content-end ">
                        {{-- <button type="submit" class="checkin_save_btn">Save</button> --}}
                    </div>

                </div>


                {{-- qr checkin --}}

                <div class="qr__checkin" id="qrCheckin" style="display: none; margin-top: 30px">
                    <p style="font-weight: 700">Scan QR Code</p>

                    <div class="row">
                        <div class="col-md-6">
                            <img src="{{ asset('images/qr_area.png') }}" alt="qr" id="qr_holder"
                                style="width: 300px; margin-top: 20px" />

                            <video id="video" width="300" height="300" autoplay style="display: none"></video>
                            <canvas id="canvas" width="300" height="300" style="display: none;"></canvas>
                        </div>
                        <div id="resultDiv" class="col-md-6" style="display: none;">
                            <h3>Informations:</h3>
                            <p id="result">UUID: Yok</p>

                        </div>
                    </div>

                    <div class="d-flex mt-4">
                        <button type="submit" class="scan_qr_btn">Scan QR Code</button>
                    </div>
                </div>

            </div>
        </div>
    </div>

@endsection

@push('css')
    <style>
        .qr-code-link {
            position: relative;
            display: inline-block;
        }

        .qr-code-container {
            position: relative;
            display: inline-block;
        }

        .qr-hover-icon {
            position: absolute;
            font-weight: 800;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 48px;
            color: #000000;
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .qr-code-link:hover .qr-hover-icon {
            opacity: 1;
        }

        .qr-code-link:hover img {
            opacity: 0.4;
        }
    </style>
@endpush

@push('js')
    <!-- ---------------------------------------------- -->
    <!-- current page js files -->
    <!-- ---------------------------------------------- -->
    <script src="{{ asset('js/manualCheckin__QRCheckin.js') }}"></script>

    <script src="https://cdn.jsdelivr.net/npm/jsqr/dist/jsQR.js"></script>
    <script>
        const sessions = @json($event->sessions);
        console.log(JSON.stringify(sessions));
        const video = document.getElementById('video');
        const canvas = document.getElementById('canvas');
        const context = canvas.getContext('2d');
        const resultElement = document.getElementById('result');
        const resultDiv = document.getElementById('resultDiv');

        document.querySelector('.scan_qr_btn').addEventListener('click', function() {
            document.getElementById('qr_holder').style.display = "none";
            document.getElementById('video').style.display = "block";
            navigator.mediaDevices.getUserMedia({
                    video: {
                        facingMode: "environment"
                    }
                })
                .then(function(stream) {
                    video.srcObject = stream;
                })
                .catch(function(err) {
                    console.log("Kamera erişim hatası: " + err);
                });
        });

        // QR kodu tara
        setInterval(() => {
            // Video boyutunda canvas oluştur
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            context.drawImage(video, 0, 0, canvas.width, canvas.height);

            // Görüntüdeki QR kodu tarat
            const imageData = context.getImageData(0, 0, canvas.width, canvas.height);
            const code = jsQR(imageData.data, canvas.width, canvas.height);

            if (code) {
                resultDiv.style.display = "block";
                resultElement.textContent = "UUID: " + code.data;

                let uuid = code.data;

                $.ajax({
                    url: '/admin/event/' + {{ $event->id }} + '/getRegisterDetails/' + uuid,
                    type: 'GET',
                    success: function(data) {
                        resultElement.innerHTML =
                            "UUID: " + code.data + "<br><br>" +
                            "<strong>Name:</strong> " + data.first_name + " " + data.last_name +
                            "<br>" +
                            "<strong>Email:</strong> " + data.email + "<br>" +
                            "<strong>Company:</strong> " + data.company + "<br>" +
                            "<strong>Country:</strong> " + data.country + "<br>" +
                            "<strong>Event Check-in:</strong> " + (data.check_in ? "Yes" : "No") +
                            "<br><br><br>"

                        if (data.check_in == 0) {
                            resultElement.innerHTML += "<a href='/admin/event/" + {{ $event->id }} +
                                "/register/set_check_in_qr/" + code.data +
                                "' class='btn btn-primary'>Check-in</a>";
                        } else {
                            let html = '';
                            if (sessions && sessions.length > 0) {
                                sessions.forEach(session => {

                                    // Checkin yapılan sessionlara "Done" yazısı eklenir
                                    const isSessionRegistered = data.sessionRegisters &&
                                        Array.isArray(data.sessionRegisters) &&
                                        data.sessionRegisters.some(sr => sr.session_id === session.id);

                                    html += `
                                        <div>
                                            <label for="check_in_session_status_${session.id}" style="width: 150px; margin-bottom: 20px">${session.session_title}</label>
                                        `;
                                    if (isSessionRegistered) {
                                        html += `
                                            <span style="color: green">Check-in Done</span><br/>
                                        `;
                                    } else {
                                        html += `
                                        <select name="check_in_session_status_${session.id}" onchange="changeResigterSessionCheckInQr(this, {{ $event->id }}, ${data.id}, ${session.id})" style="border-radius: 5px; padding: 3px">
                                                <option value="">Select</option>
                                                <option value="1">Yes</option>
                                                <option value="0">No</option>
                                            </select><br/>
                                            </div>
                                        `;

                                    }
                                });
                            }

                            resultElement.innerHTML += html;
                        }

                        document.getElementById('setRegisterButton').href = "/admin/event/" +
                            {{ $event->id }} + "/register/set_check_in_qr/" + code.data;

                        console.log(data);
                    },
                    error: function(error) {
                        var errorMessage = error.responseJSON.message;
                        resultElement.innerHTML = errorMessage;
                        console.log(error);
                    }
                });

                // data parametresi ile birlikte checkin route'una yönlendir
                // window.location.href = "/register/check-in/" + code.data;

            }
        }, 3000); // Her 1 saniyede QR kodu kontrol et
    </script>

    <script src="//cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        function submitCheckInForm(registerId) {
            Swal.fire({
                title: 'Are you sure?',
                text: "Register's checkin status will be updated.",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes!'
            }).then((result) => {
                if (result.isConfirmed) {
                    document.getElementById('checkInForm_' + registerId)
                        .submit(); // redirect to the href if confirmed
                }
            })

        }
    </script>

    {{-- 50px margin ile beyaz bg verildi --}}
    <script>
        function downloadQRCode(base64QRCode, fileName) {
            const canvas = document.createElement('canvas');
            const context = canvas.getContext('2d');
            const img = new Image();

            img.onload = function() {
                const margin = 50;
                canvas.width = img.width + margin * 2;
                canvas.height = img.height + margin * 2;

                context.fillStyle = '#FFFFFF';
                context.fillRect(0, 0, canvas.width, canvas.height);

                context.drawImage(img, margin, margin);

                const link = document.createElement('a');
                link.href = canvas.toDataURL('image/png');
                link.download = fileName;
                link.click();
            };

            img.src = base64QRCode;
        }

        document.querySelectorAll('.qr-code-link').forEach(link => {
            link.addEventListener('click', function(event) {
                event.preventDefault();
                const base64QRCode = this.querySelector('img').src;
                const row = this.closest('tr');
                const firstName = row.querySelector('td:nth-child(2)').textContent;
                const lastName = row.querySelector('td:nth-child(3)').textContent;
                const fileName = `qr_code_${firstName}_${lastName}.png`;
                downloadQRCode(base64QRCode, fileName);
            });
        });
    </script>

    <script>
        document.querySelectorAll('.link-copy').forEach(link => {
            link.addEventListener('click', function(event) {
                event.preventDefault();
                const url = this.getAttribute('data-url');
                navigator.clipboard.writeText(url).then(() => {
                    Swal.fire({
                        icon: 'success',
                        title: 'URL Copied',
                        showConfirmButton: false,
                        timer: 1000
                    });
                });
            });
        });

        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });

        function changeResigterSessionCheckIn(eventId, registerId, sessionId) {

            const selectEl = document.getElementById(`check_in_session_status_${eventId}_${registerId}_${sessionId}`);
            const status = selectEl.value;

            if (status === "1") {
                selectEl.style.backgroundColor = "#E6FFFA";
            } else {
                selectEl.style.backgroundColor = "#fff7f5";
            }

            $.ajax({
                url: '/admin/event/' + eventId + '/session_checkin',
                type: 'POST',
                data: {
                    register_id: registerId,
                    session_id: sessionId,
                    status: document.getElementById('check_in_session_status_' + eventId + '_' + registerId + '_' +
                        sessionId).value
                },
                success: function(data) {
                    Swal.fire({
                        title: "Success",
                        text: data.success,
                        icon: "success"
                    });
                },
                error: function(error) {
                    Swal.fire({
                        title: "Warning",
                        text: error,
                        icon: "warning"
                    });
                }
            });
        }

        function changeResigterSessionCheckInQr(selectElement, eventId, registerId, sessionId) {

            const selectedValue = selectElement.value;

            if(selectedValue == 1){
                $.ajax({
                    url: '/admin/event/' + eventId + '/session_checkin_qr',
                    type: 'POST',
                    data: {
                        register_id: registerId,
                        session_id: sessionId,
                        status: selectedValue,
                        event_id: eventId
                    },
                    success: function(data) {
                        Swal.fire({
                            title: "Success",
                            text: data.message,
                            icon: "success"
                        }).then(() => {
                            if (data.redirect_url) {
                                window.location.href = data.redirect_url;
                            }
                        });
                    },
                    error: function(error) {
                        Swal.fire({
                            title: "Warning",
                            text: error,
                            icon: "warning"
                        });
                    }
                });
            }
        }
    </script>
@endpush
