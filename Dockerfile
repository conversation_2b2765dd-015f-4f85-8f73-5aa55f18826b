FROM public.ecr.aws/docker/library/debian:bullseye-slim AS builder
#RUN apt-get update && apt-get install -y build-essential wget && \
#    wget https://ftp.gnu.org/gnu/tar/tar-1.35.tar.gz && \
#    tar -xf tar-1.35.tar.gz && \
#    cd tar-1.35 && \
#    env FORCE_UNSAFE_CONFIGURE=1 ./configure && make && make install
RUN apt-get update && apt-get install -y curl && \
    curl -sL https://aka.ms/downloadazcopy-v10-linux -o azcopy.tar.gz && \
    tar -xf azcopy.tar.gz && \
    cp ./azcopy_linux_amd64_*/azcopy /usr/local/bin/azcopy


FROM 799121415609.dkr.ecr.eu-central-1.amazonaws.com/fly-laravel:8.3 as base
#fideloper/fly-laravel:8.3 from dockerhub (debian)
#RUN rm -rf /bin/tar
#COPY --from=builder /usr/local/bin/tar /bin/tar

COPY . /var/www/html/
COPY nginx/default /etc/nginx/sites-enabled/default
WORKDIR /var/www/html/


# Install Imagick , memcached extension and dependencies
RUN apt-get update && apt-get install -y \
    libmagickwand-dev \
    php8.3-imagick \
    php8.3-memcached \
    && rm -rf /var/lib/apt/lists/*
# php.ini configuration
#php openbasedir settings
RUN sed -i "s'/var/www/html:/dev/stdout:/tmp'/var/www/html:/dev/stdout:/tmp:/static'g" /etc/php/8.3/fpm/pool.d/www.conf
COPY --from=builder /usr/local/bin/azcopy /usr/local/bin/azcopy
RUN chmod +x /usr/local/bin/azcopy

# env ayarı
RUN sed -i 's|/var/www/html:/dev/stdout:/tmp|/var/www/html:/dev/stdout:/tmp:/static|g' /etc/php/8.3/fpm/pool.d/www.conf && \
    sed -i 's/^upload_max_filesize\s*=.*/upload_max_filesize = 512M/' /etc/php/8.3/fpm/php.ini && \
    sed -i 's/^post_max_size\s*=.*/post_max_size = 512M/' /etc/php/8.3/fpm/php.ini && \
    sed -i 's/^upload_max_filesize\s*=.*/upload_max_filesize = 512M/' /etc/php/8.3/cli/php.ini && \
    sed -i 's/^post_max_size\s*=.*/post_max_size = 512M/' /etc/php/8.3/cli/php.ini && \
    sed -i 's/^max_file_uploads\s*=.*/max_file_uploads = 100/' /etc/php/8.3/fpm/php.ini

COPY --from=public.ecr.aws/docker/library/composer:2 /usr/bin/composer /usr/local/bin/composer
RUN composer install
EXPOSE 8080
