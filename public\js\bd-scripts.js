/**
 * BD Frontend JavaScript
 */

// Side Menu Functionality
class BDSideMenu {
    constructor() {
        this.menuToggle = document.getElementById('menuToggle');
        this.sideMenu = document.getElementById('sideMenu');
        this.sideMenuOverlay = document.getElementById('sideMenuOverlay');
        this.sideMenuClose = document.getElementById('sideMenuClose');

        this.init();
    }

    init() {
        if (this.menuToggle) {
            this.menuToggle.addEventListener('click', () => this.openSideMenu());
        }

        if (this.sideMenuClose) {
            this.sideMenuClose.addEventListener('click', () => this.closeSideMenu());
        }

        if (this.sideMenuOverlay) {
            this.sideMenuOverlay.addEventListener('click', () => this.closeSideMenu());
        }
    }

    openSideMenu() {
        if (this.sideMenu && this.sideMenuOverlay) {
            this.sideMenu.classList.add('show');
            this.sideMenuOverlay.classList.add('show');
            document.body.style.overflow = 'hidden';
        }
    }

    closeSideMenu() {
        if (this.sideMenu && this.sideMenuOverlay) {
            this.sideMenu.classList.remove('show');
            this.sideMenuOverlay.classList.remove('show');
            document.body.style.overflow = '';
        }
    }
}

// Tab Functionality
class BDTabs {
    constructor() {
        this.tabButtons = document.querySelectorAll('.tab-btn');
        this.tabContents = document.querySelectorAll('.tab-content');

        this.init();
    }

    init() {
        this.tabButtons.forEach(button => {
            button.addEventListener('click', (e) => this.switchTab(e));
        });
    }

    switchTab(e) {
        const targetTab = e.target.getAttribute('data-tab');

        // Remove active class from all buttons
        this.tabButtons.forEach(btn => btn.classList.remove('active'));

        // Add active class to clicked button
        e.target.classList.add('active');

        // Hide all tab contents with fade out
        this.tabContents.forEach(content => {
            content.classList.remove('active');
        });

        // Show target tab content with fade in
        setTimeout(() => {
            const targetContent = document.getElementById(targetTab);
            if (targetContent) {
                targetContent.classList.add('active');
            }
        }, 150);
    }
}

// Dropdown Functionality
class BDDropdown {
    constructor() {
        this.dropdownBtn = document.getElementById('eventDropdown');
        this.dropdownMenu = document.getElementById('dropdownMenu');
        this.dropdownText = document.querySelector('.dropdown-text');
        this.chevronIcon = this.dropdownBtn?.querySelector('i');
        this.eventOptions = document.querySelectorAll('.event-option');

        this.init();
    }

    init() {
        if (this.dropdownBtn) {
            this.dropdownBtn.addEventListener('click', (e) => this.toggleDropdown(e));
        }

        this.eventOptions.forEach(option => {
            option.addEventListener('click', (e) => this.selectOption(e));
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', (e) => this.closeOnOutsideClick(e));
    }

    toggleDropdown(e) {
        e.preventDefault();
        e.stopPropagation();

        if (this.dropdownMenu) {
            this.dropdownMenu.classList.toggle('show');

            if (this.chevronIcon) {
                if (this.dropdownMenu.classList.contains('show')) {
                    this.chevronIcon.classList.remove('bi-chevron-down');
                    this.chevronIcon.classList.add('bi-chevron-up');
                } else {
                    this.chevronIcon.classList.remove('bi-chevron-up');
                    this.chevronIcon.classList.add('bi-chevron-down');
                }
            }
        }
    }

    selectOption(e) {
        const eventName = e.target.getAttribute('data-event');

        // Update button text
        if (this.dropdownText) {
            this.dropdownText.textContent = eventName;
        }

        // Update active state
        this.eventOptions.forEach(opt => opt.classList.remove('active'));
        e.target.classList.add('active');

        // Close dropdown
        this.closeDropdown();

        // Show event details section
        const eventDetailsSection = document.getElementById('eventDetailsSection');
        if (eventDetailsSection) {
            eventDetailsSection.classList.remove('d-none');
        }
    }

    closeDropdown() {
        if (this.dropdownMenu) {
            this.dropdownMenu.classList.remove('show');
        }

        if (this.chevronIcon) {
            this.chevronIcon.classList.remove('bi-chevron-up');
            this.chevronIcon.classList.add('bi-chevron-down');
        }
    }

    closeOnOutsideClick(e) {
        if (this.dropdownBtn && this.dropdownMenu) {
            if (!this.dropdownBtn.contains(e.target) && !this.dropdownMenu.contains(e.target)) {
                this.closeDropdown();
            }
        }
    }
}

// Event Card Click Handler
class BDEventCards {
    constructor() {
        this.eventCards = document.querySelectorAll('.event-card');
        this.init();
    }

    init() {
        this.eventCards.forEach(card => {
            card.addEventListener('click', () => {
                window.location.href = '/event-detail';
            });
        });
    }
}

// Resend OTP Timer
class BDResendTimer {
    constructor() {
        this.resendSpan = document.querySelector('.resend-otp span');
        this.timer = 30;

        this.init();
    }

    init() {
        if (this.resendSpan) {
            this.startTimer();
            this.resendSpan.addEventListener('click', () => this.handleResend());
        }
    }

    startTimer() {
        this.resendSpan.style.pointerEvents = 'none';
        this.resendSpan.style.opacity = '0.5';
        this.resendSpan.style.textDecoration = 'none';
        this.resendSpan.style.cursor = 'default';

        const interval = setInterval(() => {
            this.timer--;
            this.resendSpan.textContent = `Resend in ${this.timer} seconds`;

            if (this.timer <= 0) {
                clearInterval(interval);
                this.resendSpan.textContent = 'Resend';
                this.resendSpan.style.pointerEvents = 'auto';
                this.resendSpan.style.opacity = '1';
                this.resendSpan.style.textDecoration = 'underline';
                this.resendSpan.style.cursor = 'pointer';
                this.timer = 30;
            }
        }, 1000);
    }

    handleResend() {
        // Handle resend OTP logic here
        console.log('Resending OTP...');
        this.startTimer();
    }
}

// Smooth Scroll for Back Button
class BDNavigation {
    constructor() {
        this.backButtons = document.querySelectorAll('.back-btn');
        this.init();
    }

    init() {
        this.backButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                history.back();
            });
        });
    }
}

// Initialize all BD components when DOM is loaded
document.addEventListener('DOMContentLoaded', function () {
    // Initialize components
    new BDSideMenu();
    new BDTabs();
    new BDDropdown();
    new BDEventCards();
    new BDResendTimer();
    new BDNavigation();

    // Add smooth transitions to all elements
    document.body.style.transition = 'all 0.3s ease';

    // Add loading states to buttons
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(btn => {
        btn.addEventListener('click', function () {
            if (!this.classList.contains('no-loading')) {
                this.style.opacity = '0.7';
                setTimeout(() => {
                    this.style.opacity = '1';
                }, 200);
            }
        });
    });
});

// Utility Functions
const BDUtils = {
    // Format date
    formatDate: function (date) {
        const options = {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        };
        return new Date(date).toLocaleDateString('en-US', options);
    },

    // Show toast notification
    showToast: function (message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.textContent = message;

        // Add toast styles
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'success' ? '#4caf50' : type === 'error' ? '#f44336' : '#0A599A'};
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            z-index: 9999;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
        `;

        document.body.appendChild(toast);

        // Show toast
        setTimeout(() => {
            toast.style.opacity = '1';
            toast.style.transform = 'translateX(0)';
        }, 100);

        // Hide toast after 3 seconds
        setTimeout(() => {
            toast.style.opacity = '0';
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 300);
        }, 3000);
    },

    // Loading state
    setLoading: function (element, loading = true) {
        if (loading) {
            element.disabled = true;
            element.style.opacity = '0.7';
            element.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Loading...';
        } else {
            element.disabled = false;
            element.style.opacity = '1';
        }
    }
};

// Export for use in other files
window.BDUtils = BDUtils;
