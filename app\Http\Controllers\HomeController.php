<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Event;
use App\Models\Country;
use App\Models\BusinessUnit;
use Carbon\Carbon;

class HomeController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index(Request $request)
    {
        $events = Event::query()->join('event_dates', 'events.id', '=', 'event_dates.event_id');
        if ($request->search) {
            $events->where(function ($query) use ($request) {
                $query->where('event_title', 'like', '%'.$request->search.'%')
                      ->orWhere('event_dates.event_location', 'like', '%'.$request->search.'%');
            });
        }

        if($request->sort_by){
            if($request->sort_by == 'date_asc'){
                $events->orderBy('event_dates.event_date', 'ASC');
            }
            if($request->sort_by == 'date_desc'){
                $events->orderBy('event_dates.event_date', 'DESC');
            }
            if($request->sort_by == 'az'){
                $events->orderBy('events.event_title', 'ASC');
            }
            if($request->sort_by == 'za'){
                $events->orderBy('events.event_title', 'DESC');
            }
            if($request->sort_by == 'latest'){
                $oneMonthAgo = Carbon::now()->subMonth();
                $events->where('events.created_at', '>', $oneMonthAgo);
                $events->orderBy('events.created_at', 'DESC');
            }
            if($request->sort_by == 'two_weeks'){
                $twoWeeksAgo = Carbon::now()->subWeeks(2);
                $events->where('events.created_at', '>', $twoWeeksAgo);
                $events->orderBy('events.created_at', 'DESC');
            }
        }else{
            $events->orderBy('events.created_at', 'DESC');
        }

        $events = $events->select('events.id', 'events.event_title', 'events.event_banner', 'events.status');

        $events = $events->groupBy('events.id', 'events.event_title', 'events.event_banner', 'events.status')->get();

        $countries = Country::get();
        $business_units = BusinessUnit::get();

        return view('home', ['events' => $events, 'countries' => $countries, 'business_units' => $business_units]);
    }

    public function filter(Request $request)
    {
        $events = Event::query()->join('event_dates', 'events.id', '=', 'event_dates.event_id');

        if ($request->event_location) {
            $events->where('event_dates.event_country', $request->event_location);
        }

        if ($request->business_unit) {
            $events->where('events.business_unit', $request->business_unit);
        }

        if ($request->search) {
            $events->where('events.event_title', 'like', '%'.$request->search.'%');
        }

        $events = $events->select('events.id', 'events.event_title', 'events.event_banner')->orderBy('events.created_at', 'DESC')->get();

        $countries = Country::get();
        $business_units = BusinessUnit::get();

        return view('home', ['events' => $events, 'countries' => $countries, 'business_units' => $business_units]);
    }

    /**
     * Show PHP information page.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function phpinfo()
    {
        return view('admin.phpinfo');
    }
}
