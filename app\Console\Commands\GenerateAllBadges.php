<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Event;
use App\Models\Register;
use App\Models\Badge;
use App\Models\BadgeTemplate;
use App\Http\Controllers\BadgeController;

class GenerateAllBadges extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'badges:generate-all {event_id? : The event ID to generate badges for} {--all : Generate badges for all completed events}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate badges for all shortlisted users in an event or all completed events';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $eventId = $this->argument('event_id');
        $generateAll = $this->option('all');

        if ($generateAll) {
            $this->generateForAllEvents();
        } elseif ($eventId) {
            $this->generateForEvent($eventId);
        } else {
            $this->error('Please provide an event ID or use --all option');
            return 1;
        }

        return 0;
    }

    /**
     * Generate badges for all completed events
     */
    private function generateForAllEvents()
    {
        $this->info('Generating badges for all completed events...');
        
        $completedEvents = Event::where('is_completed', 2)->get();
        
        if ($completedEvents->isEmpty()) {
            $this->warn('No completed events found.');
            return;
        }

        $this->info("Found {$completedEvents->count()} completed events.");

        foreach ($completedEvents as $event) {
            $this->info("Processing event: {$event->name} (ID: {$event->id})");
            $this->generateForEvent($event->id);
        }

        $this->info('All completed events processed!');
    }

    /**
     * Generate badges for a specific event
     */
    private function generateForEvent($eventId)
    {
        $event = Event::find($eventId);
        
        if (!$event) {
            $this->error("Event with ID {$eventId} not found.");
            return;
        }

        $this->info("Generating badges for event: {$event->name}");

        // Template kontrolü
        $template = BadgeTemplate::where('event_id', $eventId)->where('name', 1)->first();
        
        if (!$template) {
            $this->error("Badge template not found for event {$eventId}. Please create a template first.");
            return;
        }

        // Background dosyası kontrolü
        $bg_path = public_path('/' . $template->bg_url);
        if (!file_exists($bg_path)) {
            $this->error("Template background image not found: {$template->bg_url}");
            return;
        }

        // Shortlist'teki register'ları al (status=1)
        $registers = Register::where('event_id', $eventId)
            ->where('status', 1)
            ->get();

        if ($registers->isEmpty()) {
            $this->warn("No users found in shortlist for event {$eventId}.");
            return;
        }

        $this->info("Found {$registers->count()} users in shortlist.");

        $successCount = 0;
        $errorCount = 0;
        $skippedCount = 0;

        $progressBar = $this->output->createProgressBar($registers->count());
        $progressBar->start();

        foreach ($registers as $register) {
            try {
                // Badge zaten var mı kontrol et
                $existingBadge = Badge::where('event_id', $eventId)
                    ->where('user_id', $register->user_id)
                    ->first();

                if ($existingBadge) {
                    $skippedCount++;
                    $progressBar->advance();
                    continue;
                }

                // Badge oluştur
                $badgeController = new BadgeController();
                $reflection = new \ReflectionClass($badgeController);
                $method = $reflection->getMethod('createSingleBadge');
                $method->setAccessible(true);
                
                $method->invoke($badgeController, $eventId, $register, 1);
                $successCount++;

            } catch (\Exception $e) {
                $this->error("Error creating badge for {$register->first_name} {$register->last_name}: " . $e->getMessage());
                $errorCount++;
            }

            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine();

        // Sonuçları göster
        $this->info("Badge generation completed for event: {$event->name}");
        $this->info("✅ Successfully created: {$successCount} badges");
        
        if ($skippedCount > 0) {
            $this->warn("⏭️  Skipped (already exists): {$skippedCount} badges");
        }
        
        if ($errorCount > 0) {
            $this->error("❌ Failed: {$errorCount} badges");
        }

        $this->info("Total processed: " . ($successCount + $skippedCount + $errorCount) . " users");
    }
}
