// Template design için basit JavaScript
// Artık sadece template design var, upload design kaldırıldı


/*document.getElementById('department').addEventListener('change', function() {
    var selectedValue = this.value;
    var previewContainer = document.querySelector('.preview-container');

    switch(selectedValue) {
        case 'Omni':
            previewContainer.style.background = 'url("/images/badge/bg_omni.png") no-repeat center center';
            break;
        case 'ComEx':
            previewContainer.style.background = 'url("/images/badge/bg_comex.png") no-repeat center center';
            break;
        case 'Tgs':
            previewContainer.style.background = 'url("/images/badge/bg_tgs.png") no-repeat center center';
            break;
        default:
            previewContainer.style.background = 'url("/images/badge/bg_default.png") no-repeat center center';
            break;
    }
})*/

// Upload ve Template formlarinda doldurulan alanlara gore onizleme
const updateTextContent = (inputId, targetClass) => {
    document.getElementById(inputId).addEventListener('input', function () {
        if (this.value.trim() !== '') {
            document.querySelectorAll(targetClass).forEach(el => el.textContent = this.value);
        } else {
            document.querySelectorAll(targetClass).forEach(el => el.textContent = '\u200B');
        }
    });
};
// updateTextContent('first_name', '.first_name');
// updateTextContent('last_name', '.last_name');
// updateTextContent('country', '.company_name');
// updateTextContent('template_first_name', '.template_first_name');
// updateTextContent('template_last_name', '.template_last_name');
updateTextContent('upload_specialty', '.upload_specialty');
updateTextContent('upload_event_name', '.upload_event_name');
updateTextContent('upload_company', '.upload_company');
updateTextContent('upload_country', '.upload_country');

updateTextContent('template_specialty', '.template_specialty');
updateTextContent('template_event_name', '.template_event_name');
updateTextContent('template_company', '.template_company');
updateTextContent('template_country', '.template_country');


/* UPLOAD INPUTS */
document.getElementById('upload_first_name').addEventListener('input', function () {
    document.querySelectorAll('.upload_full_name').forEach(el => el.textContent = `${this.value} ${document.getElementById('upload_last_name').value}`);
});
document.getElementById('upload_last_name').addEventListener('input', function () {
    document.querySelectorAll('.upload_full_name').forEach(el => el.textContent = `${document.getElementById('upload_first_name').value} ${this.value}`);
});
document.getElementById('upload_first_name').addEventListener('input', function () {
    if (this.value !== '') {
        document.querySelectorAll('.upload_full_name').forEach(el => el.textContent = `${document.getElementById('upload_first_name').value} ${document.getElementById('upload_last_name').value}`);
    }
});

/* TEMPLATE INPUTS */
document.getElementById('template_first_name').addEventListener('input', function () {
    document.querySelectorAll('.template_full_name').forEach(el => el.textContent = `${this.value} ${document.getElementById('template_last_name').value}`);
});
document.getElementById('template_last_name').addEventListener('input', function () {
    document.querySelectorAll('.template_full_name').forEach(el => el.textContent = `${document.getElementById('template_first_name').value} ${this.value}`);
});
document.getElementById('template_first_name').addEventListener('input', function () {
    if (this.value !== '') {
        document.querySelectorAll('.template_full_name').forEach(el => el.textContent = `${document.getElementById('template_first_name').value} ${document.getElementById('template_last_name').value}`);
    }
});


// Sayfa yüklendiğinde çalıştır
document.addEventListener('DOMContentLoaded', function () {
    // Upload
    if (document.getElementById('upload_company').value !== '') {
        document.querySelectorAll('.upload_full_name').forEach(el => el.textContent = `${document.getElementById('upload_first_name').value} ${document.getElementById('upload_last_name').value}`);
    }
    if (document.getElementById('upload_company').value !== '') {
        document.querySelectorAll('.upload_company').forEach(el => el.textContent = document.getElementById('upload_company').value);
    }
    if (document.getElementById('upload_event_name').value !== '') {
        document.querySelectorAll('.upload_event_name').forEach(el => el.textContent = document.getElementById('upload_event_name').value);
    }

    // Template
    if (document.getElementById('template_company').value !== '') {
        document.querySelectorAll('.template_full_name').forEach(el => el.textContent = `${document.getElementById('template_first_name').value} ${document.getElementById('template_last_name').value}`);
    }
    if (document.getElementById('template_company').value !== '') {
        document.querySelectorAll('.template_company').forEach(el => el.textContent = document.getElementById('template_company').value);
    }
    if (document.getElementById('template_event_name').value !== '') {
        document.querySelectorAll('.template_event_name').forEach(el => el.textContent = document.getElementById('template_event_name').value);
    }
});

// template sayfasında renk seçildiğinde previewde renk değişmesi
document.querySelector('input[name="first_name_color"]').addEventListener('input', function () {
    const color = this.value;
    document.querySelectorAll('.template_preview_fn').forEach(el => el.style.color = color);
    document.querySelector('input[name="last_name_color"]').value = color;
});
document.querySelector('input[name="last_name_color"]').addEventListener('input', function () {
    const color = this.value;
    document.querySelectorAll('.template_preview_fn').forEach(el => el.style.color = color);
    document.querySelector('input[name="first_name_color"]').value = color;
});
document.querySelector('input[name="country_color"]').addEventListener('input', function () {
    const color = this.value;
    document.querySelectorAll('.template_preview_c').forEach(el => el.style.color = color);
});
document.querySelector('input[name="specialty_color"]').addEventListener('input', function () {
    const color = this.value;
    document.querySelectorAll('.template_preview_specialty').forEach(el => el.style.color = color);
});
document.querySelector('input[name="event_name_color"]').addEventListener('input', function () {
    const color = this.value;
    document.querySelectorAll('.template_preview_event_name').forEach(el => el.style.color = color);
});
document.querySelector('input[name="company_color"]').addEventListener('input', function () {
    const color = this.value;
    document.querySelectorAll('.template_preview_company').forEach(el => el.style.color = color);
});
