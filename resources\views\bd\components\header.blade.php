<!-- B<PERSON> -->
 <style>
    @media only screen and (max-width: 996px) and (orientation: landscape) {
    .side-menu {
        max-height: 100vh;
    }
}
 </style>
<header class="dashboard-header">
    <div class="container px-3 py-2">
        <div class="d-flex justify-content-between align-items-center">
            <!-- Left side - Logo and Navigation -->
            <div class="d-flex align-items-center">
                <div class="d-flex align-items-center me-4">
                    <a href="{{ route('bd.dashboard') }}"><img src="{{ asset('assets/bd/images/bd-compass-logo.png') }}" style="width:115px;" alt="BD Logo" class="logo-image me-2"></a>

                </div>
                <!-- Desktop Navigation -->
                <nav class="d-none d-lg-flex">
                    <a href="{{ route('bd.travel-documents') }}" class="nav-link me-4 {{ request()->routeIs('bd.travel-documents') ? 'active' : '' }}">
                        Travel Documents
                    </a>
                    <a href="{{ route('bd.dashboard') }}" class="nav-link me-4 {{ request()->routeIs('bd.dashboard*') ? 'active' : '' }}">
                        My Events
                    </a>
                    <a href="{{ route('bd.my-qr') }}" class="nav-link {{ request()->routeIs('bd.my-qr') ? 'active' : '' }}">
                        My QR
                    </a>
                </nav>
            </div>

            <div class="d-none d-lg-block text-center flex-grow-1">
                <p class="welcome-text-desktop mb-0">Welcome to your BD events dashboard</p>
            </div>

            <!-- Right side - User Profile and Menu -->
            <div class="d-flex align-items-center">
                <!-- Desktop User Profile -->
                <div class="d-none d-lg-flex align-items-center position-relative">
                    <div class="text-end me-3">
                        <h6 class="user-name-desktop mb-0">{{ $user->name ?? 'Dr. John Simmons' }}</h6>
                        <small class="user-title-desktop">{{ $user->title ?? 'Title placeholder' }}</small>
                    </div>
                    <div class="dropdown">
                        @php
                            $userName = $user->name ?? 'Dr. John Simmons';
                            $nameParts = explode(' ', trim($userName));
                            $initials = '';
                            if (count($nameParts) >= 2) {
                                $initials = strtoupper(substr($nameParts[0], 0, 1) . substr($nameParts[1], 0, 1));
                            } else {
                                $initials = strtoupper(substr($userName, 0, 2));
                            }
                        @endphp
                        <div class="profile-avatar-desktop dropdown-toggle"
                             id="profileDropdown"
                             data-bs-toggle="dropdown"
                             aria-expanded="false"
                             style="cursor: pointer;">
                            {{ $initials }}
                        </div>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="profileDropdown">
                            <li class="dropdown-header">
                                <div class="d-flex align-items-center">
                                    <div class="profile-avatar-small me-2">
                                        {{ $initials }}
                                    </div>
                                    <div>
                                        <h6 class="mb-0">{{ $user->name ?? 'Dr. John Simmons' }}</h6>
                                        <small class="text-muted">{{ $user->email ?? '<EMAIL>' }}</small>
                                    </div>
                                </div>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form action="{{ route('bd.logout') }}" method="POST" class="d-inline w-100">
                                    @csrf
                                    <button type="submit" class="dropdown-item d-flex align-items-center text-danger w-100 border-0 bg-transparent">
                                        <i class="bi bi-box-arrow-right me-2"></i>
                                        Logout
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- Mobile Menu Button -->
                <button class="btn btn-link p-0 text-dark d-lg-none" id="menuToggle">
                    <i class="bi bi-list fs-4"></i>
                </button>
            </div>
        </div>
    </div>
</header>

<!-- Side Menu Overlay -->
<div class="side-menu-overlay" id="sideMenuOverlay"></div>

<!-- Side Menu -->
<div class="side-menu" id="sideMenu">
    <div class="side-menu-header">
        <div class="side-menu-logo">
            <img src="{{ asset('assets/bd/images/bd-logo.png') }}" alt="BD Logo" class="logo-image">
        </div>
        <button class="side-menu-close" id="sideMenuClose">
            <i class="bi bi-x"></i>
        </button>
    </div>

    <div class="side-menu-profile">
        <div class="side-menu-profile-avatar">
            {{ $initials }}
        </div>
        <div class="side-menu-profile-info">
            <h5>{{ $user->name ?? 'Dr. John Simmons' }}</h5>
            <small>{{ $user->title ?? 'Title placeholder' }}</small>
        </div>
    </div>

    <nav class="side-menu-nav">
        <a href="{{ route('bd.travel-documents') }}" class="side-menu-item">Travel Documents</a>
        <a href="{{ route('bd.dashboard') }}" class="side-menu-item">My Events</a>
        <a href="{{ route('bd.my-qr') }}" class="side-menu-item">My QR</a>
        <form action="{{ route('bd.logout') }}" method="POST" style="display: inline;">
            @csrf
            <button type="submit" class="side-menu-item" style="background: none; border: none; width: 100%; text-align: left;">
                Logout
            </button>
        </form>
    </nav>
</div>
