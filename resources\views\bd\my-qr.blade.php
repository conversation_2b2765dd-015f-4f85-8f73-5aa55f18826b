@extends('layouts.bd-app')

@section('title', 'BD | My QR Codes')

@section('content')
@include('bd.components.header', ['user' => $user ?? null])

<!-- Welcome Section -->
<div class="container px-3 py-3">

    <!-- QR Code Display -->
    <div class="qr-display-container">
        @if($qrData)
            <div class="qr-main-card">
                <div class="text-center">
                    <div class="qr-code-main-container mb-4">
                        <img src="{{ $qrData->qr_code_url }}?v={{ time() }}" alt="My QR Code" class="qr-code-main" onclick="openQrModal('{{ $qrData->qr_code_url }}?v={{ time() }}', 'My QR Code')">
                    </div>

                    <h5 class="qr-title mb-3">My QR Code</h5>

                    <div class="qr-user-info mb-4">
                        <p class="mb-1"><strong>{{ $qrData->user_name }}</strong></p>
                        <small class="text-muted">{{ $qrData->user_email }}</small><br>
                        <small class="text-muted">User ID: {{ $qrData->user_id }}</small>
                    </div>

                    <div class="qr-actions">
                        <button class="btn btn-primary me-2" onclick="downloadQr('{{ $qrData->qr_code_url }}', 'My_QR_Code')">
                            <i class="fas fa-download"></i> Download QR Code
                        </button>
                    </div>
                </div>
            </div>
        @else
            <div class="text-center py-5">
                {{--<img src="{{ asset('assets/bd/images/no-events.png') }}" alt="No QR Code" class="mb-3" style="width: 100px; opacity: 0.5;">--}}
                <h6 class="text-muted">No QR code available</h6>
                <p class="text-muted small">Register for an event to get your QR code.</p>
                <a href="{{ route('bd.dashboard') }}" class="btn btn-primary">Browse Events</a>
            </div>
        @endif
    </div>
</div>

<!-- QR Modal -->
<div class="modal fade" id="qrModal" tabindex="-1" aria-labelledby="qrModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="qrModalLabel">QR Code</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <img id="modalQrImage" src="" alt="QR Code" class="img-fluid">
                <p id="modalQrTitle" class="mt-3 mb-0"></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="modalDownloadBtn">Download</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.qr-main-card {
    background: white;
    border-radius: 16px;
    padding: 40px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    max-width: 400px;
    margin: 0 auto;
}

.qr-code-main-container {
    display: flex;
    justify-content: center;
    align-items: center;
}

.qr-code-main {
    width: 200px;
    height: 200px;
    border: 3px solid #0039D1;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 57, 209, 0.2);
}

.qr-code-main:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 16px rgba(0, 57, 209, 0.3);
}

.qr-title {
    color: #0039D1;
    font-weight: 600;
}

.qr-user-info {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #0039D1;
}

.qr-actions .btn {
    margin: 5px;
    min-width: 140px;
}

#modalQrImage {
    max-width: 300px;
    border: 2px solid #0039D1;
    border-radius: 8px;
}

@media (max-width: 768px) {
    .qr-main-card {
        padding: 30px 20px;
        margin: 0 15px;
    }

    .qr-code-main {
        width: 150px;
        height: 150px;
    }

    .qr-actions .btn {
        width: 100%;
        margin: 5px 0;
    }
}
</style>
@endpush

@push('scripts')
<script>
function openQrModal(qrUrl, title) {
    document.getElementById('modalQrImage').src = qrUrl;
    document.getElementById('modalQrTitle').textContent = title;
    document.getElementById('modalDownloadBtn').onclick = function() {
        downloadQr(qrUrl, 'My_QR_Code');
    };

    const modal = new bootstrap.Modal(document.getElementById('qrModal'));
    modal.show();
}

function downloadQr(qrUrl, fileName) {
    const link = document.createElement('a');
    link.href = qrUrl;
    link.download = `${fileName}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

function shareQr(qrUrl) {
    if (navigator.share) {
        navigator.share({
            title: 'My QR Code',
            url: qrUrl
        }).catch(console.error);
    } else {
        // Fallback: copy to clipboard
        navigator.clipboard.writeText(qrUrl).then(function() {
            if (typeof BDUtils !== 'undefined') {
                BDUtils.showToast('QR code URL copied to clipboard!', 'success');
            } else {
                alert('QR code URL copied to clipboard!');
            }
        }).catch(function() {
            if (typeof BDUtils !== 'undefined') {
                BDUtils.showToast('Could not copy URL', 'error');
            } else {
                alert('Could not copy URL');
            }
        });
    }
}
</script>
@endpush
