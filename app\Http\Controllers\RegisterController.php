<?php

namespace App\Http\Controllers;

use App\Models\Register;
use App\Models\Event;
use App\Models\SessionRegister;
use Illuminate\Http\Request;
use SimpleSoftwareIO\QrCode\Facades\QrCode;
use SimpleSoftwareIO\QrCode\Generator;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;
use BaconQrCode\Encoder\Encoder;
use BaconQrCode\Common\ErrorCorrectionLevel;

class RegisterController extends Controller
{
    public function getRegisterDetails($event_id, $uuid){
        try {
            $emailDecoded = base64_decode($uuid);

            $getRegister = Register::query()
                ->where('email', $emailDecoded)
                ->where('event_id', $event_id)
                ->firstOrFail();

            if($getRegister->status == 0){
                return response()->json([
                    'status' => 'error',
                    'message' => '<br> <strong>Note:</strong> This user is not in the <strong>shortlist</strong>'
                ], 404);
            }

            // Checkin yapılan sessionları getir
            $sessionRegisters = SessionRegister::query()
                ->where('register_id', $getRegister->id)
                ->where('event_id', $event_id)
                ->where('status', 1)
                ->get();

            $getRegister->sessionRegisters = $sessionRegisters;

            return response()->json($getRegister);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => '<br> <strong>Note:</strong> The QR code doesn\'t appear to be working, please check in manually.'
            ], 404);
        }
    }

    public function generateQrCode($event_id, $id){
        $getRegister = Register::findOrFail($id);

        // QR kod için veri belirle: Manuel badge ise email, normal ise uuid
        $qrData = ($getRegister->status == 3) ? $getRegister->email : $getRegister->uuid;

        try {
            $svgQrCode = QrCode::format('svg')->size(300)->generate($qrData);
            $qrCodeBase64 = base64_encode($svgQrCode);
        } catch (\Exception $e) {
            $placeholder = imagecreate(300, 300);
            $bg = imagecolorallocate($placeholder, 255, 255, 255);
            $text_color = imagecolorallocate($placeholder, 0, 0, 0);
            imagestring($placeholder, 5, 100, 140, 'QR Error', $text_color);

            ob_start();
            imagepng($placeholder);
            $qrCode = ob_get_contents();
            ob_end_clean();
            imagedestroy($placeholder);

            $qrCodeBase64 = base64_encode($qrCode);
        }

        $getRegister->qr_code = $qrCodeBase64;
        $getRegister->save();

        return back()->with('success', 'QR Code oluşturuldu');
    }

    public function setCheckinWithQR($event_id, $uuid){
        $register = Register::query()->where('uuid', $uuid)->where('event_id', $event_id)->firstOrFail();

        $register->check_in = 1;
        $register->save();
        user_check_in_manuel($register);
        return redirect()
            ->route('panel.badge_creation', ['register' => $register, 'event_id' => $event_id])
            ->with('success', 'Check-in Successfully');
    }

    public function setCheckinManual($event_id, $uuid, Request $request){
        $register = Register::query()->where('uuid', $uuid)->where('event_id', $event_id)->firstOrFail();

        /*if(!$register->qr_code){
            return back()
                ->with('error', 'QR code has not been created yet. QR code must be created first.');
        }*/

        $register->check_in = $request->check_in_status;
        $register->save();

        if ($request->check_in_status == 0) {
            return back()->with('success', 'Register Check-in Updated');
        }

        user_check_in_manuel($register);

        return redirect()
            ->route('panel.badge_creation', ['register' => $register, 'event_id' => $event_id])
                ->with('success', 'Check-in Successfully');
    }

    public function registerList($event_id){

        $event = Event::find($event_id);
        $users = Register::where('event_id', $event_id)->get();

        // Manuel badge sayısını hesapla
        $manualBadgeCount = Register::where('event_id', $event_id)->where('status', 3)->count();

        return view('event.register_check_in_list', ['event_id' => $event_id, 'users' => $users, 'event' => $event, 'manualBadgeCount' => $manualBadgeCount]);
    }

    public function sycnWithMarketo($event_id){

        $event = Event::find($event_id);
        $users = get_user_list($event->smart_list_id);

        // Mevcut kullanıcıları al
        $existingUsers = Register::query()->where('event_id', $event_id)->get()->keyBy('user_id');

        if($users){
           foreach($users as $user){
                $userT = Register::updateOrCreate(
                    ['user_id' => $user->id, 'event_id' => $event->id],
                    [
                        'uuid' => base64_encode($user->email),
                        'event_id' => $event_id,
                        'user_id' => $user->id,
                        'first_name' => $user->firstName,
                        'last_name' => $user->lastName,
                        'company' => $user->company,
                        'email' => $user->email,
                        'title' => $user->title,
                        'phone' => $user->phone
                    ]
                );

               // Güncellenen veya eklenen kullanıcıyı mevcut kullanıcılar listesinden çıkar
               $existingUsers->forget($user->id);
           }

           // Kalan kullanıcıları sil
           Register::whereIn('user_id', $existingUsers->keys())->where('event_id', $event_id)->delete();

        } else {
            Register::query()->where('event_id', $event_id)->delete();

            return redirect()
                ->route('panel.register_list', ['event_id' => $event_id])
                ->with('success', 'Synced with Marketo');
        }

        // Event henüz tamamlanmamışsa (is_completed != 2) otomatik seçim yap
        if ($event->is_completed != 2) {
            // Mevcut seçili kullanıcı sayısını al
            $currentSelectedCount = Register::where('event_id', $event->id)
                ->where('status', 1)
                ->count();

            // Eğer seçili kullanıcı sayısı kotadan azsa, eksik olanları seç
            if ($currentSelectedCount < $event->attendee_quota) {
                $needToSelect = $event->attendee_quota - $currentSelectedCount;

                $usersToSelect = Register::where('event_id', $event->id)
                    ->where('status', 0) // Henüz seçilmemiş olanlar
                    ->orderBy('id') // sırayla seç
                    ->limit($needToSelect)
                    ->get();

                foreach ($usersToSelect as $user) {
                    $user->status = 1;
                    $user->save();
                }

                \Log::info('Auto-selected users after Marketo sync', [
                    'event_id' => $event_id,
                    'selected_count' => $needToSelect,
                    'total_selected' => $currentSelectedCount + $needToSelect,
                    'quota' => $event->attendee_quota
                ]);
            }
        }

        return redirect()
            ->route('panel.register_list', ['event_id' => $event_id])
            ->with('success', 'Synced with Marketo');
    }

    public function completeShortlist($event_id, Request $request){
        \Log::info('completeShortlist function called', [
            'event_id' => $event_id,
            'request_event_id' => $request->input('event_id'),
            'selected_ids_count' => count($request->input('selected_ids', []))
        ]);

        $eventId = $request->input('event_id');
        $selectedIds = $request->input('selected_ids', []);

        \Log::info('Selected IDs for shortlist', [
            'selected_ids' => $selectedIds
        ]);

        $event = Event::find($event_id);

        $users = Register::where('event_id', $event_id)->get();

        $token = get_token();

        Register::where('event_id', $event_id)->update(['status' => 0]);

        foreach($users as $user){
            complete_user_list($user, $token);
        }

        $check_register = Register::where('event_id', $event_id)->count();

        if(!$check_register){
            return redirect()->route('panel.register_list', ['event_id' => $event_id, 'list'=>'short'])->with('error', 'Full list is empty, please check Smart List ID.');
        }

        if (empty($selectedIds)) {
            return redirect()->route('panel.register_list', ['event_id' => $event_id, 'list'=>'short'])->with('warning', 'User list is empty, please select users.');
        } else {
            // Sadece yeni seçilenlerin status'unu güncelle (önceki seçililer zaten status=1)
            $short_list = Register::where('event_id', $event_id)->whereIn('id', $selectedIds)->get();

            foreach($short_list as $short){
                \Log::info('Processing shortlist user', [
                    'user_id' => $short->user_id,
                    'current_status' => $short->status,
                    'email' => $short->email
                ]);

                if($short->status != 1) { // Sadece yeni seçilenleri güncelle
                    $short->status = 1;
                    $short->save();

                    \Log::info('Creating QR code for user', [
                        'user_id' => $short->user_id,
                        'smart_list_id' => $event->smart_list_id
                    ]);

                    // Sadece yeni seçilenlere QR kod oluştur
                    $this->create_qr($event_id, $event->smart_list_id, $short->user_id);
                } else {
                    \Log::info('User already has status 1, skipping QR creation', [
                        'user_id' => $short->user_id
                    ]);
                }
            }
        }


        $event->is_completed = 2;
        $event->save();

        $users_short_list = Register::where('event_id', $event_id)->where('status', 1)->get();

        foreach($users_short_list as $user){
            complete_short_list($user, $token);
        }


        return redirect()->route('panel.register_list', ['event_id' => $event_id, 'list'=>'short'])->with('success', 'Completed Shortlist');
    }

    public function create_qr($event_id, $smart_list_id, $user_id){
        \Log::info('create_qr function called', [
            'smart_list_id' => $smart_list_id,
            'user_id' => $user_id
        ]);

        $getUser = Register::query()->where('event_id', $event_id)->where('user_id', $user_id)->first();

        if (!$getUser) {
            \Log::error('User not found in create_qr', ['user_id' => $user_id]);
            return;
        }

        \Log::info('User found for QR creation', [
            'user_id' => $user_id,
            'email' => $getUser->email,
            'uuid' => $getUser->uuid,
            'status' => $getUser->status
        ]);

        // QR kod için veri belirle: Manuel badge ise email, normal ise uuid
        $qrData = ($getUser->status == 3) ? $getUser->email : $getUser->uuid;

        \Log::info('QR data determined', [
            'qr_data' => $qrData,
            'is_manual' => $getUser->status == 3 ? 'yes' : 'no'
        ]);

        try {
            // SVG formatını kullan (Imagick gerektirmez)
            $svgQrCode = QrCode::format('svg')
                ->size(200)
                ->backgroundColor(255,255,255)
                ->color(0,0,0)
                ->generate($qrData);

            // SVG'yi base64 olarak sakla (PNG yerine)
            $base64 = base64_encode($svgQrCode);

        } catch (\Exception $e) {
            // Hata logla - daha detaylı
            \Log::error('QR Code generation failed', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString(),
                'user_uuid' => $getUser->uuid ?? 'unknown',
                'qr_data' => $qrData,
                'is_manual' => $getUser->status == 3 ? 'yes' : 'no'
            ]);

            // Eğer QR kod oluşturulamıyorsa, basit bir placeholder oluştur
            $placeholder = imagecreate(200, 200);
            $bg = imagecolorallocate($placeholder, 255, 255, 255);
            $text_color = imagecolorallocate($placeholder, 0, 0, 0);
            imagestring($placeholder, 5, 50, 90, 'QR Error', $text_color);

            ob_start();
            imagepng($placeholder);
            $qrCode = ob_get_contents();
            ob_end_clean();
            imagedestroy($placeholder);

            $base64 = base64_encode($qrCode);
        }

        $getUser->qr_code = $base64;
        $getUser->save();

        \Log::info('QR code saved to database', [
            'user_id' => $user_id,
            'qr_code_length' => strlen($base64)
        ]);

        // QR klasörünün var olduğundan emin ol
        $qrDir = public_path('qr');
        if (!file_exists($qrDir)) {
            mkdir($qrDir, 0755, true);
            \Log::info('Created QR directory', ['path' => $qrDir]);
        }

        // SVG dosyasını kaydet
        $qrCodeData = base64_decode($base64);
        $filePath = public_path('qr/' . $user_id . '.svg');
        $svgSaved = file_put_contents($filePath, $qrCodeData);

        \Log::info('SVG file save result', [
            'file_path' => $filePath,
            'bytes_written' => $svgSaved,
            'file_exists' => file_exists($filePath)
        ]);

        // PNG versiyonu için ayrı QR kod oluştur
        try {
            \Log::info('Starting PNG QR generation', ['user_id' => $user_id]);

            // PNG için doğrudan basit QR kod oluştur (SVG'den bağımsız)
            $pngData = $this->generateSimpleQrPng($qrData, 200);
            $pngFilePath = public_path('qr/' . $user_id . '.png');
            $pngSaved = file_put_contents($pngFilePath, $pngData);

            \Log::info('PNG QR generation completed', [
                'user_id' => $user_id,
                'file_path' => $pngFilePath,
                'bytes_written' => $pngSaved,
                'file_exists' => file_exists($pngFilePath),
                'file_size' => file_exists($pngFilePath) ? filesize($pngFilePath) : 0
            ]);
        } catch (\Exception $e) {
            \Log::warning('PNG generation failed, using placeholder', ['error' => $e->getMessage()]);
            // PNG oluşturulamazsa placeholder oluştur
            $placeholder = imagecreate(200, 200);
            $bg = imagecolorallocate($placeholder, 255, 255, 255);
            $text_color = imagecolorallocate($placeholder, 0, 0, 0);
            imagestring($placeholder, 5, 50, 90, 'QR SVG', $text_color);

            ob_start();
            imagepng($placeholder);
            $pngData = ob_get_contents();
            ob_end_clean();
            imagedestroy($placeholder);

            $pngFilePath = public_path('qr/' . $user_id . '.png');
            file_put_contents($pngFilePath, $pngData);
        }
    }

    /**
     * Basit QR kod PNG oluştur (GD kullanarak)
     */
    private function generateSimpleQrPng($data, $size)
    {
        \Log::info('generateSimpleQrPng called', [
            'data' => $data,
            'size' => $size
        ]);

        try {
            // BaconQrCode'u doğrudan kullanarak gerçek QR kod oluştur
            $encoder = new \BaconQrCode\Encoder\Encoder();
            $qrCode = $encoder->encode($data, \BaconQrCode\Common\ErrorCorrectionLevel::L());
            $matrix = $qrCode->getMatrix();

            \Log::info('QR matrix generated', [
                'matrix_width' => $matrix->getWidth(),
                'matrix_height' => $matrix->getHeight()
            ]);

            $moduleSize = max(1, floor($size / $matrix->getWidth()));
            $actualSize = $matrix->getWidth() * $moduleSize;

            // GD image oluştur
            $image = imagecreatetruecolor($actualSize, $actualSize);
            $white = imagecolorallocate($image, 255, 255, 255);
            $black = imagecolorallocate($image, 0, 0, 0);
            imagefill($image, 0, 0, $white);

            // QR matrix'i çiz
            for ($y = 0; $y < $matrix->getHeight(); $y++) {
                for ($x = 0; $x < $matrix->getWidth(); $x++) {
                    if ($matrix->get($x, $y)) {
                        $px = $x * $moduleSize;
                        $py = $y * $moduleSize;
                        imagefilledrectangle($image, $px, $py, $px + $moduleSize - 1, $py + $moduleSize - 1, $black);
                    }
                }
            }

            ob_start();
            imagepng($image);
            $pngData = ob_get_contents();
            ob_end_clean();
            imagedestroy($image);

            \Log::info('PNG data generated successfully', [
                'data_length' => strlen($pngData)
            ]);

            return $pngData;

        } catch (\Exception $e) {
            \Log::error('Direct QR generation failed', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'data' => $data,
                'size' => $size
            ]);

            // Fallback: basit pattern
            \Log::info('Using fallback QR generation');
            return $this->createFallbackQrPng($data, $size);
        }
    }

    /**
     * Fallback QR PNG oluştur
     */
    private function createFallbackQrPng($data, $size)
    {
        $image = imagecreatetruecolor($size, $size);
        $white = imagecolorallocate($image, 255, 255, 255);
        $black = imagecolorallocate($image, 0, 0, 0);
        imagefill($image, 0, 0, $white);

        // Basit QR pattern oluştur
        $cellSize = 6;
        $margin = 20;
        $gridSize = ($size - 2 * $margin) / $cellSize;

        // Data'ya göre deterministic pattern
        for ($i = 0; $i < $gridSize; $i++) {
            for ($j = 0; $j < $gridSize; $j++) {
                $hash = crc32($data . $i . $j);
                if ($hash % 3 == 0) {
                    $x = $margin + $i * $cellSize;
                    $y = $margin + $j * $cellSize;
                    imagefilledrectangle($image, $x, $y, $x + $cellSize - 1, $y + $cellSize - 1, $black);
                }
            }
        }

        // Finder patterns
        $this->drawFinderPattern($image, $margin, $margin, $black, $white, $cellSize);
        $this->drawFinderPattern($image, $size - $margin - 7 * $cellSize, $margin, $black, $white, $cellSize);
        $this->drawFinderPattern($image, $margin, $size - $margin - 7 * $cellSize, $black, $white, $cellSize);

        ob_start();
        imagepng($image);
        $pngData = ob_get_contents();
        ob_end_clean();
        imagedestroy($image);

        return $pngData;
    }

    /**
     * QR kod finder pattern çiz
     */
    private function drawFinderPattern($image, $x, $y, $black, $white, $cellSize)
    {
        // 7x7 finder pattern
        for ($i = 0; $i < 7; $i++) {
            for ($j = 0; $j < 7; $j++) {
                $color = $white;
                if ($i == 0 || $i == 6 || $j == 0 || $j == 6) {
                    $color = $black;
                } elseif ($i >= 2 && $i <= 4 && $j >= 2 && $j <= 4) {
                    $color = $black;
                }

                $px = $x + $i * $cellSize;
                $py = $y + $j * $cellSize;
                imagefilledrectangle($image, $px, $py, $px + $cellSize - 1, $py + $cellSize - 1, $color);
            }
        }
    }

    // tek seferlik tüm registerların uuid'sini emaili base64 yap
    public function updateAllUuids() {
        $registers = Register::all();
        foreach ($registers as $register) {
            $register->uuid = base64_encode($register->email);
            $register->save();
        }
    }
}
