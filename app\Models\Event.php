<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Event extends Model
{
    use HasFactory;

    protected $fillable = [
        'marketo_event_id',
        'duration_pf_event',
        'event_description',
        'event_date',
        'event_start_time',
        'event_end_time',
        'event_banner',
        'event_title',
        'event_location',
        'business_unit',
        'smart_list_id',
        'attendee_quota',
        'moderators_count',
        'speakers_count',
        'user_check_in',
        'badge_creation',
        'is_completed',
        'status',
        'contact_email',
        'attendees_info'
    ];

    public function sessions(): Has<PERSON>any
    {
        return $this-><PERSON><PERSON><PERSON>(EventSession::class, 'event_id', 'id')->orderBy('session_time_duration');;
    }

    public function documents(): <PERSON><PERSON><PERSON>
    {
        return $this-><PERSON><PERSON><PERSON>(EventDocument::class, 'event_id', 'id');
    }
    
    public function moderator_speaker(): <PERSON><PERSON><PERSON>
    {
        return $this-><PERSON><PERSON><PERSON>(EventModeratorSpearker::class, 'event_id', 'id');
    }

    public function event_dates(): <PERSON><PERSON><PERSON>
    {
        return $this-><PERSON><PERSON><PERSON>(EventDate::class, 'event_id', 'id');
    }
}
