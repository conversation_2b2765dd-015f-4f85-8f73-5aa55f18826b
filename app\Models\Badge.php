<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Badge extends Model
{
    use HasFactory;

    protected $guarded = [];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'user_id' => 'string',
    ];

    /**
     * Badge'in ait olduğu register
     */
    public function register(): BelongsTo
    {
        return $this->belongsTo(Register::class, 'user_id', 'user_id')
                    ->where('event_id', $this->event_id);
    }
}
