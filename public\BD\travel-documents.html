<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BD | Travel Documents</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
</head>
<body class="travel-documents-body">
    <!-- Header -->
    <header class="dashboard-header">
        <div class="container px-3 py-2">
            <div class="d-flex justify-content-between align-items-center">
                <!-- Left side - Logo and Navigation -->
                <div class="d-flex align-items-center">
                    <div class="d-flex align-items-center me-4">
                        <img src="assets/images/bd-logo.png" alt="BD Logo" class="logo-image me-2">
                        <h4 class="logo-text-small mb-0 d-none d-lg-block">BD</h4>
                    </div>
                    <!-- Desktop Navigation -->
                    <nav class="d-none d-lg-flex">
                        <a href="travel-documents.html" class="nav-link active me-4">Travel Documents</a>
                        <a href="dashboard.html" class="nav-link">My Events</a>
                    </nav>
                </div>

                <!-- Center - Welcome Text (Desktop only) -->
                <div class="d-none d-lg-block text-center flex-grow-1">
                    <p class="welcome-text-desktop mb-0">Welcome to your BD events dashboard</p>
                </div>

                <!-- Right side - User Profile and Menu -->
                <div class="d-flex align-items-center">
                    <!-- Desktop User Profile -->
                    <div class="d-none d-lg-flex align-items-center">
                        <div class="text-end me-3">
                            <h6 class="user-name-desktop mb-0">Dr. John Simmons</h6>
                            <small class="user-title-desktop">Title placeholder</small>
                        </div>
                        <img src="assets/images/user.png" alt="Profile" class="profile-image-desktop">
                    </div>
                    
                    <!-- Mobile Menu Button -->
                    <button class="btn btn-link p-0 text-dark d-lg-none" id="menuToggle">
                        <i class="bi bi-list fs-4"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <div class="container-fluid px-4 py-4">
        <!-- Mobile Logo (only visible on mobile) -->
        <div class="text-center mb-5 d-lg-none">
            <div class="d-flex align-items-center justify-content-center mb-3">
                <img src="assets/images/bd-logo.png" alt="BD Logo" class="logo-image">
            </div>
        </div>

        <!-- Event Selection -->
        <div class="event-selection-container">
            <h6 class="select-event-title mb-3">Please select the event</h6>
            
            <!-- Dropdown Button -->
            <div class="dropdown mb-3">
                <button class="btn event-dropdown-btn w-100 d-flex justify-content-between align-items-center" type="button" id="eventDropdown">
                    <span class="dropdown-text">Select Event</span>
                    <i class="bi bi-chevron-down"></i>
                </button>
                
                <!-- Dropdown Menu -->
                <div class="dropdown-menu w-100 event-dropdown-menu" id="dropdownMenu">
                    <div class="event-option active" data-event="APAC Hospital at Home Symposium">
                        APAC Hospital at Home Sympos..
                    </div>
                    <div class="event-option" data-event="Placeholder Text Lorem Ipsum Dolor">
                        Placeholder Text Lorem Ipsum Dolor
                    </div>
                    <div class="event-option" data-event="Placeholder Text Lorem Ipsum Dolor">
                        Placeholder Text Lorem Ipsum Dolor
                    </div>
                    <div class="event-option" data-event="Placeholder Text Lorem Ipsum Dolor">
                        Placeholder Text Lorem Ipsum Dolor
                    </div>
                    <div class="event-option" data-event="Placeholder Text Lorem Ipsum Dolor">
                        Placeholder Text Lorem Ipsum Dolor
                    </div>
                    <div class="event-option" data-event="Placeholder Text Lorem Ipsum Dolor">
                        Placeholder Text Lorem Ipsum Dolor
                    </div>
                    <div class="event-option" data-event="Placeholder Text Lorem Ipsum Dolor">
                        Placeholder Text Lorem Ipsum Dolor
                    </div>
                </div>
            </div>

            <!-- Event Details Section (Hidden by default) -->
            <div class="event-details-section d-none" id="eventDetailsSection">
                <!-- Event Image -->
                <div class="event-image-container mb-3">
                    <img src="assets/images/event.png" alt="Event" class="travel-event-image">
                </div>

                <!-- Event Info -->
                <div class="event-info mb-3">
                    <div class="event-time">TODAY | 12:00PM-04:00PM</div>
                    <div class="event-location">Auditorium, National Universi...</div>
                </div>

                <hr class="event-divider">

                <!-- Travel Documents Section -->
                <div class="travel-documents-section">
                    <h6 class="travel-section-title mb-3">My Travel Documents</h6>
                    
                    <!-- Flight Information -->
                    <div class="travel-item mb-3">
                        <div class="d-flex align-items-center">
                            <div class="travel-icon flight-icon me-3">
                                <i class="bi bi-plus-lg"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="travel-item-title mb-0">Flight Information</h6>
                                <small class="travel-item-subtitle">Placeholder Text</small>
                            </div>
                            <button class="btn travel-add-btn">
                                <i class="bi bi-plus-lg"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Hotel Information -->
                    <div class="travel-item mb-3">
                        <div class="d-flex align-items-center">
                            <div class="travel-icon hotel-icon me-3">
                                <i class="bi bi-file-text"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="travel-item-title mb-0">Hotel Information</h6>
                                <small class="travel-item-subtitle">Placeholder Text</small>
                            </div>
                            <div class="travel-actions">
                                <button class="btn travel-edit-btn">
                                    <i class="bi bi-pencil"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Passport Details -->
                    <div class="travel-item mb-3">
                        <div class="d-flex align-items-center">
                            <div class="travel-icon passport-icon me-3">
                                <i class="bi bi-file-text"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="travel-item-title mb-0">Passport Details</h6>
                                <small class="travel-item-subtitle">Placeholder Text</small>
                            </div>
                            <div class="travel-actions">
                                <button class="btn travel-edit-btn">
                                    <i class="bi bi-pencil"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Side Menu Overlay -->
    <div class="side-menu-overlay" id="sideMenuOverlay"></div>

    <!-- Side Menu -->
    <div class="side-menu" id="sideMenu">
        <div class="side-menu-header">
            <div class="side-menu-logo">
                <img src="assets/images/bd-logo.png" alt="BD Logo" class="logo-image">
            </div>
            <button class="side-menu-close" id="sideMenuClose">
                <i class="bi bi-x"></i>
            </button>
        </div>
        
        <div class="side-menu-profile">
            <img src="assets/images/user.png" alt="Profile" class="side-menu-profile-image">
            <div class="side-menu-profile-info">
                <h5>Dr. John Simmons</h5>
                <small>Title placeholder</small>
            </div>
        </div>
        
        <nav class="side-menu-nav">
            <a href="travel-documents.html" class="side-menu-item">Travel Documents</a>
            <a href="dashboard.html" class="side-menu-item">My Events</a>
        </nav>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Side menu functionality
        const menuToggle = document.querySelector('.btn-link');
        const sideMenu = document.getElementById('sideMenu');
        const sideMenuOverlay = document.getElementById('sideMenuOverlay');
        const sideMenuClose = document.getElementById('sideMenuClose');

        function openSideMenu() {
            sideMenu.classList.add('show');
            sideMenuOverlay.classList.add('show');
            document.body.style.overflow = 'hidden';
        }

        function closeSideMenu() {
            sideMenu.classList.remove('show');
            sideMenuOverlay.classList.remove('show');
            document.body.style.overflow = '';
        }

        if (menuToggle) {
            menuToggle.addEventListener('click', openSideMenu);
        }
        sideMenuClose.addEventListener('click', closeSideMenu);
        sideMenuOverlay.addEventListener('click', closeSideMenu);

        // Dropdown functionality
        document.addEventListener('DOMContentLoaded', function() {
            const dropdownBtn = document.getElementById('eventDropdown');
            const dropdownMenu = document.getElementById('dropdownMenu');
            const dropdownText = document.querySelector('.dropdown-text');
            const chevronIcon = dropdownBtn.querySelector('i');
            const eventOptions = document.querySelectorAll('.event-option');

            // Toggle dropdown
            dropdownBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                dropdownMenu.classList.toggle('show');
                
                if (dropdownMenu.classList.contains('show')) {
                    chevronIcon.classList.remove('bi-chevron-down');
                    chevronIcon.classList.add('bi-chevron-up');
                } else {
                    chevronIcon.classList.remove('bi-chevron-up');
                    chevronIcon.classList.add('bi-chevron-down');
                }
            });

            // Handle option selection
            eventOptions.forEach(option => {
                option.addEventListener('click', function() {
                    const eventName = this.getAttribute('data-event');
                    
                    // Update button text
                    dropdownText.textContent = eventName;
                    
                    // Update active state
                    eventOptions.forEach(opt => opt.classList.remove('active'));
                    this.classList.add('active');
                    
                    // Close dropdown
                    dropdownMenu.classList.remove('show');
                    chevronIcon.classList.remove('bi-chevron-up');
                    chevronIcon.classList.add('bi-chevron-down');
                    
                    // Show event details section
                    document.getElementById('eventDetailsSection').classList.remove('d-none');
                });
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!dropdownBtn.contains(e.target) && !dropdownMenu.contains(e.target)) {
                    dropdownMenu.classList.remove('show');
                    chevronIcon.classList.remove('bi-chevron-up');
                    chevronIcon.classList.add('bi-chevron-down');
                }
            });
        });
    </script>
</body>
</html>







