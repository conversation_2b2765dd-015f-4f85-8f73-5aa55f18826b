<?php

namespace App\Http\Controllers;

use App\Models\Badge;
use App\Models\BadgeTemplate;
use App\Models\Event;
use App\Models\Register;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
require_once(app_path('Libraries/fpdf.php'));

class BadgeController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function badge_creation($event_id, Register $register = null){
        $badge_templates = BadgeTemplate::query()->where('event_id', $event_id)->get();
        $event = Event::query()->find($event_id);

        if($event->badge_creation == 0){
            return redirect()->route('panel.event_detail', ['event_id' => $event_id]);
        }

        // Template 1 yoksa olu<PERSON> (sadece badge_creation sayfasında)
        if (!$badge_templates->where('name', 1)->first()) {
            BadgeTemplate::create([
                'event_id' => $event_id,
                'name' => 1,
                'bg_url' => "images/default/bg-light.png",
                'logo_url' => "images/default/bd-logo.png"
            ]);

            // Template'leri yeniden al
            $badge_templates = BadgeTemplate::query()->where('event_id', $event_id)->get();
        }

        return view('event.badge_creation', compact(['register', 'badge_templates', 'event']));
    }

    public function badge_creation_qr($event_id, Register $register){
        $badge_templates = BadgeTemplate::query()->where('event_id', $event_id)->get();
        $event = Event::query()->find($event_id);

        // Template 1 yoksa oluştur (sadece badge_creation sayfasında)
        if (!$badge_templates->where('name', 1)->first()) {
            BadgeTemplate::create([
                'event_id' => $event_id,
                'name' => 1,
                'bg_url' => "images/default/bg-light.png",
                'logo_url' => "images/default/bd-logo.png"
            ]);

            // Template'leri yeniden al
            $badge_templates = BadgeTemplate::query()->where('event_id', $event_id)->get();
        }

        return view('event.badge_creation', compact(['register', 'badge_templates', 'event']));
    }

    public function badge_list($event_id){
        $badges = Badge::query()
            ->where('event_id', $event_id)
            ->get();

        // Her badge için register bilgisini al
        foreach ($badges as $badge) {
            $register = Register::where('event_id', $event_id)
                ->where('user_id', $badge->user_id)
                ->first();
            $badge->register_status = $register ? $register->status : null;
        }

        $event = Event::find($event_id);

        return view('event.badge_list', compact('badges', 'event'));
    }

    public function badgeDelete($event_id, $id)
    {
        $badge = Badge::query()->find($id);

        if ($badge) {
            $filePath = public_path('images/badge/generated/' . $badge->badge_path);
            if (file_exists($filePath)) {
                unlink($filePath);
            }
            $badge->delete();

            return redirect()->route('panel.badge_list', ['event_id' => $event_id])->with('success', 'Badge deleted successfully');
        }

        return redirect()->route('panel.badge_list', ['event_id' => $event_id])->with('error', 'Badge not found');
    }

    public function badgeDownload($event_id, $id){
        $badge = Badge::query()->find($id);

        // Option1: PNG indir
        $file = public_path('images/badge/generated/' . $badge->badge_path);

        $headers = array(
            'Content-Type: image/png',
        );

        return response()->download($file, $badge->badge_path, $headers);

        // Option2: PDF indir
//        $pngFilePath = public_path('images/badge/generated/' . $badge->badge_path);
//
//        if (!file_exists($pngFilePath)) {
//            return abort(404, 'Badge not found.');
//        }
//
//        list($width, $height) = getimagesize($pngFilePath);
//
//        $widthInMm = $width * 25.4 / 72;
//        $heightInMm = $height * 25.4 / 72;
//
//        // Pdf sayfasının genişlik ve yüksekliği resim ile aynı
//        $pdf = new \FPDF();
//        $pdf->AddPage('P', [$widthInMm, $heightInMm]);
//
//        // PNG dosyasını PDF içine ekle
//        $pdf->Image($pngFilePath, 0, 0, $widthInMm, $heightInMm);
//
//        $badgePathWithoutPng = str_replace('.png', '', $badge->badge_path);
//        // PDF çıktısını kullanıcıya indiriyoruz
//        $pdf->Output('D', $badgePathWithoutPng . '.pdf');
    }

    public function badgePrintPage($event_id, $id)
    {
        $badge = Badge::query()->find($id);

        $file = public_path('images/badge/generated/' . $badge->badge_path);

        return view('event.badge_print', compact('badge', 'file'));
    }

        public function badgePreviewAjax($event_id, $badge_id){
        $badge = Badge::query()->find($badge_id);
        $file = public_path('images/badge/generated/' . $badge->badge_path);

        return response()->json([
            'badge' => $badge,
            'file' => $file
        ]);
    }

    public function pngBadgeGenerate($event_id, Request $request){
        $badge_bg_file = $request->badge_bg_file;
        $first_name = $request->first_name;
        $last_name = $request->last_name;
        $specialty = $request->specialty;
        $event_name = $request->event_name;
        $company = $request->company;
        $country = $request->country;
        $department = $request->department;
        $email = $request->email;
        $user_id = $request->user_id;
        $badge_qr_code = $request->upload_badge_qr_code;

        $fullname = $first_name . " " . $last_name;

        $badge_file_x_size = 575;
        $badge_file_y_size = 850;

        if($email) {
            // Badge exists control
            $badge = Badge::query()
                ->where('event_id', $event_id)
                ->where('email', $email)
                ->where('user_id', $user_id)
                ->first();
            if ($badge) {
                return redirect()->route('panel.badge_list', ['event_id' => $event_id])->with('error', 'Badge already exists');
            }
        }

        if ($badge_bg_file) {
            $destinationPath = public_path('images/badge/backgrounds');
            $badge_bg_path = $badge_bg_file->getClientOriginalName();

            if (file_exists($destinationPath . '/' . $badge_bg_path)) {
                $badge_bg_path = pathinfo($badge_bg_path, PATHINFO_FILENAME) . '_' . time() . '.' . $badge_bg_file->getClientOriginalExtension();
            }
            $badge_bg_file->move($destinationPath, $badge_bg_path);
            $badge = imagecreatefrompng(public_path('images/badge/backgrounds/' . $badge_bg_path));

            $resized_badge = imagecreatetruecolor($badge_file_x_size, $badge_file_y_size);

            // Resize the uploaded image
            imagecopyresampled($resized_badge, $badge, 0, 0, 0, 0, $badge_file_x_size, $badge_file_y_size, imagesx($badge), imagesy($badge));

            imagepng($resized_badge, public_path('images/badge/backgrounds/' . $badge_bg_path));
            imagedestroy($badge);

            $badge = $resized_badge;
        }

        /*if($department == 'Omni'){
            $badge = imagecreatefrompng(public_path('images/badge/bg_omni.png'));
        }else if($department == 'Tgs'){
            $badge = imagecreatefrompng(public_path('images/badge/bg_tgs.png'));
        }else{
            $badge = imagecreatefrompng(public_path('images/badge/bg_comex.png'));
        }*/

        $text_color_blue = imagecolorallocate($badge, 14, 89, 154);
        $text_color_orange = imagecolorallocate($badge, 240, 104, 46);

        $font = public_path('fonts/Roboto-Regular.ttf');

        $image_width = imagesx($badge);

        // Calculate the x-coordinate for the fullname
        $bbox = imagettfbbox(32, 0, $font, $fullname);
        $text_width = $bbox[2] - $bbox[0];
        $x = ($image_width - $text_width) / 2;
        imagettftext($badge, 32, 0, $x, 235, $text_color_blue, $font, $fullname);

        // Calculate the x-coordinate for the last name
        /*$bbox = imagettfbbox(42, 0, $font, $last_name);
        $text_width = $bbox[2] - $bbox[0];
        $x = ($image_width - $text_width) / 2;
        imagettftext($badge, 42, 0, $x, 450, $text_color_orange, $font, $last_name);*/

        // Calculate the x-coordinate for the Specialty
        $bbox = imagettfbbox(16, 0, $font, $specialty);
        $text_width = $bbox[2] - $bbox[0];
        $x = ($image_width - $text_width) / 2;
        imagettftext($badge, 16, 0, $x, 275, $text_color_orange, $font, $specialty);

        // Calculate the x-coordinate for the Event Name
        //        $bbox = imagettfbbox(32, 0, $font, $event_name);
        //        $text_width = $bbox[2] - $bbox[0];
        //        $x = ($image_width - $text_width) / 2;
        //        imagettftext($badge, 32, 0, $x, 370, $text_color_blue, $font, $event_name);

        // Default y-coordinates for the Company and Country
        $company_y = 445;
        $country_y = 485;

        // Calculate the x-coordinate for the Event Name
        $bbox = imagettfbbox(28, 0, $font, $event_name);
        $text_width = $bbox[2] - $bbox[0];
        $x = ($image_width - $text_width) / 2;
        // Event Name uzun ise alt satırdan devam et
        if ($text_width > 500) {
            $lines = explode("\n", wordwrap($event_name, 25, "\n"));
            $y = 360;
            $space = 42;

            $company_y += ((count($lines) - 1) * $space);
            $country_y = $company_y + 40;

            foreach ($lines as $line) {
                $bbox = imagettfbbox(32, 0, $font, $line);
                $text_width = $bbox[2] - $bbox[0];
                $x = ($image_width - $text_width) / 2;
                imagettftext($badge, 32, 0, $x, $y, $text_color_blue, $font, $line);
                $y += $space;
            }
        } else {
            imagettftext($badge, 32, 0, $x, 370, $text_color_blue, $font, $event_name);
        }

        // Calculate the x-coordinate for the Company
        $bbox = imagettfbbox(18, 0, $font, $company);
        $text_width = $bbox[2] - $bbox[0];
        $x = ($image_width - $text_width) / 2;
        imagettftext($badge, 18, 0, $x, $company_y, $text_color_orange, $font, $company);

        // Calculate the x-coordinate for the Country
        $bbox = imagettfbbox(16, 0, $font, $country);
        $text_width = $bbox[2] - $bbox[0];
        $x = ($image_width - $text_width) / 2;
        imagettftext($badge, 16, 0, $x, $country_y, $text_color_blue, $font, $country);

        $logo_path = "images/bd-logo.png";
        // Logoyu konumlandır
        $logo = imagecreatefrompng(public_path('/' . $logo_path));
        $logo_width = 200;
        $logo_height = (imagesy($logo) / imagesx($logo)) * $logo_width;

        // Resize the logo
        $resized_logo = imagecreatetruecolor($logo_width, $logo_height);
        imagealphablending($resized_logo, false);
        imagesavealpha($resized_logo, true);
        $transparent = imagecolorallocatealpha($resized_logo, 0, 0, 0, 127);
        imagefilledrectangle($resized_logo, 0, 0, $logo_width, $logo_height, $transparent);
        imagecopyresampled($resized_logo, $logo, 0, 0, 0, 0, $logo_width, $logo_height, imagesx($logo), imagesy($logo));
        imagedestroy($logo);

        $logo_x = ($badge_file_x_size - $logo_width) / 2;
        $logo_y = 50; // 50px from the top

        imagecopy($badge, $resized_logo, $logo_x, $logo_y, 0, 0, $logo_width, $logo_height);
        imagedestroy($resized_logo);
        // end logo


        // QR Code konumlandır
        if ($user_id) {
            // Decode the base64 string
            /*$qr_code_data = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $badge_qr_code));
            $qr_code_image = imagecreatefromstring($qr_code_data);*/
            public_path('qr/'.$user_id.'.png');
            $qr_code_image = imagecreatefrompng($qr_code_url);

            $qr_code_width = 230;
            $qr_code_height = (imagesy($qr_code_image) / imagesx($qr_code_image)) * $qr_code_width;

            $resized_qr_code = imagecreatetruecolor($qr_code_width, $qr_code_height);
            imagealphablending($resized_qr_code, false);
            imagesavealpha($resized_qr_code, true);
            $transparent = imagecolorallocatealpha($resized_qr_code, 0, 0, 0, 127);
            imagefilledrectangle($resized_qr_code, 0, 0, $qr_code_width, $qr_code_height, $transparent);
            imagecopyresampled($resized_qr_code, $qr_code_image, 0, 0, 0, 0, $qr_code_width, $qr_code_height, imagesx($qr_code_image), imagesy($qr_code_image));
            imagedestroy($qr_code_image);

            $qr_code_x = ($badge_file_x_size - $qr_code_width) / 2;
            $qr_code_y = $badge_file_y_size - $qr_code_height - 60; // 60px from the bottom

            imagecopy($badge, $resized_qr_code, $qr_code_x, $qr_code_y, 0, 0, $qr_code_width, $qr_code_height);
            imagedestroy($resized_qr_code);
        }
        // end QR Code

        // header('Content-type: image/png');
        // imagepng($badge);
        // imagedestroy($badge);


        $filename = Str::slug($first_name . '_' . $last_name . '_' . $country . '_' . time(), '-') . '.png';

        imagepng($badge, public_path('images/badge/generated/' . $filename));
        imagedestroy($badge);


        $badge = new Badge();
        $badge->event_id = $event_id;
        $badge->first_name = $first_name;
        $badge->last_name = $last_name;
        $badge->country = $country;
        $badge->department = $request->specialty;
        $badge->badge_path = $filename;
        $badge->email = $email;
        $badge->user_id = $user_id;
        $badge->save();

        return redirect()->route('panel.badge_list', ['event_id' => $event_id])->with('success', 'Badge generated successfully');
    }

    public function badgeTemplateSave($event_id, Request $request, $page = null)
    {
        $template_id = $request->template_id;
        $register_id = $request->register_id;
        $template_badge_bg_file = $request->template_badge_bg_file;
        $template_badge_logo_file = $request->template_badge_logo_file;

        $first_name_color = $request->first_name_color;
        $last_name_color = $request->last_name_color;
        $specialty_color = $request->specialty_color;
        $event_name_color = $request->event_name_color;
        $company_color = $request->company_color;
        $country_color = $request->country_color;

        $getTemplate = BadgeTemplate::query()
            ->where('event_id', $event_id)
            ->where('name', $template_id)
            ->first();

        if (!$getTemplate){
            BadgeTemplate::updateOrCreate(
                ['event_id' => $event_id, 'name' => 1],
                ['bg_url' => "images/default/bg-light.png", 'logo_url' => "images/default/bd-logo.png"]
            );

            BadgeTemplate::updateOrCreate(
                ['event_id' => $event_id, 'name' => 2],
                ['bg_url' => "images/default/bg-light.png", 'logo_url' => "images/default/bd-logo.png"]
            );

            BadgeTemplate::updateOrCreate(
                ['event_id' => $event_id, 'name' => 3],
                ['bg_url' => "images/default/bg-light.png", 'logo_url' => "images/default/bd-logo.png"]
            );

            $getTemplate = BadgeTemplate::query()
                ->where('event_id', $event_id)
                ->where('name', $template_id)
                ->first();
        }

        if ($template_badge_bg_file) {
            $destinationPath = public_path('images/badge/templates/backgrounds');
            $template_badge_bg_path = $template_badge_bg_file->getClientOriginalName();

            if (file_exists($destinationPath . '/' . $template_badge_bg_path)) {
                $template_badge_bg_path = pathinfo($template_badge_bg_path, PATHINFO_FILENAME) . '_' . time() . '.' . $template_badge_bg_file->getClientOriginalExtension();
            }
            $template_badge_bg_file->move($destinationPath, $template_badge_bg_path);
            $getTemplate->bg_url = 'images/badge/templates/backgrounds/' . $template_badge_bg_path;
        }

        if ($template_badge_logo_file) {
            $destinationPath = public_path('images/badge/templates/logos');
            $template_badge_logo_path = $template_badge_logo_file->getClientOriginalName();

            if (file_exists($destinationPath . '/' . $template_badge_logo_path)) {
                $template_badge_logo_path = pathinfo($template_badge_logo_path, PATHINFO_FILENAME) . '_' . time() . '.' . $template_badge_logo_file->getClientOriginalExtension();
            }
            $template_badge_logo_file->move($destinationPath, $template_badge_logo_path);
            $getTemplate->logo_url = 'images/badge/templates/logos/' . $template_badge_logo_path;
        }

        $getTemplate->firstname_color = $first_name_color;
        $getTemplate->lastname_color = $last_name_color;
        $getTemplate->specialty_color = $specialty_color;
        $getTemplate->event_name_color = $event_name_color;
        $getTemplate->company_color = $company_color;
        $getTemplate->country_color = $country_color;

        $getTemplate->save();

        if (!$page) {
            return redirect()->route('panel.badge_creation', ['register' => $register_id, 'event_id' => $event_id, 'design' => 'template'])->with('success', 'Template saved successfully');
        }
    }

    public function badgeCreateTemplate($event_id, Request $request)
    {
        $template_id = $request->template_id;
        $template_badge_bg_file = $request->template_badge_bg_file;
        $template_badge_logo_file = $request->template_badge_logo_file;
        $template_badge_qr_code = $request->template_badge_qr_code;
        $first_name = $request->first_name;
        $last_name = $request->last_name;
        $specialty = $request->specialty;
        $event_name = $request->event_name;
        $company = $request->company;
        $country = $request->country;
        $first_name_color = $request->first_name_color;
        $last_name_color = $request->last_name_color;
        $specialty_color = $request->specialty_color;
        $event_name_color = $request->event_name_color;
        $company_color = $request->company_color;
        $country_color = $request->country_color;
        $department = $request->specialty;
        $email = $request->email;
        $user_id = $request->user_id;
        $new = false;

        $fullname = $first_name . " " . $last_name;

        $badge_file_x_size = 575;
        $badge_file_y_size = 850;

        $templageDetails = BadgeTemplate::query()
            ->where('event_id', $event_id)
            ->where('name', $template_id)
            ->first();

        if(!$templageDetails){
            $this->badgeTemplateSave($event_id, $request, "new");
            $new = true;

            $templageDetails = BadgeTemplate::query()
                ->where('event_id', $event_id)
                ->where('name', $template_id)
                ->first();
        }

        if($email) {
            // Badge exists control
            $badge = Badge::query()
                ->where('event_id', $event_id)
                ->where('email', $email)
                ->where('user_id', $user_id)
                ->first();

            if ($badge) {
                $old_badge_path = public_path('images/badge/generated/' . $badge->badge_path);
                if (file_exists($old_badge_path)) {
                    unlink($old_badge_path);
                }
                $badge->delete();
            }
        }

        if ($template_badge_bg_file && !$new) {
            $destinationPath = public_path('images/badge/templates/backgrounds');
            $template_badge_bg_path = $template_badge_bg_file->getClientOriginalName();

            if (file_exists($destinationPath . '/' . $template_badge_bg_path)) {
                $template_badge_bg_path = pathinfo($template_badge_bg_path, PATHINFO_FILENAME) . '_' . time() . '.' . $template_badge_bg_file->getClientOriginalExtension();
            }
            $template_badge_bg_file->move($destinationPath, $template_badge_bg_path);
            $badge_bg_path = 'images/badge/templates/backgrounds/' . $template_badge_bg_path;
        } else {
            $badge_bg_path = $templageDetails->bg_url ?? 'images/default/bg-light.png';
        }

        $badge = imagecreatefrompng(public_path('/' . $badge_bg_path));

        $resized_badge = imagecreatetruecolor($badge_file_x_size, $badge_file_y_size);

        // Resize the uploaded image
        imagecopyresampled($resized_badge, $badge, 0, 0, 0, 0, $badge_file_x_size, $badge_file_y_size, imagesx($badge), imagesy($badge));

        imagepng($resized_badge, public_path('/' . $badge_bg_path));
        imagedestroy($badge);

        $badge = $resized_badge;

        list($r_firt_name, $g_firt_name, $b_firt_name) = sscanf($first_name_color, "#%02x%02x%02x");
        list($r_last_name, $g_last_name, $b_last_name) = sscanf($last_name_color, "#%02x%02x%02x");
        list($r_country, $g_country, $b_country) = sscanf($country_color, "#%02x%02x%02x");
        list($r_specialty, $g_specialty, $b_specialty) = sscanf($specialty_color, "#%02x%02x%02x");
        list($r_event_name, $g_event_name, $b_event_name) = sscanf($event_name_color, "#%02x%02x%02x");
        list($r_company, $g_company, $b_company) = sscanf($company_color, "#%02x%02x%02x");

        $text_color_first_name = imagecolorallocate($badge, $r_firt_name, $g_firt_name, $b_firt_name);
        $text_color_last_name = imagecolorallocate($badge, $r_last_name, $g_last_name, $b_last_name);
        $text_color_country = imagecolorallocate($badge, $r_country, $g_country, $b_country);
        $text_color_specialty = imagecolorallocate($badge, $r_specialty, $g_specialty, $b_specialty);
        $text_color_event_name = imagecolorallocate($badge, $r_event_name, $g_event_name, $b_event_name);
        $text_color_company = imagecolorallocate($badge, $r_company, $g_company, $b_company);

        $font = public_path('fonts/Roboto-Regular.ttf');

        $image_width = imagesx($badge);

        // Calculate the x-coordinate for the fullname
        $bbox = imagettfbbox(32, 0, $font, $fullname);
        $text_width = $bbox[2] - $bbox[0];
        $x = ($image_width - $text_width) / 2;
        imagettftext($badge, 32, 0, $x, 235, $text_color_first_name, $font, $fullname);

        // Calculate the x-coordinate for the last name
        /*$bbox = imagettfbbox(42, 0, $font, $last_name);
        $text_width = $bbox[2] - $bbox[0];
        $x = ($image_width - $text_width) / 2;
        imagettftext($badge, 42, 0, $x, 380, $text_color_last_name, $font, $last_name);*/

        // Calculate the x-coordinate for the Specialty
        $bbox = imagettfbbox(16, 0, $font, $specialty);
        $text_width = $bbox[2] - $bbox[0];
        $x = ($image_width - $text_width) / 2;
        imagettftext($badge, 16, 0, $x, 275, $text_color_specialty, $font, $specialty);

        // Default y-coordinates for the Company and Country
        $company_y = 445;
        $country_y = 485;

        // Calculate the x-coordinate for the Event Name
        $bbox = imagettfbbox(28, 0, $font, $event_name);
        $text_width = $bbox[2] - $bbox[0];
        $x = ($image_width - $text_width) / 2;
        // Event Name uzun ise alt satırdan devam et
        if ($text_width > 500) {
            $lines = explode("\n", wordwrap($event_name, 25, "\n"));
            $y = 360;
            $space = 42;

            $company_y += ((count($lines) - 1) * $space);
            $country_y = $company_y + 40;

            foreach ($lines as $line) {
                $bbox = imagettfbbox(32, 0, $font, $line);
                $text_width = $bbox[2] - $bbox[0];
                $x = ($image_width - $text_width) / 2;
                imagettftext($badge, 32, 0, $x, $y, $text_color_event_name, $font, $line);
                $y += $space;
            }
        } else {
            imagettftext($badge, 32, 0, $x, 370, $text_color_event_name, $font, $event_name);
        }

        // Calculate the x-coordinate for the Company
        $bbox = imagettfbbox(18, 0, $font, $company);
        $text_width = $bbox[2] - $bbox[0];
        $x = ($image_width - $text_width) / 2;
        imagettftext($badge, 18, 0, $x, $company_y, $text_color_company, $font, $company);

        // Calculate the x-coordinate for the Country
        $bbox = imagettfbbox(16, 0, $font, $country);
        $text_width = $bbox[2] - $bbox[0];
        $x = ($image_width - $text_width) / 2;
        imagettftext($badge, 16, 0, $x, $country_y, $text_color_country, $font, $country);

        // header('Content-type: image/png');
        // imagepng($badge);
        // imagedestroy($badge);

        // Logoyu konumlandır
        if ($template_badge_logo_file  && !$new) {
            $destinationPath = public_path('images/badge/templates/logos');
            $template_badge_logo_path = $template_badge_logo_file->getClientOriginalName();

            if (file_exists($destinationPath . '/' . $template_badge_logo_path)) {
                $template_badge_logo_path = pathinfo($template_badge_logo_path, PATHINFO_FILENAME) . '_' . time() . '.' . $template_badge_logo_file->getClientOriginalExtension();
            }
            $template_badge_logo_file->move($destinationPath, $template_badge_logo_path);
            $logo_path = 'images/badge/templates/logos/' . $template_badge_logo_path;
        } else {
            $logo_path = $templageDetails->logo_url;
        }

        $logo = imagecreatefrompng(public_path('/' . $logo_path));

        $logo_width = 260;
        $logo_height = (imagesy($logo) / imagesx($logo)) * $logo_width;
        if ($logo_height > 140) {
            $logo_height = 140;
            $logo_width = (imagesx($logo) / imagesy($logo)) * $logo_height;
        }
        // Resize the logo
        $resized_logo = imagecreatetruecolor($logo_width, $logo_height);
        imagealphablending($resized_logo, false);
        imagesavealpha($resized_logo, true);
        $transparent = imagecolorallocatealpha($resized_logo, 0, 0, 0, 127);
        imagefilledrectangle($resized_logo, 0, 0, $logo_width, $logo_height, $transparent);
        imagecopyresampled($resized_logo, $logo, 0, 0, 0, 0, $logo_width, $logo_height, imagesx($logo), imagesy($logo));
        imagedestroy($logo);

        $logo_x = ($badge_file_x_size - $logo_width) / 2;
        $logo_y = 50; // 50px from the top

        imagecopy($badge, $resized_logo, $logo_x, $logo_y, 0, 0, $logo_width, $logo_height);
        imagedestroy($resized_logo);
        // end logo

        // QR Code konumlandır
        if ($user_id) {
            // Decode the base64 string
            /*$qr_code_data = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $template_badge_qr_code));
            $qr_code_image = imagecreatefromstring($qr_code_data);*/
            $qr_code_url = public_path('qr/'.$user_id.'.png');
            $qr_code_image = imagecreatefrompng($qr_code_url);

            $qr_code_width = 230;
            $qr_code_height = (imagesy($qr_code_image) / imagesx($qr_code_image)) * $qr_code_width;

            $resized_qr_code = imagecreatetruecolor($qr_code_width, $qr_code_height);
            imagealphablending($resized_qr_code, false);
            imagesavealpha($resized_qr_code, true);
            $transparent = imagecolorallocatealpha($resized_qr_code, 0, 0, 0, 127);
            imagefilledrectangle($resized_qr_code, 0, 0, $qr_code_width, $qr_code_height, $transparent);
            imagecopyresampled($resized_qr_code, $qr_code_image, 0, 0, 0, 0, $qr_code_width, $qr_code_height, imagesx($qr_code_image), imagesy($qr_code_image));
            imagedestroy($qr_code_image);

            $qr_code_x = ($badge_file_x_size - $qr_code_width) / 2;
            $qr_code_y = $badge_file_y_size - $qr_code_height - 60; // 60px from the bottom

            imagecopy($badge, $resized_qr_code, $qr_code_x, $qr_code_y, 0, 0, $qr_code_width, $qr_code_height);
            imagedestroy($resized_qr_code);
        }
        // end QR Code

        $filename = Str::slug($first_name . '_' . $last_name . '_' . $country . '_' . time(), '-') . '.png';

        imagepng($badge, public_path('images/badge/generated/' . $filename));
        imagedestroy($badge);


        $badge = new Badge();
        $badge->event_id = $event_id;
        $badge->first_name = $first_name;
        $badge->last_name = $last_name;
        $badge->country = $country;
        $badge->department = $department;
        $badge->badge_path = $filename;
        $badge->email = $email;
        $badge->user_id = $user_id;
        $badge->save();

        return redirect()->route('panel.badge_list', ['event_id' => $event_id])->with('success', 'Badge generated successfully');

    }

    /**
     * Event'teki tüm register'lar için toplu badge oluştur
     */
    public function generateBulkBadges($event_id, Request $request)
    {
        $event = Event::findOrFail($event_id);

        // Badge template kontrolü - otomatik oluşturma YOK
        $template = BadgeTemplate::where('event_id', $event_id)->where('name', 1)->first();

        if (!$template) {
            return redirect()->back()->with('error', 'Badge template not found. Please create a template first by going to Badge Creation page.');
        }

        // Template dosyalarının varlığını kontrol et
        $bg_path = public_path('/' . $template->bg_url);
        if (!file_exists($bg_path)) {
            return redirect()->back()->with('error', 'Template background image not found. Please upload a background image first.');
        }

        // Sadece status=1 olan (shortlist'teki) register'ları al
        $registers = Register::where('event_id', $event_id)
            ->where('status', 1)
            ->get();

        if ($registers->isEmpty()) {
            return redirect()->back()->with('error', 'No users found in shortlist to generate badges.');
        }

        // Template ID'yi request'ten al, yoksa varsayılan template kullan
        $template_id = $request->template_id ?? 1;

        $successCount = 0;
        $errorCount = 0;
        $errors = [];

        foreach ($registers as $register) {
            try {
                // Zaten badge var mı kontrol et
                $existingBadge = Badge::where('event_id', $event_id)
                    ->where('email', $register->email)
                    ->where('user_id', $register->user_id)
                    ->first();

                if ($existingBadge) {
                    $old_badge_path = public_path('images/badge/generated/' . $existingBadge->badge_path);
                    if (file_exists($old_badge_path)) {
                        unlink($old_badge_path);
                    }
                    $existingBadge->delete();
                }

                // Badge oluştur
                $this->createSingleBadge($event_id, $register, $template_id);
                $successCount++;

            } catch (\Exception $e) {
                $errors[] = "Error creating badge for {$register->first_name} {$register->last_name}: " . $e->getMessage();
                $errorCount++;
                \Log::error('Bulk badge generation error', [
                    'register_id' => $register->id,
                    'error' => $e->getMessage()
                ]);
            }
        }

        $message = "Bulk badge generation completed. Success: {$successCount}, Errors: {$errorCount}";

        if (!empty($errors)) {
            $message .= "\nErrors: " . implode(', ', array_slice($errors, 0, 3));
            if (count($errors) > 3) {
                $message .= " and " . (count($errors) - 3) . " more...";
            }
        }

        return redirect()->route('panel.badge_list', ['event_id' => $event_id])
            ->with($errorCount > 0 ? 'warning' : 'success', $message);
    }

    /**
     * Badge düzenleme sayfası
     */
    public function badgeEdit($event_id, $id)
    {
        $badge = Badge::findOrFail($id);
        $event = Event::findOrFail($event_id);

        // Badge'e ait register bilgisini al
        $register = Register::where('event_id', $event_id)
            ->where('user_id', $badge->user_id)
            ->first();

        // Badge template'ini al
        $badge_templates = BadgeTemplate::where('event_id', $event_id)->get();

        // Template 1 yoksa oluştur
        if (!$badge_templates->where('name', 1)->first()) {
            BadgeTemplate::create([
                'event_id' => $event_id,
                'name' => 1,
                'bg_url' => "images/default/bg-light.png",
                'logo_url' => "images/default/bd-logo.png"
            ]);

            $badge_templates = BadgeTemplate::where('event_id', $event_id)->get();
        }

        return view('event.badge_edit', compact(['badge', 'register', 'badge_templates', 'event']));
    }

    /**
     * Badge güncelleme
     */
    public function badgeUpdate($event_id, $id, Request $request)
    {
        $badge = Badge::findOrFail($id);

        $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'specialty' => 'nullable|string|max:255',
            'department' => 'nullable|string|max:255',
            'company' => 'nullable|string|max:255',
            'country' => 'nullable|string|max:255',
            'event_name' => 'required|string|max:255',
            'template_id' => 'required|integer',
            'badge_bg_file' => 'nullable|image|mimes:png,jpg,jpeg|max:2048',
            'badge_logo_file' => 'nullable|image|mimes:png,jpg,jpeg|max:2048',
        ]);

        try {
            // Template'i al veya oluştur
            $template = BadgeTemplate::where('event_id', $event_id)->where('name', $request->template_id)->first();
            if (!$template) {
                $template = BadgeTemplate::create([
                    'event_id' => $event_id,
                    'name' => $request->template_id,
                    'bg_url' => "images/default/bg-light.png",
                    'logo_url' => "images/default/bd-logo.png"
                ]);
            }

            // Templates klasörünü oluştur
            if (!file_exists(public_path('images/templates'))) {
                mkdir(public_path('images/badge/templates'), 0755, true);
            }

            // Background dosyası upload edilmişse
            if ($request->hasFile('badge_bg_file')) {
                $bgFile = $request->file('badge_bg_file');
                $bgFileName = 'bg_' . $event_id . '_' . time() . '.' . $bgFile->getClientOriginalExtension();
                $bgFile->move(public_path('images/badge/templates/backgrounds/'), $bgFileName);
                $template->update(['bg_url' => 'images/badge/templates/backgrounds/' . $bgFileName]);
            }

            // Logo dosyası upload edilmişse
            if ($request->hasFile('badge_logo_file')) {
                $logoFile = $request->file('badge_logo_file');
                $logoFileName = 'logo_' . $event_id . '_' . time() . '.' . $logoFile->getClientOriginalExtension();
                $logoFile->move(public_path('images/badge/templates/logos/'), $logoFileName);
                $template->update(['logo_url' => 'images/badge/templates/logos/' . $logoFileName]);
            }

            // Badge bilgilerini güncelle
            $badge->update([
                'first_name' => $request->first_name,
                'last_name' => $request->last_name,
                'specialty' => $request->specialty,
                'department' => $request->department,
                'company' => $request->company,
                'country' => $request->country,
                'event_name' => $request->event_name,
            ]);

            // Badge dosyasını yeniden oluştur
            $new_filename = $this->updateBadgeFile($badge, $event_id, $request->template_id);

            return redirect()->route('panel.badge_list', ['event_id' => $event_id])
                ->with('success', 'Badge updated successfully. New badge file: ' . $new_filename);

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to update badge: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Badge dosyasını güncelle
     */
    private function updateBadgeFile($badge, $event_id, $template_id = 1)
    {
        // Template bilgilerini al
        $template = BadgeTemplate::where('event_id', $event_id)
            ->where('name', $template_id)
            ->first();

        if (!$template) {
            throw new \Exception("Badge template not found for event {$event_id}");
        }

        // Template dosyalarının varlığını kontrol et
        $bg_path = public_path('/' . $template->bg_url);
        if (!file_exists($bg_path)) {
            throw new \Exception("Background image not found: {$template->bg_url}");
        }

        // Eski badge dosyasını sil
        if ($badge->badge_path) {
            $old_badge_path = public_path('images/badge/generated/' . $badge->badge_path);
            if (file_exists($old_badge_path)) {
                unlink($old_badge_path);
            }
        }

        // Badge dosya adını oluştur (createSingleBadge ile aynı format)
        //$filename = Str::slug($badge->first_name . '_' . $badge->last_name . '_' . ($badge->country ?? 'unknown') . '_' . time(), '-') . '.png';
        $filename = $badge->badge_path;
        $badge_path = public_path('images/badge/generated/' . $filename);

        // Generated klasörünü oluştur
        if (!file_exists(public_path('images/badge/generated'))) {
            mkdir(public_path('images/badge/generated'), 0755, true);
        }

        // Badge oluştur (createSingleBadge metodundaki mantığı kullan)
        $register_data = (object)[
            'first_name' => $badge->first_name,
            'last_name' => $badge->last_name,
            'specialty' => $badge->specialty,
            'department' => $badge->department,
            'title' => $badge->department,
            'company' => $badge->company,
            'country' => $badge->country ?? '  ',
            'event_name' => $badge->event_name,
            'email' => $badge->email,
            'user_id' => $badge->user_id,
        ];

        // Delete old badge
        $badge->delete();

        // Badge oluşturma işlemi (mevcut createSingleBadge mantığını kullan)
        $this->createSingleBadge($event_id, $register_data);

        return $filename;
    }

    /**
     * Badge image oluştur (createSingleBadge ile aynı boyutlar)
     */
    private function generateBadgeImage($register, $template, $output_path)
    {
        try {
            // createSingleBadge ile aynı boyutlar
            $badge_file_x_size = 575;
            $badge_file_y_size = 850;

            // Background image yükle
            $bg_path = public_path('/' . $template->bg_url);

            // Farklı image formatlarını dene
            $background = null;
            $image_info = @getimagesize($bg_path);

            if ($image_info) {
                switch ($image_info[2]) {
                    case IMAGETYPE_PNG:
                        $background = @imagecreatefrompng($bg_path);
                        break;
                    case IMAGETYPE_JPEG:
                        $background = @imagecreatefromjpeg($bg_path);
                        break;
                    case IMAGETYPE_GIF:
                        $background = @imagecreatefromgif($bg_path);
                        break;
                }
            }

            if (!$background) {
                // Varsayılan background oluştur (createSingleBadge boyutları)
                $background = imagecreatetruecolor($badge_file_x_size, $badge_file_y_size);
                $white = imagecolorallocate($background, 255, 255, 255);
                imagefill($background, 0, 0, $white);
            } else {
                // Background'u createSingleBadge boyutlarına resize et
                $resized_background = imagecreatetruecolor($badge_file_x_size, $badge_file_y_size);
                imagecopyresampled($resized_background, $background, 0, 0, 0, 0,
                                 $badge_file_x_size, $badge_file_y_size, imagesx($background), imagesy($background));
                imagedestroy($background);
                $background = $resized_background;
            }

            // Logo ekle (varsa)
            $logo_path = public_path('/' . $template->logo_url);
            if (file_exists($logo_path)) {
                $logo_info = @getimagesize($logo_path);
                $logo = null;

                if ($logo_info) {
                    switch ($logo_info[2]) {
                        case IMAGETYPE_PNG:
                            $logo = @imagecreatefrompng($logo_path);
                            break;
                        case IMAGETYPE_JPEG:
                            $logo = @imagecreatefromjpeg($logo_path);
                            break;
                        case IMAGETYPE_GIF:
                            $logo = @imagecreatefromgif($logo_path);
                            break;
                    }
                }

                if ($logo) {
                    // createSingleBadge ile aynı logo boyutları
                    $image_width = imagesx($background);
                    $logo_width = 200;
                    $logo_height = (imagesy($logo) / imagesx($logo)) * $logo_width;
                    $logo_x = ($image_width - $logo_width) / 2;
                    $logo_y = 50;

                    $resized_logo = imagecreatetruecolor($logo_width, $logo_height);
                    imagecopyresampled($resized_logo, $logo, 0, 0, 0, 0,
                                     $logo_width, $logo_height, imagesx($logo), imagesy($logo));
                    imagecopy($background, $resized_logo, $logo_x, $logo_y, 0, 0, $logo_width, $logo_height);
                    imagedestroy($resized_logo);
                    imagedestroy($logo);
                }
            }

            // Metin ekle
            $this->addTextToBadge($background, $register, $template);

            // QR kod ekle
            $this->addQRCodeToBadge($background, $register);

            // Badge'i kaydet
            imagepng($background, $output_path);
            imagedestroy($background);

        } catch (Exception $e) {
            throw new \Exception("Failed to generate badge image: " . $e->getMessage());
        }
    }

    /**
     * Badge'e metin ekle (createSingleBadge ile uyumlu)
     */
    private function addTextToBadge($image, $register, $template)
    {
        // createSingleBadge ile aynı renkler
        $text_color_blue = imagecolorallocate($image, 14, 89, 154);
        $text_color_orange = imagecolorallocate($image, 240, 104, 46);
        $image_width = imagesx($image);

        // Font dosyası yolu (createSingleBadge ile aynı)
        $font = public_path('fonts/Roboto-Regular.ttf');
        $use_ttf = file_exists($font);

        // İsim (createSingleBadge ile aynı)
        $full_name = trim($register->first_name . ' ' . $register->last_name);
        if ($full_name) {
            if ($use_ttf) {
                $bbox = imagettfbbox(32, 0, $font, $full_name);
                $text_width = $bbox[2] - $bbox[0];
                $x = ($image_width - $text_width) / 2;
                imagettftext($image, 32, 0, $x, 235, $text_color_blue, $font, $full_name);
            } else {
                $this->addSimpleTextCentered($image, $full_name, 235, 5, $text_color_blue);
            }
        }

        // Company (createSingleBadge ile aynı)
        if (!empty($register->company)) {
            if ($use_ttf) {
                $bbox = imagettfbbox(18, 0, $font, $register->company);
                $text_width = $bbox[2] - $bbox[0];
                $x = ($image_width - $text_width) / 2;
                imagettftext($image, 18, 0, $x, 280, $text_color_orange, $font, $register->company);
            } else {
                $this->addSimpleTextCentered($image, $register->company, 280, 4, $text_color_orange);
            }
        }

        // Country (createSingleBadge ile aynı)
        if (!empty($register->country)) {
            if ($use_ttf) {
                $bbox = imagettfbbox(16, 0, $font, $register->country);
                $text_width = $bbox[2] - $bbox[0];
                $x = ($image_width - $text_width) / 2;
                imagettftext($image, 16, 0, $x, 310, $text_color_blue, $font, $register->country);
            } else {
                $this->addSimpleTextCentered($image, $register->country, 310, 3, $text_color_blue);
            }
        }

        // Ek alanlar (department, specialty, event_name) basit font ile
        if (!empty($register->department)) {
            $bbox = imagettfbbox(18, 0, $font, $register->department);
            $text_width = $bbox[2] - $bbox[0];
            $x = ($image_width - $text_width) / 2;
            imagettftext($image, 18, 0, $x, 340, $text_color_orange, $font, $register->department);
        }

        if (!empty($register->specialty)) {
            $bbox = imagettfbbox(18, 0, $font, $register->specialty);
            $text_width = $bbox[2] - $bbox[0];
            $x = ($image_width - $text_width) / 2;
            imagettftext($image, 18, 0, $x, 375, $text_color_orange, $font, $register->specialty);
        }

        if (!empty($register->event_name)) {
            $bbox = imagettfbbox(20, 0, $font, $register->event_name);
            $text_width = $bbox[2] - $bbox[0];
            $x = ($image_width - $text_width) / 2;
            imagettftext($image, 20, 0, $x, 450, $text_color_orange, $font, $register->event_name);
        }
    }

    /**
     * Basit ortalanmış metin ekle
     */
    private function addSimpleTextCentered($image, $text, $y, $font_size, $color)
    {
        $image_width = imagesx($image);

        // Font boyutuna göre karakter genişliği tahmin et
        $char_width = $font_size * 2.5; // imagestring için daha iyi tahmin
        $text_width = strlen($text) * $char_width;
        $x = max(0, ($image_width - $text_width) / 2);

        // Basit imagestring kullan (font_size 1-5 arası)
        imagestring($image, min(max($font_size, 1), 5), $x, $y, $text, $color);
    }



    /**
     * QR kod ekle (createSingleBadge ile aynı format)
     */
    private function addQRCodeToBadge($image, $register)
    {
        if (!$register->user_id) {
            return;
        }

        $qr_path = public_path('qr/' . $register->user_id . '.png');

        if (file_exists($qr_path)) {
            $qr_image = @imagecreatefrompng($qr_path);
            if ($qr_image) {
                $image_width = imagesx($image);

                // createSingleBadge ile aynı boyutlar
                $qr_code_width = 230;
                $qr_code_height = (imagesy($qr_image) / imagesx($qr_image)) * $qr_code_width;
                $qr_code_x = ($image_width - $qr_code_width) / 2; // Ortalanmış
                $qr_code_y = 600; // Alt kısım

                // QR kod boyutlarını kontrol et
                $qr_width = imagesx($qr_image);
                $qr_height = imagesy($qr_image);

                if ($qr_width > 0 && $qr_height > 0) {
                    // Resized QR code oluştur
                    $resized_qr_code = imagecreatetruecolor($qr_code_width, $qr_code_height);
                    imagecopyresampled($resized_qr_code, $qr_image, 0, 0, 0, 0,
                                     $qr_code_width, $qr_code_height, $qr_width, $qr_height);
                    imagecopy($image, $resized_qr_code, $qr_code_x, $qr_code_y, 0, 0, $qr_code_width, $qr_code_height);
                    imagedestroy($resized_qr_code);
                }
                imagedestroy($qr_image);
            }
        }
    }

    /**
     * Badge template varlığını kontrol et (AJAX)
     */
    public function checkBadgeTemplate($event_id)
    {
        $template = BadgeTemplate::where('event_id', $event_id)
            ->where('name', 1)
            ->first();

        $has_template = false;
        $template_info = null;
        $message = 'Template not found';

        if ($template) {
            // Template dosyalarının varlığını kontrol et
            $bg_exists = file_exists(public_path('/' . $template->bg_url));
            $logo_exists = file_exists(public_path('/' . $template->logo_url));

            // Hem template hem de background dosyası olmalı
            $has_template = $bg_exists;

            if (!$bg_exists) {
                $message = 'Template background image not found. Please upload a background image.';
            } else {
                $message = 'Template is ready';
            }

            $template_info = [
                'id' => $template->id,
                'name' => $template->name,
                'bg_url' => $template->bg_url,
                'logo_url' => $template->logo_url,
                'bg_exists' => $bg_exists,
                'logo_exists' => $logo_exists
            ];
        } else {
            $message = 'Badge template not found. Please create a template first.';
        }

        return response()->json([
            'has_template' => $has_template,
            'template_info' => $template_info,
            'message' => $message
        ]);
    }

    /**
     * Tek bir register için badge oluştur
     */
    private function createSingleBadge($event_id, $register, $template_id = 1)
    {
        // Template bilgilerini al - otomatik oluşturma YOK
        $template = BadgeTemplate::where('event_id', $event_id)
            ->where('name', $template_id)
            ->first();

        $event = Event::find($event_id);

        // Template yoksa hata fırlat
        if (!$template) {
            throw new \Exception("Badge template (ID: {$template_id}) not found for event {$event_id}. Please create a template first by going to Badge Creation page.");
        }

        // Template dosyalarının varlığını kontrol et
        $bg_path = public_path('/' . $template->bg_url);
        $logo_path = public_path('/' . $template->logo_url);

        if (!file_exists($bg_path)) {
            throw new \Exception("Background image not found: {$template->bg_url}. Please upload a background image first.");
        }

        $event = Event::find($event_id);
        $fullname = $register->first_name . " " . $register->last_name;

        $badge_file_x_size = 575;
        $badge_file_y_size = 850;

        // Badge arka planını yükle
        $badge_bg_path = $template->bg_url ?? 'images/default/bg-light.png';
        $badge = imagecreatefrompng(public_path('/' . $badge_bg_path));

        $resized_badge = imagecreatetruecolor($badge_file_x_size, $badge_file_y_size);
        imagecopyresampled($resized_badge, $badge, 0, 0, 0, 0, $badge_file_x_size, $badge_file_y_size, imagesx($badge), imagesy($badge));
        imagedestroy($badge);
        $badge = $resized_badge;

        // Renkler
        list($r_firt_name, $g_firt_name, $b_firt_name) = sscanf($template->firstname_color, "#%02x%02x%02x");
        list($r_last_name, $g_last_name, $b_last_name) = sscanf($template->lastname_color, "#%02x%02x%02x");
        list($r_country, $g_country, $b_country) = sscanf($template->country_color, "#%02x%02x%02x");
        list($r_specialty, $g_specialty, $b_specialty) = sscanf($template->specialty_color, "#%02x%02x%02x");
        list($r_event_name, $g_event_name, $b_event_name) = sscanf($template->event_name_color, "#%02x%02x%02x");
        list($r_company, $g_company, $b_company) = sscanf($template->company_color, "#%02x%02x%02x");

        $text_color_blue = imagecolorallocate($badge, 14, 89, 154);
        $text_color_orange = imagecolorallocate($badge, 240, 104, 46);
        $font = public_path('fonts/Roboto-Regular.ttf');
        $image_width = imagesx($badge);

        // İsim
        $bbox = imagettfbbox(32, 0, $font, $fullname);
        $text_width = $bbox[2] - $bbox[0];
        $x = ($image_width - $text_width) / 2;
        $color_fullname = imagecolorallocate($badge, $r_firt_name, $g_firt_name, $b_firt_name);
        imagettftext($badge, 32, 0, $x, 235, $color_fullname, $font, $fullname);

        // Şirket
        if ($register->company) {
            $bbox = imagettfbbox(18, 0, $font, $register->company);
            $text_width = $bbox[2] - $bbox[0];
            $x = ($image_width - $text_width) / 2;
            $color_company = imagecolorallocate($badge, $r_company, $g_company, $b_company);
            imagettftext($badge, 18, 0, $x, 280, $color_company, $font, $register->company);
        }

        // Ülke
        if ($register->country) {
            $bbox = imagettfbbox(16, 0, $font, $register->country);
            $text_width = $bbox[2] - $bbox[0];
            $x = ($image_width - $text_width) / 2;
            $color_country = imagecolorallocate($badge, $r_country, $g_country, $b_country);
            imagettftext($badge, 16, 0, $x, 310, $color_country, $font, $register->country);
        }

        // Ek alanlar (department, specialty, event_name) basit font ile
        if (!empty($register->department)) {
            $bbox = imagettfbbox(16, 0, $font, $register->department);
            $text_width = $bbox[2] - $bbox[0];
            $x = ($image_width - $text_width) / 2;
            $color_department = imagecolorallocate($badge, $r_specialty, $g_specialty, $b_specialty);
            imagettftext($badge, 16, 0, $x, 340, $color_department, $font, $register->department);
        }

        if (!empty($register->specialty)) {
            $bbox = imagettfbbox(16, 0, $font, $register->specialty);
            $text_width = $bbox[2] - $bbox[0];
            $x = ($image_width - $text_width) / 2;
            $color_specialty = imagecolorallocate($badge, $r_specialty, $g_specialty, $b_specialty);
            imagettftext($badge, 16, 0, $x, 375, $color_specialty, $font, $register->specialty);
        }

        if (!empty($event->event_title)) {
            $bbox = imagettfbbox(20, 0, $font, $event->event_title);
            $text_width = $bbox[2] - $bbox[0];
            $x = ($image_width - $text_width) / 2;
            $color_event_name = imagecolorallocate($badge, $r_event_name, $g_event_name, $b_event_name);
            imagettftext($badge, 20, 0, $x, 450, $color_event_name, $font, $event->event_title);
        }

        // Logo
        $logo_path = $template->logo_url ?? "images/default/bd-logo.png";
        if (file_exists(public_path('/' . $logo_path))) {
            $logo = imagecreatefrompng(public_path('/' . $logo_path));

            $logo_width = 260;
            $logo_height = (imagesy($logo) / imagesx($logo)) * $logo_width;
            if ($logo_height > 120) {
                $logo_height = 120;
                $logo_width = (imagesx($logo) / imagesy($logo)) * $logo_height;
            }

            // Resize the logo
            $resized_logo = imagecreatetruecolor($logo_width, $logo_height);
            imagealphablending($resized_logo, false);
            imagesavealpha($resized_logo, true);
            $transparent = imagecolorallocatealpha($resized_logo, 0, 0, 0, 127);
            imagefilledrectangle($resized_logo, 0, 0, $logo_width, $logo_height, $transparent);
            imagecopyresampled($resized_logo, $logo, 0, 0, 0, 0, $logo_width, $logo_height, imagesx($logo), imagesy($logo));
            imagedestroy($logo);

            $logo_x = ($badge_file_x_size - $logo_width) / 2;
            $logo_y = 50; // 50px from the top

            imagecopy($badge, $resized_logo, $logo_x, $logo_y, 0, 0, $logo_width, $logo_height);
            imagedestroy($resized_logo);
        }

        // QR Code
        if ($register->user_id) {
            $qr_code_url = public_path('qr/' . $register->user_id . '.png');
            if (file_exists($qr_code_url)) {
                $qr_code_image = imagecreatefrompng($qr_code_url);
                $qr_code_width = 230;
                $qr_code_height = (imagesy($qr_code_image) / imagesx($qr_code_image)) * $qr_code_width;
                $qr_code_x = ($image_width - $qr_code_width) / 2;
                $qr_code_y = 600;

                $resized_qr_code = imagecreatetruecolor($qr_code_width, $qr_code_height);
                imagecopyresampled($resized_qr_code, $qr_code_image, 0, 0, 0, 0, $qr_code_width, $qr_code_height, imagesx($qr_code_image), imagesy($qr_code_image));
                imagecopy($badge, $resized_qr_code, $qr_code_x, $qr_code_y, 0, 0, $qr_code_width, $qr_code_height);
                imagedestroy($resized_qr_code);
                imagedestroy($qr_code_image);
            }
        }

        // Dosyayı kaydet
        $filename = Str::slug($register->first_name . '_' . $register->last_name . '_' . ($badge->country ?? 'unknown') . '_' . time(), '-') . '.png';
        imagepng($badge, public_path('images/badge/generated/' . $filename));
        imagedestroy($badge);

        // Veritabanına kaydet
        $badgeModel = new Badge();
        $badgeModel->event_id = $event_id;
        $badgeModel->first_name = $register->first_name;
        $badgeModel->last_name = $register->last_name;
        $badgeModel->country = $register->country;
        $badgeModel->company = $register->company;
        $badgeModel->department = $register->title;
        $badgeModel->badge_path = $filename;
        $badgeModel->email = $register->email;
        $badgeModel->user_id = $register->user_id;
        $badgeModel->save();

        return $badgeModel;
    }



    /**
     * Manuel badge oluşturma sayfası
     */
    public function badge_creation_manual($event_id)
    {
        $badge_templates = BadgeTemplate::query()->where('event_id', $event_id)->get();
        $event = Event::query()->find($event_id);

        if($event->badge_creation == 0){
            return redirect()->route('panel.event_detail', ['event_id' => $event_id]);
        }

        // Template kontrolü - manuel badge için template gerekli
        if (!$badge_templates->where('name', 1)->first()) {
            return redirect()->route('panel.badge_creation', ['event_id' => $event_id])
                ->with('error', 'Please create a badge template first before creating manual badges.');
        }

        return view('event.badge_creation_manual', compact(['badge_templates', 'event']));
    }

    /**
     * Manuel badge oluştur ve register'a kaydet
     */
    public function createManualBadge($event_id, Request $request)
    {
        // Validation
        $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'company' => 'nullable|string|max:255',
            'country' => 'nullable|string|max:255',
            'title' => 'nullable|string|max:255',
            'phone' => 'nullable|string|max:255',
        ]);

        $event = Event::findOrFail($event_id);

        // Email kontrolü - aynı event'te aynı email var mı?
        $existingBadge = Badge::where('event_id', $event_id)
            ->where('email', $request->email)
            ->first();

        if ($existingBadge) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'This email already has a badge for this event.');
        }

        try {
            // Unique user_id oluştur
            $user_id = 'MANUAL_' . time() . '_' . rand(1000, 9999);

            // Register object oluştur (sadece badge oluşturma için)
            $register_data = (object)[
                'first_name' => $request->first_name,
                'last_name' => $request->last_name,
                'specialty' => $request->specialty,
                'department' => $request->title, // title'ı department olarak kullan
                'company' => $request->company,
                'country' => $request->country,
                'event_name' => $event->name,
                'email' => $request->email,
                'user_id' => $user_id,
                'status' => 3,
            ];

            $newRegister = new Register();
            $newRegister->uuid = base64_encode($request->email);
            $newRegister->event_id = $event_id;
            $newRegister->user_id = $user_id;
            $newRegister->first_name = $request->first_name;
            $newRegister->last_name = $request->last_name;
            $newRegister->email = $request->email;
            $newRegister->company = $request->company;
            $newRegister->country = $request->country;
            $newRegister->specialty = $request->specialty;
            $newRegister->title = $request->title;
            $newRegister->phone = $request->phone;
            $newRegister->status = 1;
            $newRegister->check_in = 0;
            $newRegister->save();

            // QR kod oluştur (manuel badge için direkt oluştur)
            //$this->createManualQrCode($user_id, $request->email);
            // RegisterControllerdaki create_qr fonksiyonunu kullan
            $registerController = new RegisterController();
            $registerController->create_qr($event_id,0, $user_id);

            // Template kontrolü
            $template = BadgeTemplate::where('event_id', $event_id)->where('name', $request->template_id ?? 1)->first();
            if (!$template) {
                throw new \Exception("Badge template not found for event {$event_id}");
            }

            // Badge dosya adını oluştur
            $filename = Str::slug($request->first_name . '_' . $request->last_name . '_' . ($request->country ?? 'manual') . '_' . time(), '-') . '.png';
            $badge_path = public_path('images/badge/generated/' . $filename);

            // Generated klasörünü oluştur
            if (!file_exists(public_path('images/badge/generated'))) {
                mkdir(public_path('images/badge/generated'), 0755, true);
            }


            // Badge image oluştur
            $this->createSingleBadge($event_id, $newRegister);

            return redirect()->route('panel.badge_list', ['event_id' => $event_id])
                ->with('success', "Manual badge created successfully for {$request->first_name} {$request->last_name}");

        } catch (\Exception $e) {
            \Log::error('Manual badge creation failed', [
                'event_id' => $event_id,
                'email' => $request->email,
                'error' => $e->getMessage()
            ]);

            return redirect()->back()
                ->withInput()
                ->with('error', 'Failed to create manual badge: ' . $e->getMessage());
        }
    }

    /**
     * Manuel badge için QR kod oluştur
     */
    private function createManualQrCode($user_id, $email)
    {
        try {
            // QR kod klasörünü oluştur
            $qr_dir = public_path('qr');
            if (!file_exists($qr_dir)) {
                mkdir($qr_dir, 0755, true);
            }

            // QR kod dosya yolu
            $qr_file = $qr_dir . '/' . $user_id . '.png';

            // Manuel badge için email kullan (createSingleBadge mantığı ile uyumlu)
            $qr_data = $email;

            // QR kod oluştur (imagick extension kontrolü ile)
            try {
                if (class_exists('\SimpleSoftwareIO\QrCode\Facades\QrCode') && extension_loaded('imagick')) {
                    \SimpleSoftwareIO\QrCode\Facades\QrCode::format('png')
                        ->size(300)
                        ->generate($qr_data, $qr_file);
                } else {
                    // Imagick yoksa basic QR kod oluştur
                    $this->generateBasicQrCode($qr_data, $qr_file);
                }
            } catch (\Exception $e) {
                // QR kod oluşturma başarısız olursa fallback kullan
                \Log::warning('QR code generation failed, using fallback', ['error' => $e->getMessage()]);
                $this->generateBasicQrCode($qr_data, $qr_file);
            }

            \Log::info('Manual QR code created', [
                'user_id' => $user_id,
                'email' => $email,
                'file' => $qr_file
            ]);

        } catch (\Exception $e) {
            \Log::error('Manual QR code creation failed', [
                'user_id' => $user_id,
                'email' => $email,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Basit QR kod oluşturma (fallback)
     */
    private function generateBasicQrCode($data, $file_path)
    {
        // 300x300 QR kod placeholder oluştur
        $size = 300;
        $image = imagecreatetruecolor($size, $size);

        // Renkler
        $white = imagecolorallocate($image, 255, 255, 255);
        $black = imagecolorallocate($image, 0, 0, 0);
        $gray = imagecolorallocate($image, 128, 128, 128);

        // Beyaz arka plan
        imagefill($image, 0, 0, $white);

        // Sınır çiz
        imagerectangle($image, 10, 10, $size-10, $size-10, $black);

        // QR kod benzeri pattern oluştur
        $this->drawQrPattern($image, $black, $white);

        // Orta kısma veri yazısı
        $font_size = 3;
        $text = substr($data, 0, 25);
        $text_width = strlen($text) * imagefontwidth($font_size);
        $text_height = imagefontheight($font_size);
        $x = ($size - $text_width) / 2;
        $y = ($size - $text_height) / 2;

        // Metin arka planı (beyaz kutu)
        imagefilledrectangle($image, $x-5, $y-5, $x+$text_width+5, $y+$text_height+5, $white);
        imagerectangle($image, $x-5, $y-5, $x+$text_width+5, $y+$text_height+5, $black);

        // Metni yaz
        imagestring($image, $font_size, $x, $y, $text, $black);

        // Dosyayı kaydet
        imagepng($image, $file_path);
        imagedestroy($image);
    }

    /**
     * QR kod benzeri pattern çiz
     */
    private function drawQrPattern($image, $black, $white)
    {
        $size = 300;
        $block_size = 10;

        // Rastgele QR kod benzeri pattern
        for ($x = 20; $x < $size - 20; $x += $block_size) {
            for ($y = 20; $y < $size - 20; $y += $block_size) {
                // Orta kısmı boş bırak (metin için)
                if ($x > 100 && $x < 200 && $y > 120 && $y < 180) {
                    continue;
                }

                // Rastgele siyah/beyaz bloklar
                if (rand(0, 1)) {
                    imagefilledrectangle($image, $x, $y, $x + $block_size - 2, $y + $block_size - 2, $black);
                }
            }
        }

        // Köşe marker'ları çiz
        $this->drawCornerMarkers($image, $black, $white);
    }

    /**
     * QR kod köşe marker'larını çiz
     */
    private function drawCornerMarkers($image, $black, $white)
    {
        $marker_size = 50;
        $positions = [
            [20, 20],           // Sol üst
            [230, 20],          // Sağ üst
            [20, 230]           // Sol alt
        ];

        foreach ($positions as $pos) {
            $x = $pos[0];
            $y = $pos[1];

            // Dış kare
            imagefilledrectangle($image, $x, $y, $x + $marker_size, $y + $marker_size, $black);
            // İç beyaz kare
            imagefilledrectangle($image, $x + 10, $y + 10, $x + $marker_size - 10, $y + $marker_size - 10, $white);
            // Orta siyah kare
            imagefilledrectangle($image, $x + 20, $y + 20, $x + $marker_size - 20, $y + $marker_size - 20, $black);
        }
    }
}
