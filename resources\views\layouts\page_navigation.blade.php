<div class="container mt-4">
    <!-- Navigation Menu -->
    <ul class="nav nav-pills mb-3">
        <li class="nav-item">
            <a class="nav-link page_navigation_nav_link {{ request()->is('admin/event-detail*') ? 'active' : '' }}"
                href="{{ route('panel.event_detail', ['event_id' => $event->id]) }}">Event Overview</a>
        </li>
        @if ($event->status != 2)
            <li class="nav-item">
                <a class="nav-link page_navigation_nav_link {{ request()->is('*/register-list') ? 'active' : '' }}"
                    href="{{ url('admin/event/' . $event->id . '/register-list') }}">Registration List</a>
            </li>
            @if (isset($event) && $event->attendees_info)
                <li class="nav-item">
                    <a class="nav-link page_navigation_nav_link {{ request()->is('*/attendees-info') ? 'active' : '' }}"
                        href="{{ route('panel.attendees_info', ['event_id' => $event->id]) }}">Attendees Info</a>
                </li>
            @endif
        @endif
        @if (isset($event) && $event->user_check_in && $event->status != 2)
            <li class="nav-item">
                <a class="nav-link page_navigation_nav_link {{ request()->is('*/checkin') ? 'active' : '' }}"
                    href="{{ route('panel.event_manuelcheckin__or__qrcheckin', ['event_id' => $event->id]) }}">
                    Event Check-in
                </a>
            </li>
        @endif
        @if ($event->status != 2)
        <li class="nav-item">
            <a class="nav-link @if(!$event->badge_creation) passive @endif page_navigation_nav_link {{ request()->is('*/badge-creation', '*/badge-creation/*') ? 'active' : '' }}"
                href="@if($event->badge_creation){{ route('panel.badge_creation', ['event_id' => $event->id]) . '?design=template' }}@else#@endif">Design Your Badge</a>
        </li>
        <li class="nav-item">
            <a class="nav-link @if(!$event->badge_creation) passive @endif page_navigation_nav_link {{ request()->is('*/badge-creation-manual') ? 'active' : '' }}"
                href=" @if($event->badge_creation){{ route('panel.badge_creation_manual', ['event_id' => $event->id]) }}@else#@endif">
                Manual Badge
            </a>
        </li>
        @endif
        @if ($event->status != 2)
            <li class="nav-item">
                <a class="nav-link page_navigation_nav_link {{ request()->is('*/badge-list') ? 'active' : '' }}"
                    href="{{ route('panel.badge_list', ['event_id' => $event->id]) }}">Badge List</a>
            </li>
            <li class="nav-item">
                <a class="nav-link passive page_navigation_nav_link" href="#">Interactive Tools</a>
            </li>
            <li class="nav-item">
                <a class="nav-link passive page_navigation_nav_link" href="#">Feedback Survey</a>
            </li>
        @endif
    </ul>
