const dropdownSearchBlock = document.querySelector(".dropdown__search__block");
dropdownSearchBlock.style.display = "flex";

document.querySelectorAll(".checkin").forEach((item) => {
    console.log("checkin");
    item.addEventListener("click", () => {
        // Tüm öğelerden active sınıfını kaldır
        document.querySelectorAll(".checkin").forEach((el) => {
            el.classList.remove("active");
        });

        // Tıklanan öğeye active sınıfını ekle
        item.classList.add("active");

        // Manuel ve QR içeriklerini göster/gizle
        const manualCheckin = document.getElementById("manualCheckin");
        const qrCheckin = document.getElementById("qrCheckin");

        if (item.classList.contains("manualcheckin")) {
            manualCheckin.style.display = "block";
            qrCheckin.style.display = "none";
            dropdownSearchBlock.style.display = "flex";
        } else if (item.classList.contains("qrcheckin")) {
            manualCheckin.style.display = "none";
            qrCheckin.style.display = "block";
            dropdownSearchBlock.style.display = "none";
        }
    });
});
