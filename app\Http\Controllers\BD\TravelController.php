<?php

namespace App\Http\Controllers\BD;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;

class TravelController extends Controller
{
    /**
     * Show travel documents page
     */
    public function index()
    {
        $user = Session::get('bd_user');
        // Mock events data
        $events = collect([
            (object) [
                'id' => 1,
                'title' => 'APAC Hospital at Home Symposium',
                'formatted_date' => 'TODAY | 12:00PM-04:00PM',
                'location' => 'Auditorium, National University...',
                'image' => asset('assets/bd/images/event.png')
            ],
            (object) [
                'id' => 2,
                'title' => 'BD Innovation Summit 2024',
                'formatted_date' => 'TOMORROW | 09:00AM-05:00PM',
                'location' => 'Convention Center, Singapore',
                'image' => asset('assets/bd/images/event.png')
            ],
            (object) [
                'id' => 3,
                'title' => 'Healthcare Technology Forum',
                'formatted_date' => 'NEXT WEEK | 10:00AM-03:00PM',
                'location' => 'Tech Hub, Malaysia',
                'image' => asset('assets/bd/images/event.png')
            ],
            (object) [
                'id' => 4,
                'title' => 'Medical Innovation Conference',
                'formatted_date' => 'NEXT MONTH | 08:00AM-06:00PM',
                'location' => 'Medical Center, Tokyo',
                'image' => asset('assets/bd/images/event.png')
            ]
        ]);
        
        // Mock selected event (first event by default)
        $selectedEvent = $events->first();
        
        // Mock travel documents data
        $travelDocs = (object) [
            'flight_info' => null, // Will show "Add" button
            'hotel_info' => 'Marriott Hotel, Downtown - Confirmation #12345',
            'passport_info' => 'Valid until 2026-12-31'
        ];

        return view('bd.travel-documents', compact('user', 'events', 'selectedEvent', 'travelDocs'));
    }
}
