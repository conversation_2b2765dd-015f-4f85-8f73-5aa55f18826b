<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // Badge generation for all completed events - runs every hour
        $schedule->command('badges:generate-all --all')
                 ->hourly()
                 ->withoutOverlapping()
                 ->runInBackground();

        // Alternative: Run daily at 2 AM
        // $schedule->command('badges:generate-all --all')->dailyAt('02:00');

        // Alternative: Run every 30 minutes
        // $schedule->command('badges:generate-all --all')->everyThirtyMinutes();
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
