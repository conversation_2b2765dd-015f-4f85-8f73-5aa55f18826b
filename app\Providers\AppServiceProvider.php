<?php

namespace App\Providers;

use Illuminate\Support\Facades\URL;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\View;
use App\Models\Event;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        /*view()->composer('*', function ($view)
        {
            $events = Event::all();
            View::share('events', $events);
        });*/

        if (env('APP_FORCE_HTTPS', false)) {
            \URL::forceScheme('https');
        }
    }
}
