@extends('layouts.app')

@section('content')
    <section>
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex flex-column flex-lg-row justify-content-between mb-2 align-items-center">
                            <!-- Sol kısım: Başlık -->
                            <p class="mb-2 mb-md-0 event_title fw-bolder text-center text-md-start">Event List</p>

                            <!-- Sağ kısım: Filtre ve Arama Alanı -->
                            <div class="home-filter-and-search-section w-sm-100 w-auto">
                                <div class="d-flex flex-column flex-sm-row gap-2 align-items-stretch align-items-sm-center">

                                    <!-- Sıralama Dropdownu -->
                                    <div class="dropdown">
                                        <button class="btn btn-light w-sm-auto" type="button" data-bs-toggle="dropdown"
                                            aria-expanded="false"
                                            style="min-width: 85px;border: 1px solid rgba(4, 88, 156, 1);">
                                            Sort By
                                        </button>
                                        <ul class="dropdown-menu p-2" style="min-width: 85px;">
                                            <li>
                                                <button
                                                    class="dropdown-item {{ request('sort_by') == 'date_asc' ? 'active' : '' }}"
                                                    onclick="applySort('date_asc')">Date (Oldest to Newest)</button>
                                            </li>
                                            <li>
                                                <button
                                                    class="dropdown-item {{ request('sort_by') == 'date_desc' ? 'active' : '' }}"
                                                    onclick="applySort('date_desc')">Date (Newest to Oldest)</button>
                                            </li>
                                            <li>
                                                <button
                                                    class="dropdown-item {{ request('sort_by') == 'az' ? 'active' : '' }}"
                                                    onclick="applySort('az')">Alphabetical (A–Z)</button>
                                            </li>
                                            <li>
                                                <button
                                                    class="dropdown-item {{ request('sort_by') == 'za' ? 'active' : '' }}"
                                                    onclick="applySort('za')">Alphabetical (Z–A)</button>
                                            </li>
                                            <li>
                                                <button
                                                    class="dropdown-item {{ request('sort_by') == 'latest' ? 'active' : '' }}"
                                                    onclick="applySort('latest')">Latest Added</button>
                                            </li>
                                            <li>
                                                <button
                                                    class="dropdown-item {{ request('sort_by') == 'two_weeks' ? 'active' : '' }}"
                                                    onclick="applySort('two_weeks')">Events in 2 Weeks</button>
                                            </li>
                                        </ul>
                                    </div>

                                    <!-- Sort By Form (gizli) -->
                                    <form id="sortForm" method="GET" action="{{ url('/admin') }}">
                                        <input type="hidden" name="sort_by" id="sort_by_input"
                                            value="{{ request('sort_by') }}">
                                        <!-- Eğer arama filtresi korunacaksa -->
                                        <input type="hidden" name="search" value="{{ request('search') }}">
                                    </form>


                                    <!-- Tek Dropdown (Country + Business Unit) -->
                                    <form id="filterForm" method="POST" action="{{ route('panel.home.filter') }}"
                                        style="display: contents;">
                                        @csrf
                                        <div class="dropdown multi-filter-dropdown position-relative">
                                            <button class="btn btn-light w-sm-auto" type="button" data-bs-toggle="dropdown"
                                                aria-expanded="false" style="border: 1px solid rgba(4, 88, 156, 1);">
                                                Filters
                                            </button>

                                            <div class="dropdown-menu p-3"
                                                style="max-height: 300px; overflow-y: auto; min-width: 250px;">

                                                <div class="accordion" id="filterAccordion">

                                                    <!-- Country Section -->
                                                    <div class="accordion-item">
                                                        <h2 class="accordion-header" id="headingCountry">
                                                            <button class="accordion-button collapsed p-2" type="button"
                                                                data-bs-toggle="collapse" data-bs-target="#collapseCountry"
                                                                aria-expanded="false" aria-controls="collapseCountry">
                                                                Country
                                                            </button>
                                                        </h2>
                                                        <div id="collapseCountry" class="accordion-collapse collapse"
                                                            aria-labelledby="headingCountry"
                                                            data-bs-parent="#filterAccordion">
                                                            <div class="accordion-body p-2">
                                                                @php $printedCountries = []; @endphp
                                                                @if ($countries)
                                                                    @foreach ($countries as $country)
                                                                        @php
                                                                            if (
                                                                                in_array(
                                                                                    $country->country_name,
                                                                                    $printedCountries,
                                                                                )
                                                                            ) {
                                                                                continue;
                                                                            }
                                                                            $printedCountries[] =
                                                                                $country->country_name;
                                                                        @endphp
                                                                        <div class="form-check">
                                                                            <input class="form-check-input" type="checkbox"
                                                                                name="event_location[]"
                                                                                value="{{ $country->country_name }}"
                                                                                id="country_{{ $loop->index }}"
                                                                                {{ is_array(request('event_location')) && in_array($country->country_name, request('event_location')) ? 'checked' : '' }}>
                                                                            <label class="form-check-label"
                                                                                for="country_{{ $loop->index }}">
                                                                                {{ $country->country_name }}
                                                                            </label>
                                                                        </div>
                                                                    @endforeach
                                                                @endif
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <!-- Business Unit Section -->
                                                    <div class="accordion-item">
                                                        <h2 class="accordion-header" id="headingBU">
                                                            <button class="accordion-button collapsed p-2" type="button"
                                                                data-bs-toggle="collapse" data-bs-target="#collapseBU"
                                                                aria-expanded="false" aria-controls="collapseBU">
                                                                Business Unit
                                                            </button>
                                                        </h2>
                                                        <div id="collapseBU" class="accordion-collapse collapse"
                                                            aria-labelledby="headingBU" data-bs-parent="#filterAccordion">
                                                            <div class="accordion-body p-2">
                                                                @if ($business_units)
                                                                    @foreach ($business_units as $unit)
                                                                        <div class="form-check">
                                                                            <input class="form-check-input" type="checkbox"
                                                                                name="business_unit[]"
                                                                                value="{{ $unit->name }}"
                                                                                id="unit_{{ $loop->index }}"
                                                                                {{ is_array(request('business_unit')) && in_array($unit->name, request('business_unit')) ? 'checked' : '' }}>
                                                                            <label class="form-check-label"
                                                                                for="unit_{{ $loop->index }}">
                                                                                {{ $unit->name }}
                                                                            </label>
                                                                        </div>
                                                                    @endforeach
                                                                @endif
                                                            </div>
                                                        </div>
                                                    </div>

                                                </div>

                                                <!-- Listele ve Clear Filters Butonları -->
                                                <div class="mt-3 text-end d-flex justify-content-between gap-2">
                                                    <a href="{{ route('panel.home') }}"
                                                        class="btn btn-outline-secondary fw-bold">Clear Filters</a>
                                                    <button type="submit" class="btn btn-primary">List</button>
                                                </div>
                                            </div>

                                        </div>
                                    </form>

                                    <!-- Search Box -->
                                    <form id="search-form" action="{{ url('/admin') }}" method="GET"
                                        class="position-relative w-sm-auto">
                                        <div class="search-box position-relative w-sm-auto">
                                            <input class="search-input form-control" type="text" name="search"
                                                value="{{ request('search') ? request('search') : '' }}"
                                                placeholder="Search..">
                                            <span class="position-absolute" style="right: 12px; top: 7px;">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                    fill="currentColor" class="bi bi-search" viewBox="0 0 16 16">
                                                    <path
                                                        d="M11.742 10.344a6.5 6.5 0 1 0-1.397 1.398h-.001q.044.06.098.115l3.85 3.85a1 1 0 0 0 1.415-1.414l-3.85-3.85a1 1 0 0 0-.115-.1zM12 6.5a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0" />
                                                </svg>
                                            </span>
                                        </div>
                                    </form>
                                </div>
                            </div>


                        </div>
                        <div>
                            <!-- Nav tabs -->
                            <ul class="event_list_nav gap-4 nav nav-tabs mb-3" role="tablist">
                                <li class="nav-item">
                                    <a class="nav-link active" data-bs-toggle="tab" href="#home" role="tab">
                                        <span>All</span>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" data-bs-toggle="tab" href="#profile" role="tab">
                                        <span>Active Events</span>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" data-bs-toggle="tab" href="#messages" role="tab">
                                        <span>Past Events</span>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" data-bs-toggle="tab" href="#draft" role="tab">
                                        <span>Draft Events</span>
                                    </a>
                                </li>
                            </ul>
                            <!-- Tab panes -->
                            <div class="tab-content">
                                <div class="tab-pane active" id="home" role="tabpanel">

                                    <div class="row">

                                        @if ($events && $events->count() > 0)
                                            @foreach ($events as $event)
                                                <div class="col-lg-4 col-md-6">
                                                    <div class="card card-item">
                                                        @if ($event->status == 2)
                                                            <p class="past-badge">Draft</p>
                                                        @else
                                                            @if (
                                                                $event->event_dates()->orderBy('event_date', 'ASC')->first() &&
                                                                    $event->event_dates()->orderBy('event_date', 'ASC')->first()->event_date >= date('Y-m-d'))
                                                                <p class="active-badge">Active</p>
                                                            @else
                                                                <p class="past-badge">Past</p>
                                                            @endif
                                                        @endif

                                                        <div class="dropdown manage">
                                                            <button class="btn dropdown-toggle" type="button"
                                                                id="manageDropdown" data-bs-toggle="dropdown"
                                                                aria-expanded="false">
                                                                <img src="{{ asset('images/settings.png') }}" /> Manage
                                                            </button>
                                                            <ul class="dropdown-menu" aria-labelledby="manageDropdown">
                                                                <li><a class="dropdown-item"
                                                                        href="{{ route('panel.event_detail', $event->id) }}">Edit
                                                                        Event</a></li>
                                                                <li><a class="dropdown-item clone-event"
                                                                        style="cursor:pointer;"
                                                                        onclick="cloneEventSwal('{{ $event->id }}')">Clone
                                                                        Event</a></li>
                                                            </ul>
                                                        </div>

                                                        <a href="{{ route('panel.event_detail', $event->id) }}">
                                                            <img class="card-img-top img-responsive"
                                                                style="height:200px; object-fit: contain;"
                                                                src="@if ($event->event_banner) {{ asset(env('STORAGE_PATH') . '/event_banners/' . $event->event_banner) }}@else{{ asset('images/default_bg_white.png') }} @endif"
                                                                alt="">
                                                            <div class="card-body card card-info">
                                                                <p class="card-text">{{ $event->event_title }}</p>
                                                                @if ($event->event_dates)
                                                                    @foreach ($event->event_dates as $item)
                                                                        <span class="d-flex flex-row gap-1">
                                                                            <p class="card-location">
                                                                                <img class=""
                                                                                    src="{{ asset('images/location.png') }}"
                                                                                    alt="icon">
                                                                                {{ $item->event_location }}
                                                                            </p>
                                                                        </span>
                                                                        <p class="card-time">
                                                                            {{ \Carbon\Carbon::parse($item->event_date)->format('d/m/Y') }}
                                                                            |
                                                                            {{ $item->event_start_time }} -
                                                                            {{ $item->event_end_time }}</p>
                                                                    @endforeach
                                                                @endif

                                                            </div>
                                                        </a>
                                                    </div>
                                                </div>
                                            @endforeach
                                        @else
                                            <div class="col-12">
                                                <p>No events found.</p>
                                            </div>
                                        @endif


                                    </div>

                                </div>
                                <div class="tab-pane" id="profile" role="tabpanel">

                                    <div class="row">

                                        @if ($events)
                                            @foreach ($events as $event)
                                                @if (
                                                    $event->event_dates()->orderBy('event_date', 'ASC')->first() &&
                                                        $event->event_dates()->orderBy('event_date', 'ASC')->first()->event_date >= date('Y-m-d'))
                                                    <div class="col-lg-4 col-md-6">
                                                        <div class="card card-item">

                                                            <p class="active-badge">Active</p>

                                                            <div class="dropdown manage">
                                                                <button class="btn dropdown-toggle" type="button"
                                                                    id="manageDropdown" data-bs-toggle="dropdown"
                                                                    aria-expanded="false">
                                                                    <img src="{{ asset('images/settings.png') }}" />
                                                                    Manage
                                                                </button>
                                                                <ul class="dropdown-menu"
                                                                    aria-labelledby="manageDropdown">
                                                                    <li><a class="dropdown-item"
                                                                            href="{{ route('panel.event_detail', $event->id) }}">Edit
                                                                            Event</a></li>
                                                                    <li><a class="dropdown-item clone-event"
                                                                            style="cursor:pointer;"
                                                                            onclick="cloneEventSwal('{{ $event->id }}')">Clone
                                                                            Event</a></li>
                                                                </ul>
                                                            </div>

                                                            <a href="{{ route('panel.event_detail', $event->id) }}">
                                                                <img class="card-img-top img-responsive"
                                                                    style="height:200px; object-fit: contain;"
                                                                    src="@if ($event->event_banner) {{ asset(env('STORAGE_PATH') . '/event_banners/' . $event->event_banner) }}@else{{ asset('images/default_bg_white.png') }} @endif"
                                                                    alt="">
                                                                <div class="card-body card card-info">
                                                                    <p class="card-text">{{ $event->event_title }}</p>
                                                                    @if ($event->event_dates)
                                                                        @foreach ($event->event_dates as $item)
                                                                            <span class="d-flex flex-row gap-1">
                                                                                <p class="card-location">
                                                                                    <img class=""
                                                                                        src="{{ asset('images/location.png') }}"
                                                                                        alt="icon">
                                                                                    {{ $item->event_location }}
                                                                                </p>
                                                                            </span>
                                                                            <p class="card-time">
                                                                                {{ \Carbon\Carbon::parse($item->event_date)->format('d/m/Y') }}
                                                                                |
                                                                                {{ $item->event_start_time }} -
                                                                                {{ $item->event_end_time }}</p>
                                                                        @endforeach
                                                                    @endif

                                                                </div>
                                                            </a>
                                                        </div>
                                                    </div>
                                                @endif
                                            @endforeach
                                        @endif

                                    </div>

                                </div>
                                <div class="tab-pane" id="messages" role="tabpanel">
                                    <div class="row">
                                        @if ($events)
                                            @foreach ($events as $event)
                                                @if (
                                                    $event->event_dates()->orderBy('event_date', 'ASC')->first() &&
                                                        $event->event_dates()->orderBy('event_date', 'ASC')->first()->event_date < date('Y-m-d'))
                                                    <div class="col-lg-4 col-md-6">
                                                        <div class="card card-item">

                                                            <p class="past-badge">Past</p>

                                                            <div class="dropdown manage">
                                                                <button class="btn dropdown-toggle" type="button"
                                                                    id="manageDropdown" data-bs-toggle="dropdown"
                                                                    aria-expanded="false">
                                                                    <img src="{{ asset('images/settings.png') }}" />
                                                                    Manage
                                                                </button>
                                                                <ul class="dropdown-menu"
                                                                    aria-labelledby="manageDropdown">
                                                                    <li><a class="dropdown-item"
                                                                            href="{{ route('panel.event_detail', $event->id) }}">Edit
                                                                            Event</a></li>
                                                                    <li><a class="dropdown-item clone-event"
                                                                            style="cursor:pointer;"
                                                                            onclick="cloneEventSwal('{{ $event->id }}')">Clone
                                                                            Event</a></li>
                                                                </ul>
                                                            </div>

                                                            <a href="{{ route('panel.event_detail', $event->id) }}">
                                                                <img class="card-img-top img-responsive"
                                                                    style="height:200px; object-fit: contain;"
                                                                    src="@if ($event->event_banner) {{ asset(env('STORAGE_PATH') . '/event_banners/' . $event->event_banner) }}@else{{ asset('images/default_bg_white.png') }} @endif"
                                                                    alt="">
                                                                <div class="card-body card card-info">
                                                                    <p class="card-text">{{ $event->event_title }}</p>
                                                                    @if ($event->event_dates)
                                                                        @foreach ($event->event_dates as $item)
                                                                            <span class="d-flex flex-row gap-1">
                                                                                <p class="card-location">
                                                                                    <img class=""
                                                                                        src="{{ asset('images/location.png') }}"
                                                                                        alt="icon">
                                                                                    {{ $item->event_location }}
                                                                                </p>
                                                                            </span>
                                                                            <p class="card-time">
                                                                                {{ \Carbon\Carbon::parse($item->event_date)->format('d/m/Y') }}
                                                                                |
                                                                                {{ $item->event_start_time }} -
                                                                                {{ $item->event_end_time }}</p>
                                                                        @endforeach
                                                                    @endif

                                                                </div>
                                                            </a>
                                                        </div>
                                                    </div>
                                                @endif
                                            @endforeach
                                        @endif
                                    </div>
                                </div>
                                <div class="tab-pane" id="draft" role="tabpanel">
                                    <div class="row">
                                        @if ($events)
                                            @foreach ($events as $event)
                                                @if ($event->status == 2)
                                                    <div class="col-lg-4 col-md-6">
                                                        <div class="card card-item">

                                                            <p class="past-badge">Draft</p>

                                                            <div class="dropdown manage">
                                                                <button class="btn dropdown-toggle" type="button"
                                                                    id="manageDropdown" data-bs-toggle="dropdown"
                                                                    aria-expanded="false">
                                                                    <img src="{{ asset('images/settings.png') }}" />
                                                                    Manage
                                                                </button>
                                                                <ul class="dropdown-menu"
                                                                    aria-labelledby="manageDropdown">
                                                                    <li><a class="dropdown-item"
                                                                            href="{{ route('panel.event_detail', $event->id) }}">Edit
                                                                            Event</a></li>
                                                                    <li><a class="dropdown-item clone-event"
                                                                            style="cursor:pointer;"
                                                                            onclick="cloneEventSwal('{{ $event->id }}')">Clone
                                                                            Event</a></li>
                                                                </ul>
                                                            </div>

                                                            <a href="{{ route('panel.event_detail', $event->id) }}">
                                                                <img class="card-img-top img-responsive"
                                                                    style="height:200px; object-fit: contain;"
                                                                    src="@if ($event->event_banner) {{ asset(env('STORAGE_PATH') . '/event_banners/' . $event->event_banner) }}@else{{ asset('images/default_bg_white.png') }} @endif"
                                                                    alt="">
                                                                <div class="card-body card card-info">
                                                                    <p class="card-text">{{ $event->event_title }}</p>
                                                                    @if ($event->event_dates)
                                                                        @foreach ($event->event_dates as $item)
                                                                            <span class="d-flex flex-row gap-1">
                                                                                <p class="card-location">
                                                                                    <img class=""
                                                                                        src="{{ asset('images/location.png') }}"
                                                                                        alt="icon">
                                                                                    {{ $item->event_location }}
                                                                                </p>
                                                                            </span>
                                                                            <p class="card-time">
                                                                                {{ \Carbon\Carbon::parse($item->event_date)->format('d/m/Y') }}
                                                                                |
                                                                                {{ $item->event_start_time }} -
                                                                                {{ $item->event_end_time }}</p>
                                                                        @endforeach
                                                                    @endif

                                                                </div>
                                                            </a>
                                                        </div>
                                                    </div>
                                                @endif
                                            @endforeach
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        </div>
    </section>
@endsection

<script>
    var events = @json($events);



    function submitSearchForm() {
        document.getElementById('search-form').submit();
    }

    document.addEventListener("DOMContentLoaded", function() {
        document.getElementById("filterForm").addEventListener("submit", function(event) {
            var eventLocation = document.getElementById("event_location").value;
            var businessUnit = document.getElementById("business_unit").value;

            // Eğer ikisi de boşsa form gönderimini engelle
            if (!eventLocation && !businessUnit) {
                alert("Please select at least one option.");
                event.preventDefault();
            }
        });
    });

    // Event Clone
    function cloneEventSwal(eventId) {
        Swal.fire({
            title: 'Are you sure?',
            text: 'Do you want to clone this event?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes, clone it!'
        }).then((result) => {
            if (result.isConfirmed) {
                window.location.href = '{{ route('panel.new_event') }}?clone=' + eventId;

            }
        });
    }
</script>
<script>
    function applySort(value) {
        document.getElementById('sort_by_input').value = value;
        document.getElementById('sortForm').submit();
    }
</script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Tüm accordion buttonlarını seç
        const accordionButtons = document.querySelectorAll('.dropdown-menu .accordion-button');

        accordionButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.stopPropagation(); // Dropdown'un kapanmasını engeller
            });
        });

        // Accordion içindeki checkbox'lar da dropdown'u kapatmasın
        const checks = document.querySelectorAll('.dropdown-menu input[type="checkbox"]');
        checks.forEach(check => {
            check.addEventListener('click', function(e) {
                e.stopPropagation();
            });
        });

        // Accordion içindeki label'lar da dropdown'u kapatmasın
        const labels = document.querySelectorAll('.dropdown-menu .form-check');
        labels.forEach(label => {
            label.addEventListener('click', function(e) {
                e.stopPropagation();
            });
        });
    });
</script>
