<!DOCTYPE html>
<html>
<head>
    <title>QR Code Preview</title>
    <link rel="shortcut icon" type="image/png" href="{{ asset('images/favicon.png') }}" />
    <style>
        body {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
        }
        img {
            max-width: 100%;
            height: auto;
        }
        button {
            margin-top: 20px;
            padding: 10px 20px;
            font-size: 16px;
        }
        .content-div {
            text-align: center;
        }
        .name_area h3 {
            font-size: 32px;
            font-family: monospace;
        }
        .mt-4rem {
            margin-top: 4rem;
        }
        .mb-1rem {
            margin-bottom: 1rem;
        }

        @media (max-width: 1024px) {
            .content-div {
                scale: 1.5;
            }
        }

    </style>
</head>
<body>
<div class="content-div">
    <div class="name_area mb-1rem">
        <h3>{{ $register->first_name }} {{ $register->last_name }}</h3>
    </div>

    {{--<img src="data:image/png;base64, {!! $base64 !!}" width="250">--}}
    <img src="{{ url('qr/'.$register->user_id.'.png') }}?v={{ time() }}" width="250">

    <div class="mt-4rem">
        <button id="downloadButton">Download QR</button>
        <button id="copyButton">Copy URL</button>
    </div>
</div>

<script>
    document.getElementById('copyButton').addEventListener('click', function() {
        const currentUrl = window.location.href;
        navigator.clipboard.writeText(currentUrl).then(() => {
            alert('URL Copied');
        });
    });
</script>

{{-- 50px margin ile beyaz bg verildi --}}
<script>
    document.getElementById('downloadButton').addEventListener('click', function() {
        const base64QRCode = document.querySelector('img').src;
        const fileName = 'qr_code_{{ \Illuminate\Support\Str::slug($register->first_name . '_' . $register->last_name, '_') }}.png';
        downloadQRCode(base64QRCode, fileName);
    });

    function downloadQRCode(base64QRCode, fileName) {
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        const img = new Image();

        img.onload = function() {
            const margin = 50;
            canvas.width = img.width + margin * 2;
            canvas.height = img.height + margin * 2;

            context.fillStyle = '#FFFFFF';
            context.fillRect(0, 0, canvas.width, canvas.height);

            context.drawImage(img, margin, margin);

            const link = document.createElement('a');
            link.href = canvas.toDataURL('image/png');
            link.download = fileName;
            link.click();
        };

        img.src = base64QRCode;
    }
</script>

</body>
</html>
