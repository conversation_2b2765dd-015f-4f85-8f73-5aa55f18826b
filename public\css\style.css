:root {
    --orange: #f26925;
    --blue: #0A599A;
    --light-gray: #5b6b84;
    --green: #26A43A;
    --red: #CD001E;
}

/*header*/
#main-wrapper[data-layout="vertical"] .app-header.fixed-header .navbar {
    margin-top: 0 !important;
    border-radius: 0 0 15px 15px !important;
}

/*header end*/
section {
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    box-shadow: none;
}

.card {
    margin-bottom: 0 !important;
}

.status.dropdown-toggle::after {
    content: none !important;
}

/* buttons */
#new_date_button,
.add_image_btn,
.done_btn,
#manageDropdown,
.edit_btn,
.sync_marketo_btn,
#complete-shortlist-button,
#saveDraftBtn,
#completeSaveBtn,
.page_navigation_nav_link.active {
    background-color: var(--blue) !important;
    border: 2px solid var(--blue) !important;
}

.delete_btn {
    background-color: var(--red);
    border: 2px solid var(--red) !important;
}

#new_date_button:hover,
.add_image_btn:hover,
.done_btn:hover,
#manageDropdown:hover,
.edit_btn:hover,
.sync_marketo_btn:hover,
#complete-shortlist-button:hover,
#saveDraftBtn:hover,
#completeSaveBtn:hover,
.page_navigation_nav_link.active:hover {
    background-color: #fff !important;
    border: 2px solid var(--blue) !important;
    color: #000 !important;
}

.remove-btn {
    background-color: var(--red);
}


/* home */
.dropdown.country,
.dropdown.business-unit,
.filter-btn {
    border: 1px solid rgba(4, 88, 156, 1);
    border-radius: 8px;
}

.dropdown.country .dropdown-toggle,
.dropdown.business-unit .dropdown-toggle,
.filter-btn,
.search-box input {
    padding: 5px 0;
}

.dropdown.country button,
.dropdown.business-unit button,
.search-box input {
    background-color: #04589c1a !important;
    color: rgba(0, 0, 0, 1) !important;
    font-weight: 400;
    font-size: 16px;
}

@media (min-width: 1267px) and (max-width: 1400px) {
    .dropdown.country {
        width: 230px;
        display: flex;
    }

    .dropdown.country select {
        width: 228px !important;
    }

    .dropdown.country select option {
        width: 228px !important;
    }
}

@media (min-width: 575px) and (max-width: 1266px) {
    .dropdown.country {
        width: 170px;
    }

    .dropdown.country select {
        width: 168px !important;
    }
}

@media (min-width: 575px) and (max-width: 1176px) {

    .filter-btn,
    #event_location,
    #business_unit {
        font-size: 13px;
    }
}

.filter-btn {
    color: rgba(43, 54, 71, 1);
    font-weight: 700 !important;
    background-color: transparent;
    padding: 7px;
}

.search-box input {
    border: 1px solid rgba(4, 88, 156, 1);
    border-radius: 48px;
    padding-left: 12px;
    max-width: 250px;
}

.search-box input::placeholder {
    color: rgba(0, 0, 0, 1);
    font-style: italic;
    font-size: 16px;
    font-weight: 400;
}

.line-input,
.line-subinput {
    width: 100%;
    max-width: 250px;
}

.line-input {
    font-size: 12px;
}

.line-subinput {
    font-size: 12px;
}

.line-input::placeholder {
    color: rgba(43, 54, 71, 1);
    font-weight: 700;
}

.pencil-icon {
    width: 10px;
    height: 10px;
}

.filter-btn {
    width: 100px;
}

.input-group-text {
    background-color: rgba(232, 240, 254, 1);
    border: 1px solid rgba(223, 229, 239, 1);
}

.upload-cont {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    font-size: 16px;
    color: #333;
    transition: background-color 0.3s ease;
}

.upload-cont:hover {
    background-color: #e0e7ff;
}

.upload-cont input[type="file"] {
    display: none;
}

.upload-cont svg {
    margin-right: 10px;
    width: 20px;
    height: 20px;
    fill: #6b7280;
}

@media (min-width: 992px) {

    .dropdown.country .dropdown-toggle,
    .dropdown.business-unit .dropdown-toggle {
        max-width: 150px !important;
    }
}

@media (min-width: 992px) and (max-width: 1210px) {
    .sync-marketo-btn {
        font-size: 12px;
    }
}

@media (min-width: 768px) and (max-width: 1200px) {
    .pencil-icon {
        display: none;
    }
}

@media (max-width: 767px) {
    .filter-btn {
        width: auto;
    }

    .sync-marketo-btn {
        font-size: 12px;
    }
}

/* home card items */
.card-item {
    margin-bottom: 20px !important;
    border: 1px solid rgba(234, 234, 234, 1);
    box-shadow: none !important;
}

.card-info {
    padding: 10px;
    box-shadow: none !important;
}

.card-text {
    color: rgba(4, 88, 156, 1);
    font-weight: 700;
    font-size: 17px;
    min-height: 60px;
    max-width: 300px;
    padding-bottom: 10px;
}

.card-location {
    color: rgba(4, 88, 156, 1);
    font-weight: 500;
    font-size: 15px;
    padding-bottom: 10px;
}

.card-time {
    color: rgba(40, 45, 85, 1);
    font-weight: 400;
    font-size: 16px;
}

.active-badge,
.past-badge {
    position: absolute;
    left: 0;
    top: 15px;
    border: 2px solid #fff;
    border-bottom-right-radius: 8px;
    border-top-right-radius: 8px;
    border-left: 0;
    color: #fff;
    padding: 4px 8px;
}

.active-badge {
    /* background-color: rgba(0, 185, 63, 1); */
    background-color: var(--green);
}

.past-badge {
    /* background-color: rgba(242, 58, 37, 1); */
    background-color: var(--red);
}

.manage {
    position: absolute;
    right: 10px;
    top: 15px;
    /*border: 2px solid #fff;*/
    border-radius: 8px;
    color: #fff;
    /*padding: 4px 12px;*/
    background-color: rgba(4, 88, 156, 1);
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 3px;
}

.manage .dropdown-toggle {
    width: auto;
}

.manage .dropdown-toggle::after {
    display: none;
}

@media (min-width: 992px) {
    .card-text {
        font-size: 15px;
    }

    .card-time {
        font-size: 14px;
    }
}

@media (max-width: 767px) {
    .card-text {
        font-size: 14px;
    }

    .card-time {
        font-size: 13px;
    }
}

@media (min-width: 769px) and (max-width: 1430px) {
    .form-group label {
        font-size: 11px !important;
        height: 30px;
    }
}

/* event inputs/labels */
.form-group label {
    color: rgba(43, 54, 71, 1);
    font-weight: 700;
    font-size: 14px;
    margin-bottom: 5px;
}

.form-control:focus {
    background-color: rgba(232, 240, 254, 1);
    border: 1px solid rgba(223, 229, 239, 1);
}

#moderator_div,
#speakers_div {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
}

#moderator_div>div,
#speakers_div>div {
    flex: 1 1 calc(50% - 16px);
    max-width: 50%;
}

@media (max-width: 768px) {

    #moderator_div>div,
    #speakers_div>div {
        max-width: 50%;
    }
}

@media (max-width: 767px) {
    .form-group label {
        font-size: 12px;
    }
}


@media (min-width: 841px) and (max-width: 1400px) {
    .session-div .dropdown [type="checkbox"] {
        top: 2px;
    }

}

@media (min-width: 768px) and (max-width: 840px) {
    .session-div .dropdown [type="checkbox"] {
        top: 4px;
    }

}

@media (min-width: 768px) and (max-width: 1200px) {
    .date-time-row .input-group {
        flex-wrap: nowrap;
    }

    .date-time-row .date-icon i,
    .date-time-row .timepicker_icon i {
        width: 15px;
        height: 15px;
    }

    .input-group-text {
        padding: 8px;
    }
}

/* left_menu */
.sidebar-nav ul .sidebar-item .first-level .sidebar-item .sidebar-link.active {
    background-color: #04589c1a !important;
    color: rgba(43, 54, 71, 1) !important;
    font-weight: 700 !important;
}

.sidebar-eventlist {
    background-color: var(--blue) !important;
    color: #fff !important;
    font-weight: 700 !important;
}

.sidebar-item {
    padding: 5px 0;
}

/* page_navigation */
.page_navigation_nav .nav-link {
    color: var(--light-gray);
    padding: 0;
    font-size: 14px;
}

.page_navigation_nav .nav-link.active,
.badge_create_save_btn,
.checkin_save_btn,
.scan_qr_btn {
    background-color: var(--blue);
    color: #fff;
    border-radius: 8px;
    border: none;
    padding: 10px;
}

/* event_details */
.line {
    width: 100%;
    height: 1px;
    background-color: #e5e7eb;
}

.form-check-input {
    background-color: var(--red);
    border-color: var(--red);
    /* --bs-form-switch-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e") !important; */
    --bs-form-switch-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='white'/%3e%3cpath d='M-1,-1 L1,1 M-1,1 L1,-1' stroke='%23CD001E' stroke-width='1' stroke-linecap='round'/%3e%3c/svg%3e") !important;
}

.form-check-input:focus {
    border-color: var(--red);
    --bs-form-switch-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='white'/%3e%3cpath d='M-1,-1 L1,1 M-1,1 L1,-1' stroke='%23CD001E' stroke-width='1' stroke-linecap='round'/%3e%3c/svg%3e") !important;
}

.form-check-input:checked {
    background-color: var(--green);
    border-color: var(--green);
    --bs-form-switch-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='white'/%3e%3cpath d='M-1,0 L0,1.5 L2,-1' stroke='%2326A43A' stroke-width='0.7' stroke-linecap='round' fill='none'/%3e%3c/svg%3e") !important;
}

.event_details_container .form-switch,
.event_details_container .form-check {
    padding-left: 0;
}

.dropdown-toggle {
    border: none;
    color: #fff !important;
    width: 200px;
}

.dropdown-toggle:hover {
    color: #fff !important;
}

.dropdown-toggle.active {
    background-color: var(--green);
}

.dropdown-toggle.pass {
    /* background-color: rgba(242, 58, 37, 1); */
    background-color: var(--red);
}

/* home page */
.add_event_btn {
    background-color: var(--blue);
    width: 240px;
    font-size: 15px;
}

.event_title {
    font-size: 21px;
    color: #2b3647;
}

.event_list_nav .nav-link {
    color: #9c9c9c;
    font-size: 16px;
    padding: 0;
}

.event_list_nav .nav-link:hover {
    color: #9c9c9c;
}

.event_list_nav .nav-link.active {
    color: var(--blue);
    font-weight: 700;
    background-color: transparent !important;
}

/* checkin and design text */
.checkin.active,
.template-design.active,
.design.active {
    color: #2b3647;
    font-weight: 700;
}

.checkin,
.template-design,
.design {
    cursor: pointer;
}

/* checkin page */
.table-bordered> :not(caption)>*>* {
    border-width: 0;
}

.table-bordered thead th {
    font-weight: 700;
    color: var(--blue);
}

.body-wrapper>.container-fluid,
.container {
    max-width: none !important;
}

p {
    margin-bottom: 0 !important;
}

/* badge_creation */
.upload-container {
    border: 1px solid #eaeaea;
    background-color: #f7f9fc;
    height: 500px;
    border-radius: 8px;
}

#templateDesign .upload-container {
    border: 1px dotted var(--orange);
    background-color: transparent;
}

#templateDesign .upload_qr_container {
    border: 1px dotted var(--orange);
    border-radius: 8px;
    width: 150px;
    height: 150px;
}

.upload-box {
    border: 1px dotted var(--orange);
    border-radius: 8px;
    padding: 10px;
    width: 160px;
    margin: 0 auto;
    gap: 5px;
}

.upload-icon {
    font-size: 50px;
    color: #5a5a5a;
}

.color-icon {
    width: 13px;
}

.upload-text {
    font-size: 13px;
    color: #5a5a5a;
}

.upload-subtext {
    font-size: 9px;
    color: #5a5a5a;
}

.preview-container {
    background: url("/images/badge-background.png") no-repeat center center;
    background-size: contain !important;
    border: 1px solid var(--blue);
    padding: 20px;
    border-radius: 8px;
    height: 500px;
}

.background-upload-container {
    left: 5px;
    bottom: 5px;
}

.first_name,
.last_name,
.template_first_name,
.template_last_name {
    color: var(--orange);
}

.speaker,
.company_name,
.template_company_name {
    color: var(--blue);
}

.upload_logo_container {
    margin-top: 132px;
    transform: scale(0.8);
}

.template_logo_container {
    margin-top: 92px;
    transform: scale(0.8);
}

.upload_preview_logo_area {
    position: absolute;
    top: 50px;
}

.qr_container {
    border-radius: 8px;
    border: 1px solid var(--blue);
    width: 190px;
    height: 190px;
}

.qr-image {
    width: 125px;
    height: 125px;
}

.w-full-custom {
    width: 100%;
}

.form-control,
.form-select {
    background-color: rgba(232, 240, 254, 1);
    border: 1px solid rgba(223, 229, 239, 1);
}

.form-select {
    --bs-form-select-bg-img: url("/images/input-dropdown-icon.png");
}

.form-container input,
.form-container select {
    margin-bottom: 15px;
}

.preview-container {
    /*background: url("/images/badge/bg_omni.png") no-repeat center center;*/
}

.nav-item .passive {
    color: #b7bfb7;
}

/* clock */
.daterangepicker {
    position: absolute;
    color: inherit;
    /* border-radius: 23px; */
    width: 278px;
    max-width: none;
    padding: 0;
    margin-top: 7px;
    top: 100px;
    left: 20px;
    z-index: 3001;
    display: none;
    font-family: arial;
    font-size: 15px;
    line-height: 1em;
    background-color: #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.daterangepicker:before,
.daterangepicker:after {
    position: absolute;
    display: inline-block;
    border-bottom-color: rgba(0, 0, 0, 0.2);
    /* content: ''; */
}

.daterangepicker:before {
    top: -7px;
    border-right: 7px solid transparent;
    border-left: 7px solid transparent;
    border-bottom: 7px solid #ccc;
}

.daterangepicker:after {
    top: -6px;
    border-right: 6px solid transparent;
    border-bottom: 6px solid #fff;
    border-left: 6px solid transparent;
}

.daterangepicker.opensleft:before {
    right: 9px;
}

.daterangepicker.opensleft:after {
    right: 10px;
}

.daterangepicker.openscenter:before {
    left: 0;
    right: 0;
    width: 0;
    margin-left: auto;
    margin-right: auto;
}

.daterangepicker.openscenter:after {
    left: 0;
    right: 0;
    width: 0;
    margin-left: auto;
    margin-right: auto;
}

.daterangepicker.opensright:before {
    left: 9px;
}

.daterangepicker.opensright:after {
    left: 10px;
}

.daterangepicker.drop-up {
    margin-top: -7px;
}

.daterangepicker.drop-up:before {
    top: initial;
    bottom: -7px;
    border-bottom: initial;
    border-top: 7px solid #ccc;
}

.daterangepicker.drop-up:after {
    top: initial;
    bottom: -6px;
    border-bottom: initial;
    border-top: 6px solid #fff;
}

.daterangepicker.single .daterangepicker .ranges,
.daterangepicker.single .drp-calendar {
    float: none;
}

.daterangepicker.single .drp-selected {
    display: none;
}

.daterangepicker.show-calendar .drp-calendar {
    display: block;
}

.daterangepicker.show-calendar .drp-buttons {
    display: block;
}

.daterangepicker.auto-apply .drp-buttons {
    display: none;
}

.daterangepicker .drp-calendar {
    display: none;
    max-width: 270px;
}

.daterangepicker .drp-calendar.left {
    padding: 8px 0 8px 8px;
}

.daterangepicker .drp-calendar.right {
    padding: 8px;
}

.daterangepicker .drp-calendar.single .calendar-table {
    border: none;
}

.daterangepicker .calendar-table .next span,
.daterangepicker .calendar-table .prev span {
    color: #fff;
    /* border: solid #fff; */
    border: solid #c5c5c5;
    border-width: 0 2px 2px 0;
    border-radius: 0;
    display: inline-block;
    padding: 3px;
}

.daterangepicker .calendar-table .next span {
    transform: rotate(-45deg);
    -webkit-transform: rotate(-45deg);
}

.daterangepicker .calendar-table .prev span {
    transform: rotate(135deg);
    -webkit-transform: rotate(135deg);
}

.daterangepicker .calendar-table th,
.daterangepicker .calendar-table td {
    white-space: nowrap;
    text-align: center;
    vertical-align: middle;
    min-width: 32px;
    width: 32px;
    height: 32px;
    line-height: 24px;
    font-size: 12px;
    border-radius: 50px;
    border: 1px solid transparent;
    white-space: nowrap;
    cursor: pointer;
}

.daterangepicker .calendar-table {
    color: #000;
}

.daterangepicker .calendar-table table {
    width: 100%;
    margin: 0;
    border-spacing: 0;
    border-collapse: collapse;
}

.monthselect,
.yearselect {
    border: 1px solid #d9d6d6;
    border-radius: 4px;
    padding: 2px 0;
}

.daterangepicker td.available:hover

/* ,.daterangepicker th.available:hover  */
    {
    background-color: #eee;
    border-color: transparent;
    color: #000;
}

.daterangepicker td.week,
.daterangepicker th.week {
    font-size: 80%;
    color: #ccc;
}

.daterangepicker td.off,
.daterangepicker td.off.in-range,
.daterangepicker td.off.start-date,
.daterangepicker td.off.end-date {
    /* background-color: #fff; */
    border-color: transparent;
    color: #999;
}

.daterangepicker td.in-range {
    background-color: rgba(4, 88, 156, 1);
    border-color: transparent;
    color: #fff;
    border-radius: 50px;
}

.daterangepicker td.start-date {
    border-radius: 50px;
}

.daterangepicker td.end-date {
    border-radius: 50px;
}

.daterangepicker td.start-date.end-date {
    border-radius: 50px;
}

.daterangepicker td.active,
.daterangepicker td.active:hover {
    background-color: rgba(4, 88, 156, 1);
    border-color: transparent;
    color: #fff;
    border-radius: 50px;
}

.daterangepicker th.month {
    width: auto;
}

.daterangepicker td.disabled,
.daterangepicker option.disabled {
    color: #999;
    cursor: not-allowed;
    text-decoration: line-through;
}

.daterangepicker select.monthselect,
.daterangepicker select.yearselect {
    font-size: 12px;
    padding: 1px;
    height: auto;
    margin: 0;
    cursor: default;
}

.daterangepicker select.monthselect {
    margin-right: 2%;
    width: 56%;
}

.daterangepicker select.yearselect {
    width: 40%;
}

.daterangepicker select.hourselect,
.daterangepicker select.minuteselect,
.daterangepicker select.secondselect,
.daterangepicker select.ampmselect {
    width: 50px;
    margin: 0 auto;
    background: #eee;
    border: 1px solid #eee;
    padding: 2px;
    outline: 0;
    font-size: 12px;
}

.daterangepicker .calendar-time {
    text-align: center;
    margin: 4px auto 0 auto;
    line-height: 30px;
    position: relative;
}

.daterangepicker .calendar-time select.disabled {
    color: #ccc;
    cursor: not-allowed;
}

.daterangepicker .drp-buttons {
    clear: both;
    text-align: right;
    padding: 0 15px 15px 8px;
    display: none;
    line-height: 12px;
    vertical-align: middle;
}

.daterangepicker .drp-selected {
    display: inline-block;
    font-size: 12px;
    padding-right: 8px;
    display: none;
}

.daterangepicker .drp-buttons .btn {
    margin-left: 8px;
    font-size: 12px;
    font-weight: bold;
    padding: 4px 8px;
    border: none;
}

.daterangepicker.show-ranges.single.rtl .drp-calendar.left {
    border-right: 1px solid #ddd;
}

/* .daterangepicker.show-ranges.single.ltr .drp-calendar.left {
    border-left: 1px solid #ddd;
} */

/* .daterangepicker.show-ranges.rtl .drp-calendar.right {
    border-right: 1px solid #ddd;
}

.daterangepicker.show-ranges.ltr .drp-calendar.left {
    border-left: 1px solid #ddd;
} */

.daterangepicker .ranges {
    float: none;
    text-align: left;
    margin: 0;
    display: none;
}

.daterangepicker.show-calendar .ranges {
    margin-top: 8px;
}

.daterangepicker .ranges ul {
    list-style: none;
    margin: 0 auto;
    padding: 0;
    width: 100%;
}

.daterangepicker .ranges li {
    font-size: 12px;
    padding: 8px 12px;
    cursor: pointer;
}

.daterangepicker .ranges li:hover {
    background-color: #eee;
}

.daterangepicker .ranges li.active {
    background-color: #08c;
    color: #fff;
}

@media screen and (max-width: 576px) {
    .btn-light {
        width: 100%;
    }
}

/*  Larger Screen Styling */
@media (min-width: 564px) {
    .daterangepicker {
        width: auto;
    }

    .daterangepicker .ranges ul {
        width: 140px;
    }

    .daterangepicker.single .ranges ul {
        width: 100%;
    }

    .daterangepicker.single .drp-calendar.left {
        clear: none;
    }

    .daterangepicker.single .ranges,
    .daterangepicker.single .drp-calendar {
        float: left;
    }

    .daterangepicker {
        direction: ltr;
        text-align: left;
    }

    .daterangepicker .drp-calendar.left {
        clear: left;
        margin-right: 0;
    }

    .daterangepicker .drp-calendar.left .calendar-table {
        border-right: none;
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
    }

    .daterangepicker .drp-calendar.right {
        margin-left: 0;
    }

    .daterangepicker .drp-calendar.right .calendar-table {
        border-left: none;
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
    }

    .daterangepicker .drp-calendar.left .calendar-table {
        padding-right: 8px;
    }

    .daterangepicker .ranges,
    .daterangepicker .drp-calendar {
        float: left;
    }
}

@media (min-width: 730px) {
    .daterangepicker .ranges {
        width: auto;
    }

    .daterangepicker .ranges {
        float: left;
    }

    .daterangepicker.rtl .ranges {
        float: right;
    }

    .daterangepicker .drp-calendar.left {
        clear: none !important;
    }
}

.hidden_banner_input {
    margin-top: -50px;
    opacity: 0;
    z-index: -1;
    width: 50px;
}

.required_input_thing {
    color: red;
    font-size: 12px;
    margin-left: 3px;
}

.applyBtn {
    background-color: rgba(4, 88, 156, 1) !important;
    color: #fff;
}

.cancelBtn {
    background-color: rgba(248, 248, 248, 1) !important;
    color: rgba(138, 138, 138, 1);
}

.calender-hr {
    display: none;
}

.calender-title {
    color: #fff;
    padding: 20px 8px 20px 15px;
    font-size: 16px;
    font-weight: 400;
    background-color: rgba(4, 88, 156, 1);
    border-radius: 4px 4px 0 0;
}

.timeselect .drp-calendar.left .calendar-table {
    display: none;
}
